# AI组状态转换接口说明

## 接口概述

`/api/v2/knowledgeSpaces/documents/status/update` 接口用于接收AI组传递的文档处理状态更新，支持自动将RAG状态转换为系统内部的ProcessStatus。

## 状态映射规则

### RAG状态码 → ProcessStatus映射

| RAG状态码 | RAG状态名 | 中文描述 | ProcessStatus | ProcessStatus描述 |
|-----------|-----------|----------|---------------|-------------------|
| 0 | UNSTART | 未开始 | 9 | 处理失败 |
| 1 | RUNNING | 运行中 | 5 | 向量化中 |
| 2 | CANCEL | 已取消 | 9 | 处理失败 |
| 3 | DONE | 已完成 | 6 | 向量化完成 |
| 4 | FAIL | 失败 | 9 | 处理失败 |
| 其他 | 未知状态 | 未知 | 9 | 处理失败 |

### 核心转换逻辑

- **成功状态**: 只有 `rag_status = 3 (DONE)` 才会被视为向量化完成
- **运行状态**: `rag_status = 1 (RUNNING)` 对应向量化中
- **失败状态**: 除了1和3之外的所有状态都被视为处理失败

## 接口参数

### 请求参数 (KgBasesDocumentStatusUpdateDTO)

```json
{
    "dataset_id": "dataset123",           // 必填：数据集ID
    "doc_id": "doc456",                   // 必填：文档ID  
    "rag_status": 1,                      // 推荐：RAG状态码，AI组传递
    "process_status": 5,                  // 可选：如果没有rag_status，使用此字段
    "status_message": "向量化处理中..."   // 可选：状态消息
}
```

### 参数优先级

1. **优先使用 `rag_status`**: 如果传递了`rag_status`，系统会自动转换为对应的`process_status`
2. **兜底使用 `process_status`**: 如果没有`rag_status`，直接使用`process_status`
3. **异常处理**: 如果两个字段都为空，接口会返回错误

## 请求示例

### 示例1：AI组传递运行中状态

```bash
curl -X POST http://localhost:8080/api/v2/knowledgeSpaces/documents/status/update \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_id": "dataset_12345",
    "doc_id": "doc_67890", 
    "rag_status": 1,
    "status_message": "文档正在进行向量化处理"
  }'
```

**系统处理结果**:
- `rag_status: 1` → `process_status: 5` (向量化中)
- 日志记录: "RAG状态转换：运行中(1) -> 向量化中(5)"

### 示例2：AI组传递完成状态

```bash
curl -X POST http://localhost:8080/api/v2/knowledgeSpaces/documents/status/update \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_id": "dataset_12345", 
    "doc_id": "doc_67890",
    "rag_status": 3,
    "status_message": "文档向量化处理完成"
  }'
```

**系统处理结果**:
- `rag_status: 3` → `process_status: 6` (向量化完成)
- 日志记录: "RAG状态转换：已完成(3) -> 向量化完成(6)"

### 示例3：AI组传递失败状态

```bash
curl -X POST http://localhost:8080/api/v2/knowledgeSpaces/documents/status/update \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_id": "dataset_12345",
    "doc_id": "doc_67890", 
    "rag_status": 4,
    "status_message": "向量化处理失败，请检查文档格式"
  }'
```

**系统处理结果**:
- `rag_status: 4` → `process_status: 9` (处理失败)
- 日志记录: "RAG状态转换：失败(4) -> 处理失败(9)"

## 响应格式

### 成功响应

```json
{
    "code": 0,
    "message": "状态更新成功", 
    "data": true
}
```

### 失败响应

```json
{
    "code": 1,
    "message": "rag_status和process_status不能都为空",
    "data": false
}
```

## 日志记录

### 状态转换日志

```
INFO - 状态转换：RAG状态转换：运行中(1) -> 向量化中(5)
INFO - 成功更新文档状态，RAG状态转换：运行中(1) -> 向量化中(5)
```

### 消息存储格式

系统会将状态转换信息和用户消息合并存储到`rag_message`字段：

```
RAG状态转换：运行中(1) -> 向量化中(5) | 文档正在进行向量化处理
```

## 技术实现

### 核心类

1. **RagStatusEnum**: RAG状态枚举定义
2. **StatusMappingUtil**: 状态映射工具类
3. **ProcessStatusEnum**: 系统内部状态枚举

### 状态转换方法

```java
public static Integer mapRagStatusToProcessStatus(Integer ragStatus) {
    RagStatusEnum ragStatusEnum = RagStatusEnum.getByCode(ragStatus);
    switch (ragStatusEnum) {
        case RUNNING: return 5;  // 向量化中
        case DONE:    return 6;  // 向量化完成  
        default:      return 9;  // 处理失败
    }
}
```

## 监控与调试

### AI接口调用日志

系统会自动记录所有AI接口调用到`kg_bases_ai_call_log_d`表，包括：

- 调用批次号
- 调用开始/结束时间
- 请求参数（包含rag_status）
- 响应结果
- 成功/失败状态
- 状态转换详情

### 查询调用记录

```sql
SELECT * FROM kg_bases_ai_call_log_d 
WHERE call_type = 'STATUS_UPDATE' 
ORDER BY call_start_time DESC;
```

## 注意事项

1. **向后兼容**: 接口同时支持新的`rag_status`和原有的`process_status`
2. **状态一致性**: 所有非成功状态都会被转换为失败，确保数据一致性
3. **日志追踪**: 每次状态转换都会记录详细日志，便于问题排查
4. **消息累积**: 状态消息会累积存储，保留完整的处理历史

## AI组接入指南

### 推荐做法

1. **统一使用rag_status**: 建议AI组统一使用`rag_status`字段传递状态
2. **详细状态消息**: 在`status_message`中提供详细的处理信息
3. **及时状态更新**: 在处理状态变化时及时调用接口更新

### 状态流程建议

```
开始处理 → rag_status: 1 (RUNNING)
  ↓
处理完成 → rag_status: 3 (DONE)
  或
处理失败 → rag_status: 4 (FAIL)
```

这样的设计确保了系统的稳定性和可追溯性，同时保持了良好的向后兼容性。 