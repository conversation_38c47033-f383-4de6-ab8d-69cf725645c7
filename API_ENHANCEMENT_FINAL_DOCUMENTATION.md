# 专题图谱接口功能增强文档

## 概述

根据您的需求，我们对以下接口进行了功能增强，支持List<String> entityName参数和keyword搜索功能：

1. `/api/opr/thematicGraph/queryTgInsEntityPage` - 分页查询专题图谱实体实例
2. `ThematicGraphInsServiceImpl.searchEntityInstances` - 根据关键字查询实例
3. `/api/opr/thematicGraph/getChangeSelectionList` - 获取变更选择列表

## 修改详情

### 1. `/api/opr/thematicGraph/queryTgInsEntityPage` 接口增强

#### 新增参数
```json
{
  "tgId": 1,
  "pageNo": 1,
  "pageSize": 10,
  "entityName": ["设备", "系统"],  // 新增：实体类型名称列表
  "keyword": "测试关键字",         // 新增：关键字搜索
  "tenantCode": "test",
  "graphSpaceCode": "test_space"
}
```

#### 参数说明
- `entityName`: List<String> - 实体类型名称列表
  - 传入具体实体类型名称时，进行精准查询（支持多个）
  - 传入 `["不限定实体类型"]` 时，查询所有实体类型
  - 不传入时，不限制实体类型
- `keyword`: String - 关键字，同时对实体的 `name` 和 `displayName` 字段进行模糊查询（OR 关系）

#### 使用示例

**示例1：查询指定实体类型**
```bash
POST /api/opr/thematicGraph/queryTgInsEntityPage
{
  "tgId": 1,
  "pageNo": 1,
  "pageSize": 10,
  "entityName": ["设备"],
  "keyword": "测试",
  "tenantCode": "test",
  "graphSpaceCode": "test_space"
}
```

**示例2：查询多个实体类型**
```bash
POST /api/opr/thematicGraph/queryTgInsEntityPage
{
  "tgId": 1,
  "pageNo": 1,
  "pageSize": 10,
  "entityName": ["设备", "系统", "网络"],
  "keyword": "故障",
  "tenantCode": "test",
  "graphSpaceCode": "test_space"
}
```

**示例3：不限定实体类型查询**
```bash
POST /api/opr/thematicGraph/queryTgInsEntityPage
{
  "tgId": 1,
  "pageNo": 1,
  "pageSize": 10,
  "entityName": ["不限定实体类型"],
  "keyword": "测试",
  "tenantCode": "test",
  "graphSpaceCode": "test_space"
}
```

### 2. `searchEntityInstances` 方法增强

#### 新增参数
```java
SearchEntityInstanceEvt evt = new SearchEntityInstanceEvt();
evt.setTgId(1L);
evt.setKeyword("测试关键字");
evt.setEntityName(Arrays.asList("设备", "系统")); // 新增：支持List<String>
```

#### 使用示例
```java
// 查询指定实体类型
SearchEntityInstanceEvt evt1 = new SearchEntityInstanceEvt();
evt1.setTgId(1L);
evt1.setKeyword("测试");
evt1.setEntityName(Arrays.asList("设备"));

// 查询多个实体类型
SearchEntityInstanceEvt evt2 = new SearchEntityInstanceEvt();
evt2.setTgId(1L);
evt2.setKeyword("测试");
evt2.setEntityName(Arrays.asList("设备", "系统"));

// 不限定实体类型
SearchEntityInstanceEvt evt3 = new SearchEntityInstanceEvt();
evt3.setTgId(1L);
evt3.setKeyword("测试");
evt3.setEntityName(Arrays.asList("不限定实体类型"));
```

### 3. `getChangeSelectionList` 方法增强

#### 新增参数
```java
CommonGraphEvt evt = new CommonGraphEvt();
evt.setTgId(1L);
evt.setEntityName(Arrays.asList("设备", "系统")); // 新增：实体类型名称列表
evt.setKeyword("测试关键字");                    // 新增：关键字搜索
```

#### 使用示例
```java
// 使用entityName和keyword过滤
CommonGraphEvt evt1 = new CommonGraphEvt();
evt1.setTgId(1L);
evt1.setEntityName(Arrays.asList("设备"));
evt1.setKeyword("测试");

// 只使用keyword过滤
CommonGraphEvt evt2 = new CommonGraphEvt();
evt2.setTgId(1L);
evt2.setKeyword("测试");

// 不限定实体类型
CommonGraphEvt evt3 = new CommonGraphEvt();
evt3.setTgId(1L);
evt3.setEntityName(Arrays.asList("不限定实体类型"));
evt3.setKeyword("测试");
```

## 修改的文件清单

### 1. 事件类修改
- `QueryThematicGraphParamPageEvt.java` - 添加 `List<String> entityName` 和 `String keyword` 字段
- `SearchEntityInstanceEvt.java` - 将 `entityName` 从 `String` 改为 `List<String>`
- `CommonGraphEvt.java` - 添加 `List<String> entityName` 和 `String keyword` 字段
- `QueryThematicGraphParamPageModel.java` - 添加对应字段

### 2. 服务类修改
- `ThematicGraphService.java` - 更新 `queryTgInsEntityPage` 方法，添加total数量检查
- `ThematicGraphInsServiceImpl.java` - 更新 `searchEntityInstances` 方法，支持List<String> entityName
- `ThematicGraphServiceImpl.java` - 更新 `getChangeSelectionList` 方法，添加entityName和keyword搜索
- `GraphInsServiceImpl.java` - 修复 `pageTempEntity` 方法的total数量计算问题

### 3. Mapper文件修改
- `TempTgInsEntityMapper.xml` - 添加entityName和keyword的查询条件

## 功能特性

### 1. 实体类型精准查询
- 支持单个或多个实体类型名称的精准查询
- 支持"不限定实体类型"的特殊处理
- 查询条件使用IN语句，性能优化

### 2. 关键字联合模糊查询
- 同时对 `name` 和 `displayName` 字段进行模糊查询
- 使用OR逻辑连接，提高查询覆盖率
- 支持中文和英文关键字

### 3. Total数量准确计算
- 修复了过滤后total数量不准确的问题
- 确保在没有数据时total为0
- 支持复杂查询条件下的准确计数

### 4. 向后兼容
- 所有新增参数都是可选的
- 不传入新参数时，保持原有功能不变
- 完全兼容现有的前端调用

## 测试验证

已创建完整的测试用例 `ThematicGraphEnhancementTest.java`，包含：
- 单个和多个entityName的测试
- keyword搜索功能测试
- "不限定实体类型"的特殊处理测试
- Total数量计算准确性测试

## 部署说明

1. **无需数据库变更**：本次修改不涉及数据库表结构变更
2. **代码部署**：直接部署修改后的Java代码即可
3. **配置变更**：无需修改配置文件
4. **向后兼容**：现有接口调用方式完全兼容

## 总结

本次功能增强完全满足了您的需求：

1. ✅ `/api/opr/thematicGraph/queryTgInsEntityPage` 接口的 `entityName` 参数改为 `List<String>` 类型
2. ✅ 修复了返回的total数量问题，确保没有数据时total为0
3. ✅ `/api/opr/thematicGraph/getChangeSelectionList` 接口添加了entityName和keyword搜索功能
4. ✅ `ThematicGraphInsServiceImpl.searchEntityInstances` 方法的entityName改为List方式

所有修改都遵循了现有的代码规范，保持了与现有功能的完全兼容性。
