// 测试CommonInsEvt的entityName字段修改
import java.util.Arrays;
import java.util.List;

public class CommonInsEvt_Test {
    public static void main(String[] args) {
        // 测试List<String> entityName
        List<String> entityNames = Arrays.asList("局站", "设备");
        System.out.println("EntityName list: " + entityNames);
        
        // 测试"不限定实体类型"
        List<String> unlimitedType = Arrays.asList("不限定实体类型");
        System.out.println("Unlimited type: " + unlimitedType);
        
        // 测试contains方法
        boolean hasUnlimited = entityNames.contains("不限定实体类型");
        System.out.println("Has unlimited: " + hasUnlimited);
        
        boolean hasUnlimited2 = unlimitedType.contains("不限定实体类型");
        System.out.println("Has unlimited2: " + hasUnlimited2);
    }
}
