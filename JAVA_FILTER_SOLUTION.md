# Java代码过滤解决方案

## 问题分析

经过多次尝试，XML中的过滤条件始终没有生效，可能的原因：
1. MyBatis参数绑定问题
2. XML条件语法问题
3. 数据类型转换问题
4. 代码部署问题

## 新的解决方案

采用**Java代码过滤**的方式，确保过滤逻辑一定生效：

### 核心思路
1. **查询所有基础数据**（不依赖XML过滤条件）
2. **在Java代码中进行过滤**（entityName + keyword）
3. **手动分页处理**
4. **准确计算total**

### 实现逻辑

```java
public PageInfo<TempTgInsVm> queryTgInsEntityPage(QueryThematicGraphParamPageEvt evt) {
    // 1. 查询所有基础数据
    List<TempTgInsEntity> allList = tempTgInsEntityMapper.queryTgInsEntityPage(model);
    
    // 2. Java代码过滤 - entityName
    List<TempTgInsEntity> filteredList = allList;
    if (CollectionUtils.isNotEmpty(evt.getEntityName()) && !evt.getEntityName().contains("不限定实体类型")) {
        filteredList = allList.stream()
                .filter(item -> evt.getEntityName().contains(item.getEntityName()))
                .collect(Collectors.toList());
    }
    
    // 3. Java代码过滤 - keyword
    if (StringUtils.isNotBlank(evt.getKeyword())) {
        filteredList = filteredList.stream()
                .filter(item -> (item.getName() != null && item.getName().contains(evt.getKeyword())) ||
                               (item.getDisplayName() != null && item.getDisplayName().contains(evt.getKeyword())))
                .collect(Collectors.toList());
    }
    
    // 4. 计算准确的total
    long actualTotal = filteredList.size();
    
    // 5. 手动分页
    int startIndex = (pageNo - 1) * pageSize;
    int endIndex = Math.min(startIndex + pageSize, filteredList.size());
    List<TempTgInsEntity> pagedList = filteredList.subList(startIndex, endIndex);
    
    // 6. 构建返回结果
    pageInfo.setTotal(actualTotal);
    return pageInfo;
}
```

## 优势

1. **确保过滤生效**：不依赖XML条件，Java代码过滤一定生效
2. **准确的total**：total = 过滤后的实际数量
3. **详细日志**：每一步都有日志输出，便于调试
4. **向后兼容**：不影响其他功能

## 性能考虑

### 潜在问题
- 如果数据量很大（如2859条），会全部加载到内存中

### 优化措施
1. **数据量警告**：超过10000条时输出警告日志
2. **后续优化**：可以考虑分批处理或优化SQL查询

### 性能对比
- **原方案**：1次SQL查询 + PageHelper分页（但total不准确）
- **新方案**：1次SQL查询 + Java过滤 + 手动分页（total准确）

## 预期结果

### 测试场景
**入参**:
```json
{
  "tgId": 11640673,
  "entityName": ["局站"],
  "operateType": 1,
  "pageNo": 1,
  "pageSize": 20
}
```

### 预期日志输出
```
queryTgInsEntityPage - 入参entityName: [局站], keyword: null, tgId: 11640673
queryTgInsEntityPage - model entityName: [局站], keyword: null
queryTgInsEntityPage - 查询到的原始数据数量: 2859
queryTgInsEntityPage - entityName过滤后数据数量: 1
queryTgInsEntityPage - 最终结果: total=1, size=1, pageNo=1, pageSize=20
```

### 预期返回结果
```json
{
  "total": 1,        // 不再是2859，而是过滤后的实际数量
  "list": [
    {
      "entityName": "局站",
      "displayName": "马尾区"
    }
  ],
  "size": 1,
  "pages": 1
}
```

## 验证步骤

1. **重新部署代码**
2. **调用接口测试**
3. **检查日志输出**：确认每一步的数据数量
4. **验证返回结果**：total应该等于实际数据条数

## 后续优化方向

如果这个方案工作正常，后续可以考虑：

1. **SQL优化**：找出为什么XML过滤条件不生效，修复根本问题
2. **性能优化**：对于大数据量场景，考虑分批处理或数据库层面优化
3. **缓存机制**：对于频繁查询的数据，考虑添加缓存

## 回滚方案

如果新方案有问题，可以快速回滚：
1. 恢复原来的PageHelper分页逻辑
2. 保留日志输出功能
3. 继续调试XML过滤条件问题
