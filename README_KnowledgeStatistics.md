# 知识库数据统计与趋势分析功能

## 功能概述

本功能提供知识库数据的统计和趋势分析能力，包括：
- 文档大小统计与趋势
- 问答对数量统计与趋势  
- 专业词汇数量统计与趋势

支持按时间区间和地区（福建省各地市区县）进行查询，提供日度统计数据和趋势图表。

## 架构设计

### 1. 数据统计表设计

**表名**: `kg_data_statistics_d`

**主要字段**:
- `statistics_date`: 统计日期
- `region_id`: 区域ID（关联cs_sm_region_d表）
- `region_name`: 区域名称
- `document_total_size`: 文档总大小（字节）
- `document_count`: 文档数量
- `qa_pair_count`: 问答对数量
- `professional_terms_count`: 专业词汇数量

### 2. 定时任务

**任务类**: `KnowledgeDataStatisticsTask`
**执行时间**: 每天凌晨1点
**功能**: 统计截止到前一天的累计数据，以区域为维度记录各项指标（文档大小、问答对数量、专业词汇数量）

### 3. API接口

**基础路径**: `/api/v2/knowledgeBases/statistics`

**主要接口**:
- `POST /trend` - 通用趋势查询接口
- `POST /documentSizeTrend` - 文档大小趋势
- `POST /qaPairCountTrend` - 问答对数量趋势  
- `POST /professionalTermsCountTrend` - 专业词汇数量趋势
- `POST /regions` - 获取区域列表（支持层级查询）
- `GET /regions/fujian` - 获取福建省地市列表

## 配置说明

### 1. 定时任务配置

在 `application.yml` 中添加以下配置：

```yaml
# 知识库数据统计配置
knowledge:
  data:
    statistics:
      task:
        enabled: true  # 是否启用定时统计任务
        cron: "0 0 1 * * ?"  # 每天凌晨1点执行
      cleanup:
        cron: "0 0 2 1 * ?"  # 每月1号凌晨2点清理过期数据

# 区域配置（多租户支持）
region:
  default-region: 1000000  # 福建省ID
  region-schema: diswb  # 区域模式，根据租户配置
```

### 2. 数据库配置

执行建表脚本：`V2.0.0_20241218__create_kg_data_statistics_table.sql`

## API使用示例

### 1. 获取文档大小趋势

```bash
POST /api/v2/knowledgeBases/statistics/documentSizeTrend
Content-Type: application/json

{
  "startDate": "2024-12-01",
  "endDate": "2024-12-31", 
  "regionIds": [1000000, 1001000],  // 可选，不传默认查询福建省全省
  "regionLevel": 1,  // 可选，0-省级 1-地市级 2-区县级
  "statisticsType": 1,  // 1-文档大小趋势
  "aggregateType": 1,   // 1-按日期聚合 2-按区域聚合
  "includeSubRegions": true  // 是否包含子区域数据
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "statisticsTypeName": "文档大小趋势",
    "unit": "字节",
    "trendData": [
      {
        "value": 45,
        "name": "1日",
        "date": "2024-12-01"
      },
      {
        "value": 52,
        "name": "2日", 
        "date": "2024-12-02"
      }
      // ... 更多数据
    ],
    "regions": [
      {
        "regionId": 1001,
        "regionName": "福州市",
        "fullRegionPath": "福建省/福州市"
      }
    ]
  }
}
```

### 2. 获取问答对数量趋势

```bash
POST /api/v2/knowledgeBases/statistics/qaPairCountTrend
Content-Type: application/json

{
  "startDate": "2024-12-01",
  "endDate": "2024-12-31",
  "regionIds": [1001],
  "statisticsType": 2,  // 2-问答对数量趋势
  "aggregateType": 1
}
```

### 3. 获取专业词汇数量趋势

```bash
POST /api/v2/knowledgeBases/statistics/professionalTermsCountTrend
Content-Type: application/json

{
  "startDate": "2024-12-01", 
  "endDate": "2024-12-31",
  "regionIds": [1001],
  "statisticsType": 3,  // 3-专业词汇数量趋势
  "aggregateType": 1
}
```

### 4. 通用趋势查询接口

```bash
POST /api/v2/knowledgeBases/statistics/trend
Content-Type: application/json

{
  "startDate": "2024-12-01",
  "endDate": "2024-12-31",
  "regionIds": [1000000, 1001000],
  "regionLevel": 1,  // 可选，0-省级 1-地市级 2-区县级
  "statisticsType": 1,  // 1-文档大小 2-问答对数量 3-专业词汇数量
  "aggregateType": 1,   // 1-按日期聚合 2-按区域聚合
  "includeSubRegions": true
}
```

### 5. 获取区域列表接口

```bash
# 获取福建省所有地市
GET /api/v2/knowledgeBases/statistics/regions/fujian

# 获取指定层级的区域列表
POST /api/v2/knowledgeBases/statistics/regions?regionLevel=1&parentId=1000000
```

## 数据源说明

### 1. 文档数据
- **来源表**: `kg_knowledge_bases_d`
- **过滤条件**: `bases_type = '1'` AND `is_deleted = '0'`
- **统计字段**: `filesize` (单位: 字节)

### 2. 问答对数据  
- **来源表**: `kg_qa_pair_d`
- **过滤条件**: `is_deleted = '0'`
- **统计方式**: 记录数量统计

### 3. 专业词汇数据
- **来源表**: `kg_professional_terms_d` 
- **过滤条件**: `is_deleted = '0'`
- **统计方式**: 记录数量统计

## 性能优化

### 1. 索引优化
- 日期索引: `idx_kg_data_statistics_date`
- 区域索引: `idx_kg_data_statistics_region`  
- 复合索引: `idx_kg_data_statistics_query`

### 2. 查询优化
- 使用聚合查询统计累计数据，避免逐个区域查询
- 只需3个SQL查询完成所有统计，然后批量写入
- 大幅减少数据库连接数，提升性能

### 3. 数据清理
- 自动清理365天前的历史数据
- 定期维护确保存储空间合理使用

## 监控与维护

### 1. 日志监控
- 定时任务执行日志
- API调用性能日志
- 异常错误日志

### 2. 手动触发
- 支持手动触发统计任务: `POST /api/dataStatisticsTask/executeManual`
- 支持历史数据补充: `POST /api/dataStatisticsTask/executeHistorical`

### 3. 健康检查
- 测试接口: `GET /api/v2/knowledgeBases/statistics/test`

## 注意事项

1. **数据时效性**: 统计数据为截止到T-1日的累计数据，当日数据需等到次日凌晨统计完成后才能查询
2. **统计逻辑**: 统计的是累计的全量数据（count/sum），不是增量数据
3. **区域维度**: 按实际数据的区域维度统计，支持省级、地市级、区县级
4. **多租户支持**: 通过regionSchema配置支持不同租户的区域数据隔离
5. **性能优化**: 使用聚合查询替代逐个区域查询，大幅减少数据库连接
6. **数据一致性**: 每次统计前清理当日已有记录，避免重复统计
7. **批量处理**: 统计结果批量写入，提高写入效率
8. **默认行为**: 
   - 不传regionIds时默认查询福建省全省（ID: 1000000）
   - 默认包含子区域数据（includeSubRegions: true）
   - 前端推荐按地市进行选择，系统自动聚合区县数据

## 扩展计划

1. 支持更多维度的统计（按用户、按分类等）
2. 增加实时统计能力
3. 支持自定义统计周期（周、月统计）
4. 增加数据导出功能
5. 支持统计数据的可视化图表配置 