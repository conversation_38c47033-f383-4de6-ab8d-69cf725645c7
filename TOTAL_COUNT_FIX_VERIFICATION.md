# Total数量修复验证文档

## 修复方案

### 问题分析
原问题：`entityName: ["局站"]` 过滤条件没有在分页查询的total计算中生效，导致返回total=2859，但实际数据只有1条。

### 解决方案
1. **添加专门的count查询方法**
2. **修改Service逻辑**：当有过滤条件时，先执行count查询获取准确的total，再执行分页查询
3. **保持向后兼容**：没有过滤条件时，仍使用原来的PageHelper逻辑

## 修改内容

### 1. Mapper接口添加count方法
**文件**: `TempTgInsEntityMapper.java`
```java
Long queryTgInsEntityPageCount(QueryThematicGraphParamPageModel model);
```

### 2. XML添加count查询
**文件**: `TempTgInsEntityMapper.xml`
```xml
<select id="queryTgInsEntityPageCount" resultType="java.lang.Long">
    select count(*)
    from kg_temp_thematic_graph_entity_d kttged
    join kg_temp_tg_ins_entity_d ins on ins.entity_code = kttged.model_entity_code and kttged.tg_id = ins.tg_id
    join kg_model_entity_c entityc on ins.entity_code = entityc.entity_code
    where 1=1 and ins.deleted = '0' and kttged.is_del = '0'
    and ins.ins_code is not null and ins.entity_code is not null
    <!-- 与查询SQL相同的过滤条件 -->
    <if test="operateType == '1'.toString()">
        and kttged.is_sync = '1'and ins.is_sync = '1' and kttged.tg_id = #{tgId}
    </if>
    <!-- entityName过滤条件 -->
    <if test="entityName != null and entityName.size() > 0">
        <if test="entityName[0] != '不限定实体类型'">
            and entityc.entity_name in
            <foreach collection="entityName" item="name" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
    </if>
    <!-- keyword过滤条件 -->
    <if test="keyword != null and keyword != ''">
        and (ins.name like concat('%', #{keyword}, '%') or ins.display_name like concat('%', #{keyword}, '%'))
    </if>
</select>
```

### 3. Service方法修改
**文件**: `ThematicGraphService.java`
```java
// 如果有entityName或keyword过滤条件，需要手动计算total
boolean hasFilter = (CollectionUtils.isNotEmpty(evt.getEntityName()) && 
                    !evt.getEntityName().contains("不限定实体类型")) ||
                   StringUtils.isNotBlank(evt.getKeyword());

if (hasFilter) {
    // 先查询总数
    actualTotal = tempTgInsEntityMapper.queryTgInsEntityPageCount(model);
    
    // 再进行分页查询
    PageHelper.startPage(model.getPageNo(), model.getPageSize());
    list = tempTgInsEntityMapper.queryTgInsEntityPage(model);
} else {
    // 没有过滤条件，使用原来的分页逻辑
    PageHelper.startPage(model.getPageNo(), model.getPageSize());
    list = tempTgInsEntityMapper.queryTgInsEntityPage(model);
    PageInfo<TempTgInsEntity> page = new PageInfo<>(list);
    actualTotal = page.getTotal();
}
```

## 预期结果

### 测试用例1：有entityName过滤
**入参**:
```json
{
  "tgId": 11640673,
  "entityName": ["局站"],
  "operateType": 1,
  "pageNo": 1,
  "pageSize": 20
}
```

**预期返回**:
```json
{
  "total": 1,  // 应该是实际符合条件的数量，不再是2859
  "list": [
    {
      "entityName": "局站",
      "displayName": "马尾区"
      // ... 其他字段
    }
  ],
  "size": 1
}
```

### 测试用例2：不限定实体类型
**入参**:
```json
{
  "tgId": 11640673,
  "entityName": ["不限定实体类型"],
  "operateType": 1,
  "pageNo": 1,
  "pageSize": 20
}
```

**预期返回**:
```json
{
  "total": 2859,  // 所有数据的总数
  "list": [
    // 所有实体类型的数据
  ]
}
```

### 测试用例3：多个实体类型
**入参**:
```json
{
  "tgId": 11640673,
  "entityName": ["局站", "设备"],
  "operateType": 1,
  "pageNo": 1,
  "pageSize": 20
}
```

**预期返回**:
```json
{
  "total": X,  // 局站+设备的总数量
  "list": [
    // 包含局站和设备的数据
  ]
}
```

## 验证步骤

1. **重新部署代码**
2. **测试原问题场景**：使用 `entityName: ["局站"]` 调用接口
3. **检查日志输出**：确认参数传递和查询逻辑
4. **验证total数量**：应该与实际返回的数据数量一致
5. **测试其他场景**：不限定实体类型、多个实体类型等

## 性能考虑

- **优化点**：使用专门的count查询，避免查询不必要的字段
- **性能影响**：有过滤条件时会执行两次查询（count + 分页），但确保了数据准确性
- **建议**：如果数据量很大，可以考虑在相关字段上添加数据库索引

## 回滚方案

如果新方案有问题，可以快速回滚到原来的逻辑：
1. 删除新添加的count查询方法
2. 恢复Service方法中的原始分页逻辑
3. 保留XML中的过滤条件修改（这部分是有益的）
