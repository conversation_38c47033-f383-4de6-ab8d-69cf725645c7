# Total数量不一致问题分析与解决方案

## 问题描述

**接口**: `/api/opr/thematicGraph/queryTgInsEntityPage`

**入参**:
```json
{
  "tgId": 11640673,
  "entityName": ["局站"],
  "keyword": null,
  "operateType": 1,
  "pageNo": 1,
  "pageSize": 20
}
```

**问题**: 返回数据只有1条，但total显示2859，数量不一致。

## 问题分析

### 1. 调用链路
```
Controller: ThematicGraphController.queryTgInsEntityPage()
    ↓
Service: ThematicGraphService.queryTgInsEntityPage()
    ↓
Mapper: TempTgInsEntityMapper.queryTgInsEntityPage()
```

### 2. 问题根源
1. **分页查询机制**: 使用了 `PageHelper.startPage()` 自动计算total
2. **过滤条件问题**: XML中的 `entityName` 过滤条件可能没有生效
3. **数据不一致**: PageHelper计算的total是基于没有entityName过滤的结果，但实际返回数据被过滤了

### 3. 可能的原因
- XML中的MyBatis条件判断有问题
- BeanUtils.copyProperties没有正确复制entityName字段
- 数据库中的数据状态问题

## 解决方案

### 已实施的修改

#### 1. 简化XML查询条件
**文件**: `kg-data/src/main/resources/mapper/thematicGraph/TempTgInsEntityMapper.xml`

**修改前**:
```xml
<if test="entityName != null">
    <choose>
        <when test="entityName instanceof java.util.List and entityName.size() > 0">
            <!-- 复杂的条件判断 -->
        </when>
    </choose>
</if>
```

**修改后**:
```xml
<if test="entityName != null and entityName.size() > 0">
    <if test="entityName[0] != '不限定实体类型'">
        and entityc.entity_name in
        <foreach collection="entityName" item="name" open="(" close=")" separator=",">
            #{name}
        </foreach>
    </if>
</if>
```

#### 2. 添加调试日志
**文件**: `kg-system-web/src/main/java/com/ffcs/oss/kg/system/service/thematicGraph/ThematicGraphService.java`

```java
// 添加调试日志
log.info("queryTgInsEntityPage - 入参entityName: {}, keyword: {}, tgId: {}", 
        evt.getEntityName(), evt.getKeyword(), evt.getTgId());
log.info("queryTgInsEntityPage - model entityName: {}, keyword: {}", 
        model.getEntityName(), model.getKeyword());
```

#### 3. 修改字段类型
**文件**: `QueryThematicGraphParamPageEvt.java` 和 `QueryThematicGraphParamPageModel.java`

将 `entityName` 字段类型改为 `List<String>`，支持多个实体类型查询。

## 验证方法

### 1. 检查日志输出
运行接口后，查看日志中的entityName值是否正确传递：
```
queryTgInsEntityPage - 入参entityName: [局站], keyword: null, tgId: 11640673
queryTgInsEntityPage - model entityName: [局站], keyword: null
```

### 2. 检查生成的SQL
期望生成的SQL应该包含entityName过滤条件：
```sql
SELECT ... 
FROM kg_temp_thematic_graph_entity_d kttged
JOIN kg_temp_tg_ins_entity_d ins ON ...
JOIN kg_model_entity_c entityc ON ...
WHERE 1=1 
  AND ins.deleted = '0' 
  AND kttged.is_del = '0'
  AND kttged.is_sync = '1' 
  AND ins.is_sync = '1' 
  AND kttged.tg_id = 11640673
  AND entityc.entity_name IN ('局站')  -- 这个条件必须存在
ORDER BY ins.updated_time DESC
```

### 3. 数据库直接查询验证
```sql
-- 查询总数（不带entityName过滤）
SELECT COUNT(*) 
FROM kg_temp_thematic_graph_entity_d kttged
JOIN kg_temp_tg_ins_entity_d ins ON ins.entity_code = kttged.model_entity_code AND kttged.tg_id = ins.tg_id
JOIN kg_model_entity_c entityc ON ins.entity_code = entityc.entity_code
WHERE ins.deleted = '0' 
  AND kttged.is_del = '0'
  AND kttged.is_sync = '1' 
  AND ins.is_sync = '1' 
  AND kttged.tg_id = 11640673;

-- 查询总数（带entityName过滤）
SELECT COUNT(*) 
FROM kg_temp_thematic_graph_entity_d kttged
JOIN kg_temp_tg_ins_entity_d ins ON ins.entity_code = kttged.model_entity_code AND kttged.tg_id = ins.tg_id
JOIN kg_model_entity_c entityc ON ins.entity_code = entityc.entity_code
WHERE ins.deleted = '0' 
  AND kttged.is_del = '0'
  AND kttged.is_sync = '1' 
  AND ins.is_sync = '1' 
  AND kttged.tg_id = 11640673
  AND entityc.entity_name = '局站';
```

## 预期结果

修改后，接口应该返回：
- `total`: 实际符合条件的数据总数（应该是1或接近1的数字）
- `list`: 包含符合条件的数据
- 数量一致性：total和实际返回的数据数量应该匹配

## 后续优化建议

1. **添加单元测试**: 确保entityName过滤功能正常工作
2. **性能优化**: 如果数据量很大，考虑在数据库层面添加索引
3. **错误处理**: 添加更好的错误处理和日志记录
4. **文档更新**: 更新API文档，说明entityName参数的使用方法
