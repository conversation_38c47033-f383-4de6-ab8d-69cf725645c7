# SQL调试分析

## 问题描述
- 入参：`entityName: ["局站"]`
- 返回：只有1条数据，但total显示2859
- 说明：分页查询的total计算有问题

## 可能的原因

### 1. XML条件没有生效
当前XML条件：
```xml
<if test="entityName != null">
    <choose>
        <when test="entityName instanceof java.util.List and entityName.size() > 0">
            <choose>
                <when test="entityName.size() == 1 and entityName[0] == '不限定实体类型'">
                    <!-- 不限定实体类型，不添加过滤条件 -->
                </when>
                <otherwise>
                    and entityc.entity_name in
                    <foreach collection="entityName" item="name" open="(" close=")" separator=",">
                        #{name}
                    </foreach>
                </otherwise>
            </choose>
        </when>
    </choose>
</if>
```

### 2. 预期生成的SQL
```sql
select ... 
from kg_temp_thematic_graph_entity_d kttged
join kg_temp_tg_ins_entity_d ins on ins.entity_code = kttged.model_entity_code and kttged.tg_id = ins.tg_id
join kg_model_entity_c entityc on ins.entity_code = entityc.entity_code
where 1=1 and ins.deleted = '0' and kttged.is_del = '0'
and ins.ins_code is not null and ins.entity_code is not null
and kttged.is_sync = '1' and ins.is_sync = '1' and kttged.tg_id = 11640673
and entityc.entity_name in ('局站')
order by ins.updated_time desc
```

### 3. 实际可能执行的SQL（如果条件没生效）
```sql
select ... 
from kg_temp_thematic_graph_entity_d kttged
join kg_temp_tg_ins_entity_d ins on ins.entity_code = kttged.model_entity_code and kttged.tg_id = ins.tg_id
join kg_model_entity_c entityc on ins.entity_code = entityc.entity_code
where 1=1 and ins.deleted = '0' and kttged.is_del = '0'
and ins.ins_code is not null and ins.entity_code is not null
and kttged.is_sync = '1' and ins.is_sync = '1' and kttged.tg_id = 11640673
-- 缺少 entityName 条件
order by ins.updated_time desc
```

## 解决方案

### 方案1：简化XML条件
```xml
<if test="entityName != null and entityName.size() > 0">
    <if test="!entityName.contains('不限定实体类型')">
        and entityc.entity_name in
        <foreach collection="entityName" item="name" open="(" close=")" separator=",">
            #{name}
        </foreach>
    </if>
</if>
```

### 方案2：在Java代码中处理
在ThematicGraphService.queryTgInsEntityPage方法中，如果XML条件不生效，可以在Java代码中进行二次过滤。

### 方案3：添加日志调试
在XML中添加日志输出，确认条件是否生效。
