-- AI接口调用日志表
CREATE TABLE kg_bases_ai_call_log_d (
    id BIGSERIAL PRIMARY KEY,                                          -- 主键ID
    batch_no VARCHAR(50) NOT NULL,                                     -- 调用批次号
    call_type VARCHAR(30) NOT NULL,                                    -- 调用类型
    call_type_desc VARCHAR(100),                                       -- 调用类型描述
    space_id BIGINT,                                                   -- 知识空间ID
    document_id BIGINT,                                                -- 文档ID（可选）
    call_start_time TIMESTAMP,                                         -- 调用开始时间
    call_end_time TIMESTAMP,                                           -- 调用结束时间
    execution_time BIGINT,                                             -- 执行时长（毫秒）
    is_success BOOLEAN,                                                -- 是否成功
    total_count INTEGER,                                               -- 总数量
    success_count INTEGER,                                             -- 成功数量
    failed_count INTEGER,                                              -- 失败数量
    error_message TEXT,                                                -- 错误信息
    request_params JSONB,                                              -- 请求参数（JSON格式）
    response_data JSONB,                                               -- 响应数据（JSON格式）
    caller_info VARCHAR(200),                                          -- 调用方信息
    remark VARCHAR(500),                                               -- 备注
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,                  -- 创建时间
    created_user VARCHAR(50)                                           -- 创建用户
);

-- 创建序列
CREATE SEQUENCE kg_bases_ai_call_log_d_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 设置默认值
ALTER TABLE kg_bases_ai_call_log_d ALTER COLUMN id SET DEFAULT nextval('kg_bases_ai_call_log_d_id_seq');

-- 创建索引
CREATE INDEX idx_kg_bases_ai_call_log_d_batch_no ON kg_bases_ai_call_log_d (batch_no);
CREATE INDEX idx_kg_bases_ai_call_log_d_call_type ON kg_bases_ai_call_log_d (call_type);
CREATE INDEX idx_kg_bases_ai_call_log_d_space_id ON kg_bases_ai_call_log_d (space_id);
CREATE INDEX idx_kg_bases_ai_call_log_d_document_id ON kg_bases_ai_call_log_d (document_id);
CREATE INDEX idx_kg_bases_ai_call_log_d_call_time ON kg_bases_ai_call_log_d (call_start_time);
CREATE INDEX idx_kg_bases_ai_call_log_d_is_success ON kg_bases_ai_call_log_d (is_success);
CREATE INDEX idx_kg_bases_ai_call_log_d_created_time ON kg_bases_ai_call_log_d (created_time);

-- 添加表注释
COMMENT ON TABLE kg_bases_ai_call_log_d IS 'AI接口调用日志表';
COMMENT ON COLUMN kg_bases_ai_call_log_d.id IS '主键ID';
COMMENT ON COLUMN kg_bases_ai_call_log_d.batch_no IS '调用批次号';
COMMENT ON COLUMN kg_bases_ai_call_log_d.call_type IS '调用类型：SLICE_IMPORT(导入文档切片), STATUS_UPDATE(更新文档处理状态)等';
COMMENT ON COLUMN kg_bases_ai_call_log_d.call_type_desc IS '调用类型描述';
COMMENT ON COLUMN kg_bases_ai_call_log_d.space_id IS '知识空间ID';
COMMENT ON COLUMN kg_bases_ai_call_log_d.document_id IS '文档ID（可选）';
COMMENT ON COLUMN kg_bases_ai_call_log_d.call_start_time IS '调用开始时间';
COMMENT ON COLUMN kg_bases_ai_call_log_d.call_end_time IS '调用结束时间';
COMMENT ON COLUMN kg_bases_ai_call_log_d.execution_time IS '执行时长（毫秒）';
COMMENT ON COLUMN kg_bases_ai_call_log_d.is_success IS '是否成功';
COMMENT ON COLUMN kg_bases_ai_call_log_d.total_count IS '总数量';
COMMENT ON COLUMN kg_bases_ai_call_log_d.success_count IS '成功数量';
COMMENT ON COLUMN kg_bases_ai_call_log_d.failed_count IS '失败数量';
COMMENT ON COLUMN kg_bases_ai_call_log_d.error_message IS '错误信息';
COMMENT ON COLUMN kg_bases_ai_call_log_d.request_params IS '请求参数（JSON格式）';
COMMENT ON COLUMN kg_bases_ai_call_log_d.response_data IS '响应数据（JSON格式）';
COMMENT ON COLUMN kg_bases_ai_call_log_d.caller_info IS '调用方信息';
COMMENT ON COLUMN kg_bases_ai_call_log_d.remark IS '备注';
COMMENT ON COLUMN kg_bases_ai_call_log_d.created_time IS '创建时间';
COMMENT ON COLUMN kg_bases_ai_call_log_d.created_user IS '创建用户'; 