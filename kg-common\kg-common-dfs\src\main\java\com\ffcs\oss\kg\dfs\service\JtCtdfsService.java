package com.ffcs.oss.kg.dfs.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.dfs.config.JtCtdfsConfig;
import com.jcraft.jsch.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Properties;
import java.util.Vector;
import java.util.stream.Collectors;

@Component
@Service
public class JtCtdfsService {

    private static final Logger log = LoggerFactory.getLogger(JtCtdfsService.class);

    @Resource
    private JtCtdfsConfig jtCtdfsConfig;

    // 简化为单一连接管理（适用于串行处理）
    private Session session = null;
    private ChannelSftp sftpClient = null;
    private static final int CONNECT_TIMEOUT = 10000; // 10秒连接超时
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    
    // 连接状态追踪
    private volatile long lastUsedTime = 0;
    private final Object connectionLock = new Object(); // 连接同步锁

    /**
     * 连接到SFTP服务器（串行版本）
     */
    public synchronized void connect() {
        if (isConnected()) {
            updateLastUsedTime();
            log.debug("SFTP连接已存在，无需重复连接");
            return;
        }

        int retryCount = 0;
        while (retryCount < MAX_RETRY_ATTEMPTS) {
            try {
                // 清理旧连接
                disconnect();
                
                JSch jsch = new JSch();
                session = jsch.getSession(jtCtdfsConfig.getUserName(),
                        jtCtdfsConfig.getHost(),
                        Integer.parseInt(jtCtdfsConfig.getPort()));
                session.setPassword(jtCtdfsConfig.getPassword());

                // 配置SFTP连接参数
                Properties config = new Properties();
                config.put("StrictHostKeyChecking", "no");
                config.put("PreferredAuthentications", "password");
                session.setConfig(config);
                session.setTimeout(CONNECT_TIMEOUT);

                log.info("正在连接SFTP服务器: {}:{}", jtCtdfsConfig.getHost(), jtCtdfsConfig.getPort());
                session.connect();

                // 打开SFTP通道
                Channel channel = session.openChannel("sftp");
                channel.connect();
                sftpClient = (ChannelSftp) channel;

                updateLastUsedTime();
                log.info("成功连接到SFTP服务器: {}", jtCtdfsConfig.getHost());
                return;
                
            } catch (NumberFormatException e) {
                String msg = "SFTP端口配置错误: " + jtCtdfsConfig.getPort();
                log.error(msg, e);
                throw new KnowledgeGraphException(msg, e);
            } catch (JSchException e) {
                retryCount++;
                if (retryCount >= MAX_RETRY_ATTEMPTS) {
                    String msg = String.format("连接SFTP服务器失败(重试%d次): %s", retryCount, e.getMessage());
                    log.error(msg, e);
                    throw new KnowledgeGraphException(msg, e);
                }
                log.warn("连接SFTP服务器失败，正在进行第{}次重试", retryCount);
                try {
                    Thread.sleep(1000 * retryCount); // 递增重试延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new KnowledgeGraphException("重试等待被中断", ie);
                }
            }
        }
    }

    /**
     * 断开SFTP连接并释放资源
     */
    public synchronized void disconnect() {
        try {
            if (sftpClient != null) {
                if (sftpClient.isConnected()) {
                    sftpClient.disconnect();
                }
                sftpClient = null;
            }
            if (session != null) {
                if (session.isConnected()) {
                    session.disconnect();
                }
                session = null;
            }
            log.debug("SFTP资源已成功释放");
        } catch (Exception e) {
            log.error("释放SFTP资源时发生错误", e);
        }
    }

    /**
     * 检查是否已连接
     */
    public synchronized boolean isConnected() {
        boolean connected = sftpClient != null
                && sftpClient.isConnected()
                && session != null
                && session.isConnected();
                
        // 检查连接是否超时（15分钟无活动则认为需要重连，给服务器端超时留出缓冲）
        if (connected && System.currentTimeMillis() - lastUsedTime > 15 * 60 * 1000) {
            log.info("SFTP连接空闲时间过长，主动重新连接以避免服务器端超时");
            disconnect();
            return false;
        }
        
        return connected;
    }
    
    /**
     * 更新最后使用时间
     */
    private void updateLastUsedTime() {
        this.lastUsedTime = System.currentTimeMillis();
    }

    /**
     * 上传文件到SFTP服务器 - 串行优化版本，包含连接保活
     */
    public String uploadJtFile(String remoteDirectory, String fileName, InputStream inputStream, String kgName) throws KnowledgeGraphException {
        String threadName = Thread.currentThread().getName();
        log.info("开始上传文件 - 线程: {}, 目录: {}, 原始文件名: {}, 目标文件名: {}", 
                threadName, remoteDirectory, fileName, kgName);

        // 参数校验
        if (StringUtils.isBlank(remoteDirectory) || StringUtils.isBlank(fileName) || 
            inputStream == null || StringUtils.isBlank(kgName)) {
            String msg = "上传参数不能为空 - 目录: " + remoteDirectory + ", 原始文件名: " + fileName + ", 目标文件名: " + kgName;
            log.error(msg);
            throw new KnowledgeGraphException(msg);
        }

        // 标准化路径
        String normalizedPath = normalizePath(remoteDirectory);
        String tempRemoteFilePath = normalizedPath + fileName;  // 临时文件路径
        String finalRemoteFilePath = normalizedPath + kgName;   // 最终文件路径

        if (finalRemoteFilePath.length() > 1024) {
            String msg = "上传的文件路径过长: " + finalRemoteFilePath;
            log.error(msg);
            throw new KnowledgeGraphException(msg);
        }

        // 使用连接锁确保串行访问
        synchronized (connectionLock) {
            int retryCount = 0;
            int maxRetries = 3;
            
            while (retryCount <= maxRetries) {
                try {
                    // 确保连接可用，包含保活检查
                    checkAndReconnectIfNeeded();
                    
                    log.debug("使用SFTP连接上传文件 - 线程: {}, 目标路径: {}, 尝试次数: {}", 
                            threadName, finalRemoteFilePath, retryCount + 1);

                    // 检查并创建目录
                    createDirectories(sftpClient, normalizedPath);

                    // 上传文件前再次确认连接（防止上传过程中连接断开）
                    if (!sftpClient.isConnected()) {
                        throw new SftpException(0, "SFTP连接在上传前已断开");
                    }

                    // 上传文件
                    log.debug("开始上传临时文件 - 线程: {}, 路径: {}", threadName, tempRemoteFilePath);
                    sftpClient.put(inputStream, tempRemoteFilePath, ChannelSftp.OVERWRITE);

                    // 验证临时文件上传成功
                    if (!isFileExists(sftpClient, tempRemoteFilePath)) {
                        throw new KnowledgeGraphException("临时文件上传后验证失败: " + tempRemoteFilePath);
                    }

                    // 重命名文件
                    log.debug("开始重命名文件 - 线程: {}, {} -> {}", threadName, tempRemoteFilePath, finalRemoteFilePath);
                    sftpClient.rename(tempRemoteFilePath, finalRemoteFilePath);

                    // 验证最终文件
                    if (!isFileExists(sftpClient, finalRemoteFilePath)) {
                        throw new KnowledgeGraphException("文件重命名后验证失败: " + finalRemoteFilePath);
                    }

                    updateLastUsedTime();
                    log.info("文件上传成功 - 线程: {}, 文件路径: {}", threadName, finalRemoteFilePath);
                    return finalRemoteFilePath;

                } catch (SftpException e) {
                    retryCount++;
                    String errorMsg = String.format("SFTP操作失败 - 线程: %s, 路径: %s, 尝试次数: %d, 错误: %s", 
                            threadName, tempRemoteFilePath, retryCount, e.getMessage());
                    
                    if (retryCount <= maxRetries) {
                        log.warn("{}, 将进行重试", errorMsg);
                        // 连接可能已断开，强制重新连接
                        disconnect();
                        try {
                            Thread.sleep(2000 * retryCount); // 递增延迟重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new KnowledgeGraphException("重试等待被中断", ie);
                        }
                    } else {
                        log.error("{}, 已达到最大重试次数", errorMsg);
                        throw new KnowledgeGraphException(errorMsg, e);
                    }
                } catch (Exception e) {
                    String msg = String.format("上传文件时发生异常 - 线程: %s, 路径: %s, 错误: %s", 
                            threadName, tempRemoteFilePath, e.getMessage());
                    log.error(msg, e);
                    throw new KnowledgeGraphException(msg, e);
                }
            }
            
            // 理论上不会到达这里
            throw new KnowledgeGraphException("文件上传失败，已达到最大重试次数");
        }
    }

    /**
     * 检查连接状态，必要时重新连接，包含保活机制
     */
    private void checkAndReconnectIfNeeded() {
        if (!isConnected()) {
            log.info("SFTP连接已断开，尝试重新连接...");
            connect();
        } else {
            // 连接保活：如果距离上次使用超过5分钟，发送一个简单的命令保持连接活跃
            long idleTime = System.currentTimeMillis() - lastUsedTime;
            if (idleTime > 5 * 60 * 1000) { // 5分钟
                try {
                    log.debug("执行连接保活检查...");
                    sftpClient.pwd(); // 发送一个简单的命令保持连接活跃
                    updateLastUsedTime();
                    log.debug("连接保活成功");
                } catch (SftpException e) {
                    log.warn("连接保活失败，将重新建立连接: {}", e.getMessage());
                    disconnect();
                    connect();
                }
            }
        }
    }

    /**
     * 检查并创建目录（递归）
     */
    private void createDirectories(ChannelSftp sftpClient, String path) throws SftpException {
        String[] folders = path.split("/");
        StringBuilder currentPath = new StringBuilder("/");

        for (String folder : folders) {
            if (folder.isEmpty()) {
                continue;
            }

            currentPath.append(folder).append("/");
            try {
                sftpClient.cd(currentPath.toString());
                log.debug("目录已存在: {}", currentPath);
            } catch (SftpException e) {
                if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                    log.debug("创建目录: {}", currentPath);
                    sftpClient.mkdir(currentPath.toString());
                    sftpClient.cd(currentPath.toString());
                } else {
                    throw e;
                }
            }
        }
    }

    /**
     * 检查文件是否存在
     */
    private boolean isFileExists(ChannelSftp sftpClient, String filePath) {
        try {
            sftpClient.stat(filePath);
            return true;
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                return false;
            }
            log.error("检查文件存在时出错: {}", filePath, e);
            return false;
        }
    }

    /**
     * 标准化路径（确保以/结尾）
     */
    private String normalizePath(String path) {
        if (path == null) {
            return "/";
        }

        path = path.trim();
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        if (!path.endsWith("/")) {
            path += "/";
        }
        return path;
    }

    /**
     * 获取连接状态信息
     */
    public String getConnectionStatus() {
        synchronized (connectionLock) {
            boolean connected = isConnected();
            long idleTime = System.currentTimeMillis() - lastUsedTime;
            return String.format("SFTP连接状态 - 已连接: %s, 空闲时间: %d秒", connected, idleTime / 1000);
        }
    }

    /**
     * 应用关闭时清理连接
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始清理SFTP连接...");
        disconnect();
        log.info("SFTP连接清理完成");
    }
}