package com.ffcs.oss.kg.data.enums;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/1/17
 */
public enum CaseStateEnum {
    NEW("1","草稿"),
    PENDING_AUDIT("2","待审核"),
    AUDIT_NOT_PASS("3","审核不通过"),
    TO_BE_RELEASED("4","待发布"),
    GO_ONLINE("5","上线"),
    OFF_LINE("6","下线"),
    PUBLISHED("7","已发布"),
    SECOND_ONLINE("8","二次上线"),
    EDITING("9","编辑中");

    private String stateValue;
    private String stateDesc;


    public static boolean isRepeatFail(String state) {
        return state.equals(PENDING_AUDIT.stateValue) ||
                state.equals(TO_BE_RELEASED.stateValue) ||
                state.equals(PUBLISHED.stateValue) ||
                state.equals(SECOND_ONLINE.stateValue);
    }


    // 是否为可以上报的状态
    public static boolean isTrueReportState(String state) {
        return state.equals(PENDING_AUDIT.stateValue) ||
                state.equals(TO_BE_RELEASED.stateValue) ||
                state.equals(PUBLISHED.stateValue) ||
                state.equals(SECOND_ONLINE.stateValue);
    }

    public void setStateValue(String stateValue) {
        this.stateValue = stateValue;
    }

    public String getStateDesc() {
        return stateDesc;
    }

    public void setStateDesc(String stateDesc) {
        this.stateDesc = stateDesc;
    }

    CaseStateEnum(String stateValue) {
        this.stateValue = stateValue;
    }

    public String getStateValue() {
        return stateValue;
    }

    CaseStateEnum(String stateValue, String stateDesc) {
        this.stateValue = stateValue;
        this.stateDesc = stateDesc;
    }
}
