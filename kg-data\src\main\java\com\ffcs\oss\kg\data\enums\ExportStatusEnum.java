package com.ffcs.oss.kg.data.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 上报状态枚举
 * 
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum ExportStatusEnum {

    NOT_EXPORTED("0", "未导出"),
    EXPORTED("1", "已导出");

    private final String code;
    private final String desc;

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 上报状态枚举
     */
    public static ExportStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ExportStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     * 
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(String code) {
        ExportStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
} 