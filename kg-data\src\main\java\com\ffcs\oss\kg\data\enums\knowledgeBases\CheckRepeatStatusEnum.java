package com.ffcs.oss.kg.data.enums.knowledgeBases;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 查重状态枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CheckRepeatStatusEnum {
    
    /**
     * 未查重
     */
    NOT_CHECKED(0, "未查重"),
    
    /**
     * 查重中
     */
    CHECKING(1, "查重中"),
    
    /**
     * 查重成功
     */
    SUCCESS(2, "查重成功"),
    
    /**
     * 查重失败
     */
    FAILED(3, "查重失败");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static CheckRepeatStatusEnum getByCode(Integer code) {
        if (code == null) {
            return NOT_CHECKED;
        }
        for (CheckRepeatStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return NOT_CHECKED;
    }
    
    /**
     * 判断是否为失败状态
     * 
     * @param code 状态码
     * @return 是否为失败状态
     */
    public static boolean isFailed(Integer code) {
        return FAILED.getCode().equals(code);
    }
    
    /**
     * 判断是否为成功状态
     * 
     * @param code 状态码
     * @return 是否为成功状态
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }
    
    /**
     * 判断是否为查重中状态
     * 
     * @param code 状态码
     * @return 是否为查重中状态
     */
    public static boolean isChecking(Integer code) {
        return CHECKING.getCode().equals(code);
    }
    
    /**
     * 判断是否为未查重状态
     * 
     * @param code 状态码
     * @return 是否为未查重状态
     */
    public static boolean isNotChecked(Integer code) {
        return code == null || NOT_CHECKED.getCode().equals(code);
    }
} 