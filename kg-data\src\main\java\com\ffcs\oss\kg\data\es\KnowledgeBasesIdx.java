package com.ffcs.oss.kg.data.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.PostLoad;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Document(indexName = "#{@knowledgeBasesIndexName}")
public class KnowledgeBasesIdx extends PermissionIdx {

    @Id
    @Field(type = FieldType.Integer)
    private Long kgKnowledgeBasesId;

    /**
     * 区域;cs_sm_region_d的主键
     */
    @Field(type = FieldType.Integer)
    private Long region;

    /**
     * 专业;参见字典表-专业
     */
    @Field(type = FieldType.Keyword)
    private String major;

    /**
     * 状态;参见字典表-状态
     */
    @Field(type = FieldType.Keyword)
    private String state;

    /**
     * 密级;参见字典表-密级
     */
    @Field(type = FieldType.Keyword)
    private String secretLevel;

    /**
     * 分类ID
     */
    @Field(type = FieldType.Integer)
    private Long categoryId;

    /**
     * 关联工单;多个工单号用英文逗号分隔
     */
    @Field(type = FieldType.Keyword)
    private String relOrder;

    /**
     * 摘要
     */
    @Field(type = FieldType.Text)
    private String summary;

    /**
     * 关键词;多个关键字用英文逗号分隔
     */
    @Field(type = FieldType.Text)
    private String operativeWord;

    /**
     * 业务/网络情况（纯文本）
     */
    @Field(type = FieldType.Text)
    private String businessNetworkSituation;

    /**
     * 原因分析（纯文本）
     */
    @Field(type = FieldType.Text)
    private String receiptReason;

    /**
     * 故障场景（纯文本）
     */
    @Field(type = FieldType.Text)
    private String faultScene;

    /**
     * 处置过程（纯文本）
     */
    @Field(type = FieldType.Text)
    private String disposalProcess;

    /**
     * 思考与启示(纯文本）
     */
    @Field(type = FieldType.Text)
    private String enlightenment;

    /**
     * 知识详情(纯文本）
     */
    @Field(type = FieldType.Text)
    private String knowledgeDetails;

    /**
     * 知识编写人;多个作者字用英文逗号分隔
     */
    @Field(type = FieldType.Keyword)
    private String author;

    /**
     * 审核状态;0-未审核 1-通过 2-未通过
     */
    @Field(type = FieldType.Keyword)
    private String auditStatus;

    /**
     * 审核人
     */
    @Field(type = FieldType.Keyword)
    private String reviewer;

    /**
     * 是否删除
     */
    @Field(type = FieldType.Keyword)
    private String isDeleted;

    /**
     * 创建人员
     */
    @Field(type = FieldType.Keyword)
    private String createdUserName;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 修改人员
     */
    @Field(type = FieldType.Keyword)
    private String updatedUserName;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 全文检索字段
     */
    @Field(type = FieldType.Text)
    private String fullText;

    /**
     * 搜索次数
     */
    @Field(type = FieldType.Integer)
    private Integer searchNumber;

    /**
     * 置顶顺序
     */
    @Field(type = FieldType.Keyword)
    private String topUp;

    @Field(type = FieldType.Keyword)
    private String knowledgeFilePath;

    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    @Field(type = FieldType.Double)
    private BigDecimal checkRepeatResult;

    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;

    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    @Field(type = FieldType.Text)
    private String knowledgeDetailsRt;

    /**
     * 相似文档
     */
    @Field(type = FieldType.Keyword)
    private String similarKnowledgeBases;

    /**
     * 知识分类
     */
    @Field(type = FieldType.Keyword)
    private String knowledgeType;

    /**
     * 提交时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 是否首次下线
     */
    @Field(type = FieldType.Integer)
    private Integer whetherFirstOffline;

    /**
     * 点击次数
     */
    @Field(type = FieldType.Integer)
    private Integer clickCount;

    /**
     * 知识名称
     */
    @Field(type = FieldType.Keyword)
    private String knowledgeName;

    /**
     * 知识来源
     */
    @Field(type = FieldType.Keyword)
    private String knowledgeOrigin;

    /**
     * 流程场景
     */
    @Field(type = FieldType.Keyword)
    private String flowScene;

    /**
     * 应用场景
     */
    @Field(type = FieldType.Keyword)
    private String applicationScene;

    /**
     * 文档类型
     */
    @Field(type = FieldType.Keyword)
    private String documentType;

    /**
     * 文档类型
     */
    @Field(type = FieldType.Keyword)
    private String documentFormat;

    /**
     * 全区域
     */
    @ApiModelProperty(name = "全区域")
    @Field(type = FieldType.Text)
    private String fullRegion;

    /**
     * 区域名称
     */
    @ApiModelProperty(name = "区域名称")
    @Field(type = FieldType.Text)
    private String regionName;

    /**
     * 是否同步集团
     */
    @ApiModelProperty(name = "同步集团")
    @Field(type = FieldType.Text)
    private String isSync;

    /**
     * 人力资源名
     */
    @ApiModelProperty(name = "人力资源名")
    @Field(type = FieldType.Text)
    private String jtAuthor;

//    /**
//     * 上报时间
//     */
//    @ApiModelProperty(name = "上报时间")
//    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @Field(type = FieldType.Text)
//    private String reportTime;

    /**
     * 上报状态：集团未上报：1 已上报集团：2 集团已下线：3 集团已删除：4
     */
    @ApiModelProperty(name = "上报状态", notes = "集团未上报：1 已上报集团：2 集团已下线：3 集团已删除：4")
    @Field(type = FieldType.Text)
    private String reportStatus;

    /**
     * 上报批次号
     */
    @ApiModelProperty(name = "上报批次号")
    @Field(type = FieldType.Text)
    private String reportBatchNo;

    /**
     * 上报情况说明
     */
    @ApiModelProperty(name = "上报情况说明")
    @Field(type = FieldType.Text)
    private String reportDescription;

    /**
     * 公开范围
     */
    @ApiModelProperty(name = "公开范围")
    @Field(type = FieldType.Text)
    private String publicity;

    /**
     * 有效期
     */
    @ApiModelProperty(name = "有效期")
    @Field(type = FieldType.Text)
    private String periodValidity;

    @ApiModelProperty(name = "生命周期")
    @Field(type = FieldType.Text)
    private String lifeCycle;

    @ApiModelProperty(name = "word转pdf")
    @Field(type = FieldType.Text)
    private String wordToPdf;

    @ApiModelProperty(name = "组织机构")
    @Field(type = FieldType.Text)
    private String institution;

    @ApiModelProperty(name = "问题分类")
    @Field(type = FieldType.Text)
    private String questionClassify;

    @ApiModelProperty(name = "问题")
    @Field(type = FieldType.Text)
    private String question;

    @ApiModelProperty(name = "思考过程")
    @Field(type = FieldType.Text)
    private String thoughtProcess;


    @ApiModelProperty(name = "问题回答")
    @Field(type = FieldType.Text)
    private String answer;

    @ApiModelProperty(name = "1为知识文档 2为问答对 3为专业词汇")
    @Field(type = FieldType.Text)
    private String basesType;

    @ApiModelProperty(name = "文件大小")
    @Field(type = FieldType.Long)
    private Long filesize;

    /**
     * 专业词汇-术语/缩略语/专业名词
     */
    @ApiModelProperty(name = "术语/缩略语/专业名词")
    @Field(type = FieldType.Text)
    private String term;
    
    /**
     * 专业词汇-中文
     */
    @ApiModelProperty(name = "中文")
    @Field(type = FieldType.Text)
    private String chinese;
    
    /**
     * 专业词汇-英文
     */
    @ApiModelProperty(name = "英文")
    @Field(type = FieldType.Text)
    private String english;
    
    /**
     * 专业词汇-中文同义词
     */
    @ApiModelProperty(name = "中文同义词")
    @Field(type = FieldType.Text)
    private String chineseSynonym;
    
    /**
     * 专业词汇-英文同义词
     */
    @ApiModelProperty(name = "英文同义词")
    @Field(type = FieldType.Text)
    private String englishSynonym;
    
    /**
     * 专业词汇-中文定义
     */
    @ApiModelProperty(name = "中文定义")
    @Field(type = FieldType.Text)
    private String chineseDefinition;
    
    /**
     * 专业词汇-英文定义
     */
    @ApiModelProperty(name = "英文定义")
    @Field(type = FieldType.Text)
    private String englishDefinition;
    
    /**
     * 专业词汇-是否通信信息领域
     */
    @ApiModelProperty(name = "是否通信信息领域")
    @Field(type = FieldType.Keyword)
    private String isTelecomInfoField;

    /**
     * 知识名称 + 摘要 + 关键词 + 业务/网络情况 + 故障场景 + 原因分析 + 处置过程 + 问题 + 答案
     * + 专业词汇相关字段
     */
    @PostLoad
    public void setFullText() {
        // 基础字段
        StringBuilder fullTextBuilder = new StringBuilder();
        if (knowledgeName != null) fullTextBuilder.append(knowledgeName).append(" ");
        if (summary != null) fullTextBuilder.append(summary).append(" ");
        if (operativeWord != null) fullTextBuilder.append(operativeWord).append(" ");
        
        // 通用字段
        if (businessNetworkSituation != null) fullTextBuilder.append(businessNetworkSituation).append(" ");
        if (faultScene != null) fullTextBuilder.append(faultScene).append(" ");
        if (receiptReason != null) fullTextBuilder.append(receiptReason).append(" ");
        if (disposalProcess != null) fullTextBuilder.append(disposalProcess).append(" ");
        
        // 问答对特有字段
        if ("2".equals(basesType)) {
            if (question != null) fullTextBuilder.append(question).append(" ");
            if (answer != null) fullTextBuilder.append(answer).append(" ");
        }
        
        // 专业词汇特有字段
        if ("3".equals(basesType)) {
            if (term != null) fullTextBuilder.append(term).append(" ");
            if (chinese != null) fullTextBuilder.append(chinese).append(" ");
            if (english != null) fullTextBuilder.append(english).append(" ");
            if (chineseSynonym != null) fullTextBuilder.append(chineseSynonym).append(" ");
            if (englishSynonym != null) fullTextBuilder.append(englishSynonym).append(" ");
            if (chineseDefinition != null) fullTextBuilder.append(chineseDefinition).append(" ");
            if (englishDefinition != null) fullTextBuilder.append(englishDefinition).append(" ");
        }
        
        this.fullText = fullTextBuilder.toString();
    }

}
