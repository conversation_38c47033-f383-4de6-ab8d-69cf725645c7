package com.ffcs.oss.kg.data.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 知识库问答对数据实体
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "知识库问答Cot数据实体")
@TableName(value = "kg_qa_cot_d")
public class KgQaCotD implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 问答Cot主键ID
     */
    @TableId(value = "kg_qa_cot_id", type = IdType.AUTO)
    @ApiModelProperty(value = "问答Cot主键ID")
    private Long kgQaCotId;

    /**
     * 问答对主键ID
     */
    @ApiModelProperty(value = "问答对主键ID")
    private Long kgQaPairId;

    /**
     * 关联的知识库ID
     */
    @TableField(value = "kg_knowledge_bases_id")
    @ApiModelProperty(value = "关联的知识库ID")
    private Long kgKnowledgeBasesId;

    /**
     * 关联的知识空间ID（非数据库字段，用于创建关联关系）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "关联的知识空间ID")
    private Long kgBasesSpaceCId;

    /**
     * 组织机构
     */
    @TableField(value = "institution")
    @ApiModelProperty(value = "组织机构")
    private String institution;

    /**
     * 知识编写人
     */
    @TableField(value = "author")
    @ApiModelProperty(value = "知识编写人")
    private String author;

    /**
     * 专业领域
     */
    @TableField(value = "major")
    @ApiModelProperty(value = "专业领域")
    private String major;

    /**
     * 应用场景
     */
    @TableField(value = "application_scene")
    @ApiModelProperty(value = "应用场景")
    private String applicationScene;

    /**
     * 知识来源
     */
    @TableField(value = "knowledge_origin")
    @ApiModelProperty(value = "知识来源")
    private String knowledgeOrigin;

    /**
     * 流程场景
     */
    @TableField(value = "flow_scene")
    @ApiModelProperty(value = "流程场景")
    private String flowScene;

    /**
     * 公开范围
     */
    @TableField(value = "publicity")
    @ApiModelProperty(value = "公开范围")
    private String publicity;

    /**
     * 有效期
     */
    @TableField(value = "period_validity")
    @ApiModelProperty(value = "有效期")
    private String periodValidity;

    /**
     * 生命周期
     */
    @TableField(value = "life_cycle")
    @ApiModelProperty(value = "生命周期")
    private String lifeCycle;

    /**
     * 问题分类:知识类 指标类 方案类 影响类
     */
    @TableField(value = "question_classify")
    @ApiModelProperty(value = "问题分类:知识类 指标类 方案类 影响类")
    private String questionClassify;

    /**
     * 问题
     */
    @TableField(value = "question")
    @ApiModelProperty(value = "问题")
    private String question;

    /**
     * 问题回答
     */
    @TableField(value = "answer")
    @ApiModelProperty(value = "问题回答")
    private String answer;

    /**
     * 思考过程
     */
    @TableField(value = "thought_process")
    @ApiModelProperty(value = "思考过程")
    private String thoughtProcess;

    /**
     * 通用类别表
     */
    @TableField(value = "category_id")
    @ApiModelProperty(value = "通用类别表")
    private Integer categoryId;

    /**
     * 区域
     */
    @TableField(value = "region")
    @ApiModelProperty(value = "区域")
    private Long region;

    /**
     * 是否删除(1代表删除，数据无效，0代表未删除，数据有效)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除(1代表删除，数据无效，0代表未删除，数据有效)")
    private String isDeleted;

    /**
     * 查询次数
     */
    @TableField(value = "search_number")
    @ApiModelProperty(value = "查询次数")
    private Long searchNumber;

    /**
     * 状态
     */
    @TableField(value = "state")
    @ApiModelProperty(value = "状态")
    private String state;

    /**
     * 审核人
     */
    @TableField(value = "reviewer")
    @ApiModelProperty(value = "审核人")
    private String reviewer;

    /**
     * 审核时间
     */
    @TableField(value = "audit_time")
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 审核状态
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    /**
     * 提交时间
     */
    @TableField(value = "submit_time")
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 发布时间
     */
    @TableField(value = "release_time")
    @ApiModelProperty(value = "发布时间")
    private Date releaseTime;

    /**
     * 同组权限 1可编辑|2可查看|3无权限
     */
    @TableField(value = "same_group_permission")
    @ApiModelProperty(value = "同组权限 1可编辑|2可查看|3无权限")
    private String sameGroupPermission;

    /**
     * 其他人权限：1可编辑|2可查看|3无权限
     */
    @TableField(value = "other_person_permission")
    @ApiModelProperty(value = "其他人权限：1可编辑|2可查看|3无权限")
    private String otherPersonPermission;

    /**
     * 权限类型：1配置中心|2分类权限|3数据权限
     */
    @TableField(value = "permission_type")
    @ApiModelProperty(value = "权限类型：1配置中心|2分类权限|3数据权限")
    private String permissionType;

    /**
     * 评审意见
     */
    @TableField(value = "comment")
    @ApiModelProperty(value = "评审意见")
    private String comment;

    /**
     * 点击次数
     */
    @TableField(value = "click_count")
    @ApiModelProperty(value = "点击次数")
    private Long clickCount;

    /**
     * 租户编码
     */
    @TableField(value = "tenant_code")
    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    /**
     * 创建人
     */
    @TableField(value = "created_user_name")
    @ApiModelProperty(value = "创建人")
    private String createdUserName;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 最近更新人
     */
    @TableField(value = "updated_user_name")
    @ApiModelProperty(value = "最近更新人")
    private String updatedUserName;

    /**
     * 最近更新时间
     */
    @TableField(value = "updated_time")
    @ApiModelProperty(value = "最近更新时间")
    private Date updatedTime;

    /**
     * 分类名称（非数据库字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 区域名称（非数据库字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "区域名称")
    private String regionName;

    /**
     * 是否首次下线(1代表是，0代表否)
     */
    @TableField(value = "whether_first_offline")
    @ApiModelProperty(value = "是否首次下线(1代表是，0代表否)")
    private Integer whetherFirstOffline;

    /**
     * 上报状态（0-未上报，1-已上报，2-上报失败）
     */
    @ApiModelProperty(value = "上报状态", notes = "0-未上报，1-已上报，2-上报失败")
    @TableField(value = "report_status")
    private String reportStatus;

    /**
     * 导出状态（0-未导出，1-已导出）
     */
    @ApiModelProperty(value = "导出状态（0-未导出，1-已导出）")
    @TableField(value = "export_status")
    private String exportStatus;

    /**
     * 上报情况说明
     */
    @ApiModelProperty(value = "上报情况说明")
    @TableField(value = "report_description")
    private String reportDescription;

    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间")
    @TableField(value = "report_time")
    private Date reportTime;
} 