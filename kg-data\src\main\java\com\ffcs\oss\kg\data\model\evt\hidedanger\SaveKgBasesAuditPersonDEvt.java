package com.ffcs.oss.kg.data.model.evt.hidedanger;

import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesAuditPersonD;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/7/31
 */
@Data
public class SaveKgBasesAuditPersonDEvt extends KgBasesAuditPersonD {


    /*@NotNull(message = "用户ID 不能为空")
    @ApiModelProperty("用户ID")
    private Long userId;*/
}
