package com.ffcs.oss.kg.data.model.evt.knowledgeBases;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量修改上报状态请求事件
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "批量修改上报状态请求事件")
public class BatchUpdateReportStatusEvt {

    @ApiModelProperty(value = "操作类型", notes = "1-选择修改，2-全选修改", required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @ApiModelProperty(value = "问答对ID列表", notes = "当操作类型为1时必填")
    private List<Long> qaPairIds;

    @ApiModelProperty(value = "查询条件", notes = "当操作类型为2时使用，用于全选修改")
    private GetQaPairsEvt queryCondition;

    @ApiModelProperty(value = "上报状态", notes = "0-未上报，1-已上报，2-上报失败", required = true)
    @NotBlank(message = "上报状态不能为空")
    private String reportStatus;

    @ApiModelProperty(value = "导出状态", notes = "0-未导出，1-已导出", required = true)
    @NotBlank(message = "导出状态不能为空")
    private String exportStatus;

    @ApiModelProperty(value = "上报情况说明")
    private String reportDescription;

    /**
     * 操作类型常量
     */
    public static final int OPERATION_TYPE_SELECTED = 1; // 选择修改
    public static final int OPERATION_TYPE_ALL = 2; // 全选修改
}
