package com.ffcs.oss.kg.data.model.evt.knowledgeBases;

import com.ffcs.oss.kg.common.core.mvc.PageableEvt;
import com.ffcs.oss.kg.data.model.evt.staff.StaffPermissionEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 获取问答对列表的请求事件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "获取问答对列表的请求事件")
public class GetQaPairsEvt  extends StaffPermissionEvt {

    @ApiModelProperty(value = "知识空间ID")
    private Long kgBasesSpaceCId;

    @ApiModelProperty(value = "问答对ID")
    private Long kgQaPairId;

    @ApiModelProperty(value = "关联的知识库ID")
    private Long kgKnowledgeBasesId;

    @ApiModelProperty(value = "组织机构")
    private String institution;

    @ApiModelProperty(value = "知识编写人")
    private String author;

/*    @ApiModelProperty(value = "专业领域")
    private String major;*/

    @ApiModelProperty(value = "专业领域列表")
    private List<String> major;

    @ApiModelProperty(value = "应用场景")
    private String applicationScene;

    @ApiModelProperty(value = "应用场景列表")
    private List<String> applicationSceneList;

    @ApiModelProperty(value = "知识来源")
    private String knowledgeOrigin;

    @ApiModelProperty(value = "流程场景")
    private String flowScene;

    @ApiModelProperty(value = "流程场景列表")
    private List<String> flowSceneList;

    @ApiModelProperty(value = "问题分类")
    private String questionClassify;

    @ApiModelProperty(value = "问题分类列表")
    private List<String> questionClassifyList;

    @ApiModelProperty(value = "问题关键词")
    private String questionKeyword;

    @ApiModelProperty(value = "状态")
    private List<String> state;


    @ApiModelProperty(value = "区域")
    private Long region;

    @ApiModelProperty(value = "区域列表")
    private List<Long> regionList;


    @ApiModelProperty(value = "分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "创建时间范围-开始")
    private Date startTime;

    @ApiModelProperty(value = "创建时间范围-结束")
    private Date endTime;

    @ApiModelProperty(value = "创建时间列表")
    private List<Date> createdTimeList;

    @ApiModelProperty(value = "是否已删除")
    private String isDeleted;

    @ApiModelProperty(value = "全文检索关键词")
    private String fullText;

    @ApiModelProperty(value = "是否审核排序")
    private Boolean whetherAuditOrder;

    @ApiModelProperty(value = "是否上线")
    private Boolean whetherOnline;

    @ApiModelProperty(value = "前一个用户")
    private String beforeOneUser;

    @ApiModelProperty(value = "当前用户列表")
    private List<String> currentUserList;

    @ApiModelProperty(value = "生命周期列表")
    private List<String> lifeCycleList;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "问答对ID列表")
    private List<Long> kgQaPairIds;

    @ApiModelProperty(value = "状态专业列表（审核规则用）")
    private List<String> stateMajor;

    @ApiModelProperty(value = "状态区域列表（审核规则用）")
    private List<Long> stateRegion;

    @ApiModelProperty(value = "1 文档采编 2 问答对 3 专业词汇")
    private String basesType;

    /**
     * 有效期
     */
    @ApiModelProperty(name = "有效期")
    private List<String> periodValidityList;

    /**
     * 公开范围
     */
    @ApiModelProperty(name = "公开范围")
    private List<String> publicityList;

    /**
     * 知识来源
     */
    @ApiModelProperty(name = "知识来源")
    private List<String> knowledgeOriginList;

    /**
     * 上报状态
     */
    @ApiModelProperty(value = "上报状态", notes = "0-未上报，1-已上报，2-上报失败")
    private List<String> reportStatusList;

    @ApiModelProperty(value = "导出状态", notes = "0-未导出，1-已导出")
    private List<String> exportStatusList;


    @ApiModelProperty(value = "创建人")
    private String createdUserName;

    /**
     * 文档类型
     */
    @ApiModelProperty(name = "文档类型")
    private List<String> documentTypeList;


    @ApiModelProperty("审核人")
    private String reviewer;
} 