package com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot;

import com.ffcs.oss.kg.data.model.evt.knowledgeBases.GetQaPairsEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 导出问答COT请求事件
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "导出问答COT请求事件")
public class ExportQaCotsEvt {

    @ApiModelProperty(value = "导出类型", notes = "1-选择导出，2-全选导出", required = true)
    @NotNull(message = "导出类型不能为空")
    private Integer exportType;

    @ApiModelProperty(value = "问答COTID列表", notes = "当导出类型为1时必填")
    private List<Long> qaCotIds;

    @ApiModelProperty(value = "查询条件", notes = "当导出类型为2时使用，用于全选导出")
    private GetQaCotsEvt queryCondition;

    @ApiModelProperty(value = "文件名", notes = "导出的Excel文件名，不包含扩展名")
    private String fileName;

    /**
     * 导出类型常量
     */
    public static final int EXPORT_TYPE_SELECTED = 1; // 选择导出
    public static final int EXPORT_TYPE_ALL = 2; // 全选导出
}
