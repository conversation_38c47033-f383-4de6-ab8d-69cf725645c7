package com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 问答对审核评论表实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("kg_qa_cot_audit_dimension_d")
public class KgQaCotAuditDimensionD implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "kg_qa_cot_audit_dimension_id", type = IdType.AUTO)
    private Long kgQaCotAuditDimensionId;

    /**
     * 问答COT
     */
    private Long qaCotId;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 是否通过审核(1:通过,0:不通过)
     */
    private String whetherPass;

    /**
     * 排序号
     */
    private Integer orderNum;

    /**
     * 创建人
     */
    private String createdUserName;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedUserName;

    /**
     * 更新时间
     */
    private Date updatedTime;
} 