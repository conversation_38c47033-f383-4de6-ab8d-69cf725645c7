package com.ffcs.oss.kg.data.model.vm.bases;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@ApiModel("审核人表")
public class KgBasesAuditPersonDVm implements Serializable {

	private static final long serialVersionUID =  7186712247000209186L;

	/**
	 * 审核人主键
	 */
   	@ApiModelProperty("审核人主键" )
	private BigInteger kgBasesAuditPersonId;

	/**
	 * 姓名
	 */
   	@ApiModelProperty("姓名" )
	private String name;

	/**
	 * 电话号码
	 */
   	@ApiModelProperty("电话号码" )
	private String telephone;

	/**
	 * 区域
	 */
   	@ApiModelProperty("区域" )
	private Integer regionId;

	/**
	 * 全量区域ID，用,拼接
	 */
   	@ApiModelProperty("全量区域ID，用,拼接" )
	private String fullRegion;

	/**
	 * 专业
	 */
   	@ApiModelProperty("专业" )
	private String major;

	/**
	 * 角色
	 */
   	@ApiModelProperty("角色" )
	private String role;

	/**
	 * 描述
	 */
   	@ApiModelProperty("描述" )
	private String descs;

	/**
	 * 创建时间
	 */
   	@ApiModelProperty("创建时间" )
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdTime;

	/**
	 * 创建人
	 */
   	@ApiModelProperty("创建人" )
	private String createdUserName;

	/**
	 * 最近更新人
	 */
   	@ApiModelProperty("最近更新人" )
	private String updatedUserName;

	/**
	 * 最近更新时间
	 */
   	@ApiModelProperty("最近更新时间" )
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedTime;

	/**
	 * 是否有效
	 */
   	@ApiModelProperty("是否有效" )
	private String isDeleted;
	/**
	 * 是否是区域管理员
 	 */
	private Integer whetherRegionAdmin;

	private Integer whetherProvinceAdmin;

	private List<String> regionList;
	private List<String> majorList;
	private List<String> roleList;
	private String loginName;
	private String totalFullRegion;


	private Long userId;

	public String getTotalFullRegion() {
		return totalFullRegion;
	}

	public void setTotalFullRegion(String totalFullRegion) {
		this.totalFullRegion = totalFullRegion;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public Integer getWhetherProvinceAdmin() {
		return whetherProvinceAdmin;
	}

	public void setWhetherProvinceAdmin(Integer whetherProvinceAdmin) {
		this.whetherProvinceAdmin = whetherProvinceAdmin;
	}

	public List<String> getRegionList() {
		if (CollectionUtil.isEmpty(regionList)&& StringUtils.isNotBlank(this.fullRegion)){
			return Arrays.asList(this.fullRegion.split(","));
		}
		return regionList;
	}

	public void setRegionList(List<String> regionList) {
		this.regionList = regionList;
	}

	public List<String> getMajorList() {
		if (CollectionUtil.isEmpty(majorList)&& StringUtils.isNotBlank(this.major)){
			return Arrays.asList(this.major.split(","));
		}
		return majorList;
	}

	public void setMajorList(List<String> majorList) {
		this.majorList = majorList;
	}

	public List<String> getRoleList() {
		if (CollectionUtil.isEmpty(roleList)&& StringUtils.isNotBlank(this.role)){
			return Arrays.asList(this.role.split(","));
		}
		return roleList;
	}

	public void setRoleList(List<String> roleList) {
		this.roleList = roleList;
	}

	public Integer getWhetherRegionAdmin() {
		return whetherRegionAdmin;
	}

	public void setWhetherRegionAdmin(Integer whetherRegionAdmin) {
		this.whetherRegionAdmin = whetherRegionAdmin;
	}

	public BigInteger getKgBasesAuditPersonId() {
		return kgBasesAuditPersonId;
	}

	public void setKgBasesAuditPersonId(BigInteger kgBasesAuditPersonId) {
		this.kgBasesAuditPersonId = kgBasesAuditPersonId;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTelephone() {
		return this.telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public Integer getRegionId() {
		return this.regionId;
	}

	public void setRegionId(Integer regionId) {
		this.regionId = regionId;
	}

	public String getFullRegion() {
		return this.fullRegion;
	}

	public void setFullRegion(String fullRegion) {
		this.fullRegion = fullRegion;
	}

	public String getMajor() {
		return this.major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public String getRole() {
		return this.role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public String getDescs() {
		return descs;
	}

	public void setDescs(String descs) {
		this.descs = descs;
	}

	public Date getCreatedTime() {
		return this.createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedUserName() {
		return this.createdUserName;
	}

	public void setCreatedUserName(String createdUserName) {
		this.createdUserName = createdUserName;
	}

	public String getUpdatedUserName() {
		return this.updatedUserName;
	}

	public void setUpdatedUserName(String updatedUserName) {
		this.updatedUserName = updatedUserName;
	}

	public Date getUpdatedTime() {
		return this.updatedTime;
	}

	public void setUpdatedTime(Date updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getIsDeleted() {
		return this.isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
