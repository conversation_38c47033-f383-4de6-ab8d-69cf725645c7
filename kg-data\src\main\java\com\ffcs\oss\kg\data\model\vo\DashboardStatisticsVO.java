package com.ffcs.oss.kg.data.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/8/5
 */
@Data
public class DashboardStatisticsVO {

    @ApiModelProperty("今日活跃用户数")
    private Integer todayActiveUsers;

    @ApiModelProperty("较昨日活跃用户变化（绝对数值）")
    private Integer activeUsersChange;

    @ApiModelProperty("今日语料总数")
    private Integer todayQaCount;

    @ApiModelProperty("较昨日语料数变化（绝对数值）")
    private Integer qaCountChange;

    @ApiModelProperty("任务语料总数（不统计变化）")
    private Integer taskQaCount;
}
