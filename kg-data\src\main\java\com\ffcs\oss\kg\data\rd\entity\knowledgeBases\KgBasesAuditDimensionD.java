package com.ffcs.oss.kg.data.rd.entity.knowledgeBases;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "kg_bases_audit_dimension_d")
public class KgBasesAuditDimensionD implements Serializable {

    private static final long serialVersionUID = 6858713794346643462L;

    /**
     * 知识审核维度
     */
    @TableId(value = "kg_bases_audit_dimension_id", type = IdType.AUTO)
    private Long kgBasesAuditDimensionId;

    /**
     * 维度名称
     */
    @TableField(value = "dimension_name")
    private String dimensionName;

    /**
     * 分值
     */
    @TableField(value = "score")
    private String score;

    /**
     * 评论
     */
    @TableField(value = "comment")
    private String comment;


    /**
     * 知识库主键
     */
    @TableField(value = "kg_knowledge_bases_id" )
    private Long kgKnowledgeBasesId;

    /**
     * 排列顺序
     */
    @TableField(value = "\"order\"")
    private Integer order;

    /**
     * 创建人
     */
    @TableField(value = "created_user_name")
    private String createdUserName;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_user_name")
    private String updatedUserName;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 是否通过
     */
    @TableField(value = "whether_pass")
    private Integer whetherPass;
	/**
	 * 满分值
	 */
	@TableField(value = "full_score_value")
    private String fullScoreValue;
	/**
	 * 审核标准
	 */
	@TableField(value = "review_standard")
    private String reviewStandard;

    /**
     * 是否通过 0为通过 1为不通过
     */
    @TableField(value = "single_whether_pass")
    private Integer singleWhetherPass;

    /**
     * 是否系统类型 是否系统 0 否  1是
     */
    @TableField(value = "is_system" )
    private String isSystem;

}
