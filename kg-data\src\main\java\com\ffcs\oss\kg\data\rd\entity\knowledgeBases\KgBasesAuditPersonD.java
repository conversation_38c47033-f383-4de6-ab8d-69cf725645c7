package com.ffcs.oss.kg.data.rd.entity.knowledgeBases;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Data
@TableName(value = "kg_bases_audit_person_d")
public class KgBasesAuditPersonD implements Serializable {

    private static final long serialVersionUID = 1478668369639473988L;

    /**
     * 审核人主键
     */
    @TableId(value = "kg_bases_audit_person_id", type = IdType.AUTO)
    private BigInteger kgBasesAuditPersonId;

    /**
     * 姓名
     */
    @TableField(value = "name")
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 电话号码
     */
    @TableField(value = "telephone")
    @NotBlank(message = "手机号码不能为空")
    private String telephone;

    /**
     * 区域
     */
    @TableField(value = "region_id")
    private Integer regionId;

    /**
     * 全量区域ID，用,拼接
     */
    @TableField(value = "full_region")
    @NotBlank(message = "区域不能为空")
    private String fullRegion;

    /**
     * 专业
     */
    @TableField(value = "major")
    @NotBlank(message = "专业不能为空")
    private String major;

    /**
     * 角色
     */
    @TableField(value = "role")
    @NotBlank(message = "角色不能为空")
    private String role;

    /**
     * 描述
     */
    @TableField(value = "descs")
    private String descs;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 创建人
     */
    @TableField(value = "created_user_name")
    private String createdUserName;

    /**
     * 最近更新人
     */
    @TableField(value = "updated_user_name")
    private String updatedUserName;

    /**
     * 最近更新时间
     */
    @TableField(value = "updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 是否有效
     */
    @TableField(value = "is_deleted")
    private String isDeleted;
    /**
     * 登录名
     */
    @TableField(value = "login_name")
    private String loginName;
    /**
     * 总的区域
     */
    @TableField(exist = false)
    private List<List<String>> totalFullRegionList;
    @TableField(value = "total_full_region")
    private String totalFullRegion;

    @TableField(value = "user_id")
    private Long userId;


}
