package com.ffcs.oss.kg.data.rd.entity.knowledgeBases;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ffcs.oss.kg.data.rd.entity.DtPermissionEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "知识库表")
@TableName("kg_knowledge_bases_d")
@KeySequence("kg_knowledge_bases_id_seq")
public class KnowledgeBasesEntity extends DtPermissionEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(name = "主键")
    @TableId(value = "kg_knowledge_bases_id", type = IdType.INPUT)
    private Long kgKnowledgeBasesId;

    /**
     * 区域;cs_sm_region_d的主键
     */
    @ApiModelProperty(name = "区域", notes = "cs_sm_region_d的主键")
    private Long region;

    /**
     * 专业领域
     */
    @ApiModelProperty(name = "专业领域")
    private String major;

    /**
     * 关联工单
     */
    @ApiModelProperty(name = "关联工单")
    private String relOrder;

    /**
     * 摘要
     */
    @ApiModelProperty(name = "摘要/中文")
    private String summary;

    /**
     * 关键词
     */
    @ApiModelProperty(name = "关键词/英文")
    private String operativeWord;

    /**
     * 业务/网络情况（纯文本）
     */
    @ApiModelProperty(name = "业务/网络情况（纯文本）/中文同义词")
    private String businessNetworkSituation;

    /**
     * 原因分析（纯文本）
     */
    @ApiModelProperty(name = "原因分析（纯文本）/中文定义")
    private String receiptReason;

    /**
     * 故障场景（纯文本）
     */
    @ApiModelProperty(name = "故障场景（纯文本）/英文定义")
    private String faultScene;

    /**
     * 处置过程（纯文本）
     */
    @ApiModelProperty(name = "处置过程（纯文本）/英文同义词")
    private String disposalProcess;

    /**
     * 思考与启示（纯文本）
     */
    @ApiModelProperty(name = "思考与启示（纯文本）")
    private String enlightenment;

    /**
     * 知识编写人
     */
    @ApiModelProperty(name = "知识编写人")
    private String author;

    /**
     * 审核状态
     */
    @ApiModelProperty(name = "审核状态")
    private String auditStatus;

    /**
     * 审核人
     */
    @ApiModelProperty(name = "审核人")
    private String reviewer;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    private String createdUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 最近更新人
     */
    @ApiModelProperty(name = "最近更新人")
    private String updatedUserName;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 删除标记（1-删除，0-有效）
     */
    @ApiModelProperty(name = "删除标记", notes = "1代表删除（数据无效），0代表未删除（有效）")
    private String isDeleted;

    /**
     * 查询次数
     */
    @ApiModelProperty(name = "查询次数")
    private Integer searchNumber;

    /**
     * 知识分类
     */
    @ApiModelProperty(name = "知识分类")
    private String knowledgeType;

    /**
     * 通用类别表ID
     */
    @ApiModelProperty(name = "通用类别表ID")
    private Long categoryId;

    /**
     * 密级
     */
    @ApiModelProperty(name = "密级")
    private String secretLevel;

    /**
     * 状态
     */
    @ApiModelProperty(name = "状态")
    private String state;

    /**
     * 置顶顺序
     */
    @ApiModelProperty(name = "置顶顺序")
    private String topUp;

    /**
     * 知识文件路径
     */
    @ApiModelProperty(name = "知识文件路径")
    private String knowledgeFilePath;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 查重结果
     */
    @ApiModelProperty(name = "查重结果")
    private BigDecimal checkRepeatResult;

    /**
     * 查重状态
     * 0: 未查重
     * 1: 查重中
     * 2: 查重成功
     * 3: 查重失败
     */
    @ApiModelProperty(name = "查重状态", notes = "0-未查重，1-查重中，2-查重成功，3-查重失败")
    private Integer checkRepeatStatus;

    /**
     * 查重重试次数
     */
    @ApiModelProperty(name = "查重重试次数")
    private Integer checkRepeatRetryCount;

    /**
     * 查重失败原因
     */
    @ApiModelProperty(name = "查重失败原因")
    private String checkRepeatFailReason;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;

    /**
     * 变更时间
     */
    @ApiModelProperty("变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    /**
     * 知识详情
     */
    @ApiModelProperty(name = "知识详情")
    private String knowledgeDetails;

    /**
     * 知识详情富文本
     */
    @ApiModelProperty(name = "知识详情富文本")
    private String knowledgeDetailsRt;

    /**
     * 相似知识标题
     */
    @ApiModelProperty(name = "相似知识标题")
    private String similarKnowledgeBases;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 是否首次下线（0-否，1-是）
     */
    @ApiModelProperty(name = "是否首次下线", notes = "0-否，1-是")
    private Integer whetherFirstOffline;

    /**
     * 点击次数
     */
    @ApiModelProperty(name = "点击次数")
    private Integer clickCount;

    /**
     * 同组权限（1-可编辑，2-可查看，3-无权限）
     */
    @ApiModelProperty(name = "同组权限", notes = "1可编辑|2可查看|3无权限")
    private String sameGroupPermission;

    /**
     * 其他人权限（1-可编辑，2-可查看，3-无权限）
     */
    @ApiModelProperty(name = "其他人权限", notes = "1可编辑|2可查看|3无权限")
    private String otherPersonPermission;

    /**
     * 权限类型（1-配置中心，2-分类权限，3-数据权限）
     */
    @ApiModelProperty(name = "权限类型", notes = "1配置中心|2分类权限|3数据权限")
    private String permissionType;

    /**
     * 知识名称
     */
    @ApiModelProperty(name = "知识名称/术语名称")
    private String knowledgeName;

    /**
     * 知识来源
     */
    @ApiModelProperty(name = "知识来源")
    private String knowledgeOrigin;

    /**
     * 流程场景
     */
    @ApiModelProperty(name = "流程场景")
    private String flowScene;

    /**
     * 应用场景
     */
    @ApiModelProperty(name = "应用场景")
    private String applicationScene;

    /**
     * 文档类型
     */
    @ApiModelProperty(name = "文档类型")
    private String documentType;

    /**
     * 文档格式
     */
    @ApiModelProperty(name = "文档格式")
    private String documentFormat;

    /**
     * 是否同步集团
     */
    @ApiModelProperty(name = "同步集团/是否通信信息领域")
    private String isSync;

    /**
     * 人力资源名
     */
    @ApiModelProperty(name = "人力资源名")
    private String jtAuthor;

    /**
     * 上报时间
     */
    @ApiModelProperty(name = "上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 上报状态：集团未上报：1 已上报集团：2 集团已下线：3 集团已删除：4
     */
    @ApiModelProperty(name = "上报状态", notes = "集团未上报：1 已上报集团：2 集团已下线：3 集团已删除：4")
    private String reportStatus;


    @ApiModelProperty(name = "上报情况")
    private String reportDescription;

    /**
     * 上报批次号
     */
    @ApiModelProperty(name = "上报批次号")
    private String reportBatchNo;

    /**
     * 公开范围
     */
    @ApiModelProperty(name = "公开范围")
    private String publicity;

    /**
     * 有效期
     */
    @ApiModelProperty(name = "有效期")
    private String periodValidity;

    @ApiModelProperty(name = "生命周期")
    private String lifeCycle;

    @ApiModelProperty(name = "word转pdf")
    private String wordToPdf;

    @ApiModelProperty(name = "组织机构")
    private String institution;

    @ApiModelProperty(name = "问题分类")
    private String questionClassify;

    @ApiModelProperty(name = "问题")
    private String question;

    @ApiModelProperty(name = "问题回答")
    private String answer;

    @ApiModelProperty(name = "1为知识文档 2为问答对 3为专业词汇")
    private String basesType;


    /**
     * 相似知识标题
     */
    @ApiModelProperty(name = "文件大小")
    private Long filesize;

    /**
     * 类别名
     */
    @ApiModelProperty(name = "类别名")
    @TableField(exist = false)
    private String categoryName;

    /**
     * 创建分类上传文件时的路径
     */
    @ApiModelProperty(name = "创建分类上传文件时的路径")
    @TableField(exist = false)
    private String categoryFilePath;

    /**
     * 全区域
     */
    @ApiModelProperty(name = "全区域")
    @TableField(exist = false)
    private String fullRegion;

    /**
     * 区域名称
     */
    @ApiModelProperty(name = "区域名称")
    @TableField(exist = false)
    private String regionName;

    /**
     * 上传进度
     */
    @ApiModelProperty(name = "上传进度")
    @TableField(exist = false)
    private int uploadProgress;

    /**
     * 上传结果
     */
    @ApiModelProperty(name = "上传结果")
    @TableField(exist = false)
    private String uploadResult;

    @TableField(exist = false)
    @ApiModelProperty(name = "文件内容")
    private String fileContent;

    @ApiModelProperty(name = "相似文档")
    @TableField(exist = false)
    private  List< SimilarFileInfo> similarFileInfos;

    @ApiModelProperty(name = "是否重复")
    @TableField(exist = false)
    private  boolean whetherItsRepetitive = false;


    @ApiModelProperty(name = "文件大小展示")
    @TableField(exist = false)
    private String filesizeShow;

    /**
     * 术语/缩略语/专业名词
     */
    @ApiModelProperty(name = "术语/缩略语/专业名词")
    private String term;
    
    /**
     * 中文
     */
    @ApiModelProperty(name = "中文")
    private String chinese;
    
    /**
     * 英文
     */
    @ApiModelProperty(name = "英文")
    private String english;
    
    /**
     * 中文同义词
     */
    @ApiModelProperty(name = "中文同义词")
    private String chineseSynonym;
    
    /**
     * 英文同义词
     */
    @ApiModelProperty(name = "英文同义词")
    private String englishSynonym;
    
    /**
     * 中文定义
     */
    @ApiModelProperty(name = "中文定义")
    private String chineseDefinition;
    
    /**
     * 英文定义
     */
    @ApiModelProperty(name = "英文定义")
    private String englishDefinition;
    
    /**
     * 是否通信信息领域
     */
    @ApiModelProperty(name = "是否通信信息领域")
    private String isTelecomInfoField;


    @TableField(exist = false)
    @ApiModelProperty(name = "导入的错误信息")
    private String importError;
}