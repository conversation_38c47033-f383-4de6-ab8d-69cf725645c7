package com.ffcs.oss.kg.data.rd.entity.knowledgeBases;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ffcs.oss.kg.data.rd.entity.DtPermissionEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 专业词汇实体类
 */
@Data
@ApiModel(value = "专业词汇表")
@TableName("kg_professional_terms_d")
@KeySequence("kg_professional_terms_id_seq")
public class ProfessionalTermEntity extends DtPermissionEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(name = "主键")
    @TableId(value = "kg_professional_terms_id", type = IdType.AUTO)
    private Long kgProfessionalTermsId;

    /**
     * 术语/缩略语/专业名词
     */
    @ApiModelProperty(name = "术语/缩略语/专业名词")
    @TableField("term")
    private String term;

    /**
     * 中文定义
     */
    @ApiModelProperty(name = "中文定义")
    @TableField("chinese_definition")
    private String chineseDefinition;

    /**
     * 英文定义
     */
    @ApiModelProperty(name = "英文定义")
    @TableField("english_definition")
    private String englishDefinition;

    /**
     * 中文
     */
    @ApiModelProperty(name = "中文")
    @TableField("chinese")
    private String chinese;

    /**
     * 中文同义词
     */
    @ApiModelProperty(name = "中文同义词")
    @TableField("chinese_synonym")
    private String chineseSynonym;

    /**
     * 英文
     */
    @ApiModelProperty(name = "英文")
    @TableField("english")
    private String english;

    /**
     * 英文同义词
     */
    @ApiModelProperty(name = "英文同义词")
    @TableField("english_synonym")
    private String englishSynonym;

    /**
     * 专业领域
     */
    @ApiModelProperty(name = "专业领域")
    @TableField("major")
    private String major;

    /**
     * 应用场景
     */
    @ApiModelProperty(name = "应用场景")
    @TableField("application_scene")
    private String applicationScene;

    /**
     * 是否通信信息领域
     */
    @ApiModelProperty(name = "是否通信信息领域")
    @TableField("is_telecom_info_field")
    private String isTelecomInfoField;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    @TableField("created_user_name")
    private String createdUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("created_time")
    private Date createdTime;

    /**
     * 最近更新人
     */
    @ApiModelProperty(name = "最近更新人")
    @TableField("updated_user_name")
    private String updatedUserName;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("updated_time")
    private Date updatedTime;

    /**
     * 删除标记（1-删除，0-有效）
     */
    @ApiModelProperty(name = "删除标记", notes = "1代表删除（数据无效），0代表未删除（有效）")
    @TableField("is_deleted")
    private String isDeleted;

    /**
     * 数据记录状态
     */
    @ApiModelProperty(name = "数据记录状态")
    @TableField("state")
    private String state;

    /**
     * 审核状态
     */
    @ApiModelProperty(name = "审核状态")
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 是否提交标志，true表示提交操作，false表示保存操作
     */
    @ApiModelProperty(name = "是否提交标志，true表示提交操作，false表示保存操作")
    @TableField(exist = false)
    private Boolean submitFlag;

    @TableField("region")
    private String region;
    @TableField("reviewer")
    private String reviewer;
    @TableField("search_number")
    private Integer searchNumber;

    @TableField("audit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    @TableField("release_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;

    @TableField("change_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    @TableField("submit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    @TableField("whether_first_offline")
    private Integer whetherFirstOffline;

    @TableField("click_count")
    private Integer clickCount;

    /**
     * 上报状态（0-未上报，1-已上报，2-上报失败）
     */
    @ApiModelProperty(value = "上报状态", notes = "0-未上报，1-已上报，2-上报失败")
    @TableField(value = "report_status")
    private String reportStatus;

    /**
     * 导出状态（0-未导出，1-已导出）
     */
    @ApiModelProperty(value = "导出状态（0-未导出，1-已导出）")
    @TableField(value = "export_status")
    private String exportStatus;

    /**
     * 上报情况说明
     */
    @ApiModelProperty(name = "上报情况说明")
    @TableField("report_description")
    private String reportDescription;

    /**
     * 上报时间
     */
    @ApiModelProperty(name = "上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("report_time")
    private Date reportTime;

} 