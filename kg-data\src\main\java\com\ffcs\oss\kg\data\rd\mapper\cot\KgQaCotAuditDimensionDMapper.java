package com.ffcs.oss.kg.data.rd.mapper.cot;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.KgQaCotAuditDimensionD;
import com.ffcs.oss.kg.data.rd.entity.qa.KgQaPairAuditDimensionD;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/8/4
 */
@Mapper
public interface KgQaCotAuditDimensionDMapper extends BaseMapper<KgQaCotAuditDimensionD> {

    /**
     * 查询问答COT的最大排序号
     *
     * @param qaCotId 问答COT
     * @return 最大排序号
     */
    @Select("SELECT MAX(order_num) FROM kg_qa_cot_audit_dimension_d WHERE qa_cot_id = #{qaCotId}")
    Integer selectMaxOrderNum(@Param("qaCotId") Long qaCotId);
}
