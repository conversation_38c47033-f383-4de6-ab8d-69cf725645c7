package com.ffcs.oss.kg.data.rd.mapper.cot;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.GetQaCotsEvt;
import com.ffcs.oss.kg.data.model.vm.know.MajorStatisticsVm;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 问答CotMapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface KgQaCotDMapper extends BaseMapper<KgQaCotD> {

    /**
     * 根据高级查询条件查询问答Cot列表
     *
     * @param evt 查询条件
     * @return 问答Cot列表
     */
    List<KgQaCotD> selectQaCotListByHighLevel(GetQaCotsEvt evt);

    /**
     * 查询专业统计
     *
     * @param qaCotIds 问答CotID列表
     * @return 专业总数
     */
    Integer selectMajorStatistics(@Param("qaCotIds") List<Long> qaCotIds);

    /**
     * 查询所有专业统计信息
     *
     * @param qaCotIds 问答CotID列表
     * @return 专业统计列表
     */
    List<MajorStatisticsVm> selectAllMajorStatistics(@Param("qaCotIds") List<Long> qaCotIds);

    /**
     * 根据问题内容检查是否存在重复
     *
     * @param question 问题内容
     * @return 重复数量
     */
    Integer checkQuestionRepeat(@Param("question") String question);

    /**
     * 批量检查问题是否重复
     *
     * @param questions 问题列表
     * @return 已存在的问题列表
     */
    List<String> batchCheckQuestionRepeat(@Param("questions") List<String> questions);

    /**
     * 批量插入问答Cot
     *
     * @param qaCots 问答Cot列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<KgQaCotD> qaCots);


    @MapKey("question")
    Map<String, KgQaCotD> checkQuestionsRepeat(@Param("questions") List<String> questions);


    @Select("SELECT COUNT(DISTINCT created_user_name) FROM kg.kg_qa_cot_d " +
            "WHERE is_deleted = '0' AND created_time::date = CURRENT_DATE")
    int countDistinctCreatedUsersToday();

    @Select("SELECT COUNT(DISTINCT created_user_name) FROM kg.kg_qa_cot_d " +
            "WHERE is_deleted = '0' AND created_time::date = CURRENT_DATE - INTERVAL '1 day'")
    int countDistinctCreatedUsersYesterday();

    @Select("SELECT COUNT(*) FROM kg.kg_qa_cot_d " +
            "WHERE is_deleted = '0' ")
    int countCreatedToday();

    @Select("SELECT COUNT(*) FROM kg.kg_qa_cot_d " +
            "WHERE is_deleted = '0' AND created_time::date = CURRENT_DATE - INTERVAL '1 day'")
    int countCreatedYesterday();
} 