package com.ffcs.oss.kg.data.rd.mapper.knowledgeBases;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.GetBasesEvt;
import com.ffcs.oss.kg.data.model.vm.bases.MajorStatisticsVm;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesSpaceC;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.math.BigDecimal;

@Mapper
public interface ModelKnowledgeBasesDMapper extends BaseMapper<KnowledgeBasesEntity> {
    void updateBykgBasesId(@Param("id") Long id);

    List<KnowledgeBasesEntity> selectBasesListByHighLevel(@Param("evt") GetBasesEvt evt);

    List<KgBasesSpaceC> selectBasesListByHighLevels(@Param("evt") GetBasesEvt evt);

    List<KgBasesSpaceC> selectBasesListByHighLevelss(@Param("evt") GetBasesEvt evt);

    List<KnowledgeBasesEntity> getTop5(@Param("evt") GetBasesEvt evt);

    List<MajorStatisticsVm> selectAllMajorStatistics(@Param("kgbsIdsList") List<Long> kgbsIdsList);

    Integer selectMajorStatistics(@Param("kgbsIdsList") List<Long> kgbsIdsList);
    
    /**
     * 查询没有查重结果的知识库ID列表
     * 
     * @param startTime 开始时间，可以为空
     * @param endTime 结束时间，可以为空
     * @return 无查重结果的知识库ID列表
     */
    List<Long> selectIdsWithNoCheckResult(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 批量更新查重结果
     * 
     * @param entities 要更新的实体列表
     * @return 更新的记录数
     */
    int batchUpdateCheckResult(@Param("entities") List<KnowledgeBasesEntity> entities);
    
    /**
     * 查询待上报集团的知识库数据
     * 
     * @param limit 查询数量限制，可以为空
     * @return 待上报的知识库列表
     */
    List<KnowledgeBasesEntity> selectPendingReportKnowledgeBases(@Param("limit") Integer limit);
    
    /**
     * 查询指定时间前仍在查重中的知识库ID列表（用于定时任务重新处理）
     * 
     * @param beforeTime 时间阈值，查询此时间之前创建且仍在查重中的数据
     * @param limit 查询数量限制
     * @return 仍在查重中的知识库ID列表
     */
    List<Long> selectStuckCheckingIds(@Param("beforeTime") LocalDateTime beforeTime, @Param("limit") Integer limit);
    
    /**
     * 查询指定时间前仍在查重中的知识库实体列表（用于定时任务重新处理）
     * 
     * @param beforeTime 时间阈值，查询此时间之前创建且仍在查重中的数据
     * @param limit 查询数量限制
     * @return 仍在查重中的知识库实体列表
     */
    List<KnowledgeBasesEntity> selectStuckCheckingEntities(@Param("beforeTime") LocalDateTime beforeTime, @Param("limit") Integer limit);
    
    /**
     * 查询需要重新查重的知识库ID列表（优化版本）
     * 查询未查重或查重失败且重试次数未超限的数据
     * 
     * @param beforeTime 时间阈值，查询此时间之前创建的数据
     * @param maxRetryCount 最大重试次数
     * @param limit 查询数量限制
     * @return 需要重新查重的知识库ID列表
     */
    List<Long> selectRetryableCheckingIds(@Param("beforeTime") LocalDateTime beforeTime, 
                                         @Param("maxRetryCount") Integer maxRetryCount, 
                                         @Param("limit") Integer limit);
    
    /**
     * 查询需要重新查重的知识库实体列表（优化版本）
     * 
     * @param beforeTime 时间阈值
     * @param maxRetryCount 最大重试次数
     * @param limit 查询数量限制
     * @return 需要重新查重的知识库实体列表
     */
    List<KnowledgeBasesEntity> selectRetryableCheckingEntities(@Param("beforeTime") LocalDateTime beforeTime,
                                                              @Param("maxRetryCount") Integer maxRetryCount,
                                                              @Param("limit") Integer limit);
    
    /**
     * 批量更新查重状态
     * 
     * @param entities 要更新的实体列表（包含ID和状态信息）
     * @return 更新的记录数
     */
    int batchUpdateCheckStatus(@Param("entities") List<KnowledgeBasesEntity> entities);
    
    /**
     * 更新查重状态为查重中
     * 
     * @param ids 知识库ID列表
     * @return 更新的记录数
     */
    int updateCheckStatusToChecking(@Param("ids") List<Long> ids);
    
    /**
     * 更新查重状态为成功
     * 
     * @param id 知识库ID
     * @param checkRepeatResult 查重结果
     * @return 更新的记录数
     */
    int updateCheckStatusToSuccess(@Param("id") Long id, @Param("checkRepeatResult") BigDecimal checkRepeatResult);
    
    /**
     * 更新查重状态为失败
     * 
     * @param id 知识库ID
     * @param failReason 失败原因
     * @param retryCount 重试次数
     * @return 更新的记录数
     */
    int updateCheckStatusToFailed(@Param("id") Long id, @Param("failReason") String failReason, @Param("retryCount") Integer retryCount);

    /**
     * 查询待上报集团的知识库数据（支持动态文件格式）
     * 
     * @param limit 最大查询数量，null表示不限制
     * @param supportedFormats 支持的文件格式列表
     * @return 待上报的知识库列表
     */
    List<KnowledgeBasesEntity> selectPendingReportKnowledgeBasesWithFormats(@Param("limit") Integer limit, @Param("supportedFormats") List<String> supportedFormats);
    
    /**
     * 批量更新知识库上报状态
     * 
     * @param knowledgeBasesIds 知识库ID列表
     * @param reportStatus 上报状态
     * @param reportTime 上报时间
     * @param batchNo 批次号
     * @param updatedTime 更新时间
     * @return 更新的记录数
     */
    int batchUpdateReportStatus(@Param("knowledgeBasesIds") List<Long> knowledgeBasesIds,
                               @Param("reportStatus") String reportStatus,
                               @Param("reportTime") Date reportTime,
                               @Param("batchNo") String batchNo, @Param("isSync") String isSync,
                               @Param("updatedTime") Date updatedTime,@Param("reportDescription") String reportDescription);
}
