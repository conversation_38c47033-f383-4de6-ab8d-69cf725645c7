<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ffcs.oss.kg.data.rd.mapper.cot.KgQaCotDMapper">

    <!-- 根据高级查询条件查询问答对列表 -->
    <select id="selectQaCotListByHighLevel" resultType="com.ffcs.oss.kg.data.model.entity.KgQaCotD">
        SELECT
            *
        FROM
            kg.kg_qa_cot_d
        <where>
            is_deleted = '0'
            <if test="kgQaCotIds != null and kgQaCotIds.size() > 0">
                AND kg_qa_cot_id IN
                <foreach collection="kgQaCotIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="kgQaCotId != null">
                AND kg_qa_cot_id = #{kgQaCotId}
            </if>
            <if test="kgKnowledgeBasesId != null">
                AND kg_knowledge_bases_id = #{kgKnowledgeBasesId}
            </if>
            <if test="institution != null and institution != ''">
                AND institution ILIKE CONCAT('%', #{institution}, '%')
            </if>
            <if test="author != null and author != ''">
                AND author ILIKE CONCAT('%', #{author}, '%')
            </if>


            <if test="major != null and major.size() > 0">
                AND (
                <foreach collection="major" item="majorItem" separator=" OR ">
                    major  ~* ('(^|,)' || #{majorItem} || '(,|$)')
                </foreach>
                )
            </if>


            <if test="createdUserName!=null and createdUserName!=''">
                and created_user_name =#{createdUserName}
            </if>

            <if test="reviewer!=null and reviewer!=''">
                and reviewer =#{reviewer}
            </if>


            <if test="reportStatusList!=null  and not reportStatusList.isEmpty()">
                and report_status in
                <foreach collection="reportStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="exportStatusList!=null  and not exportStatusList.isEmpty()">
                and export_status in
                <foreach collection="exportStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>


            <if test="knowledgeOriginList!=null  and not knowledgeOriginList.isEmpty()">
                and (
                <foreach collection="knowledgeOriginList"  item="item" separator=" or ">
                    knowledge_origin  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>

            <if test="publicityList!=null and not publicityList.isEmpty()">
                and publicity
                in
                <foreach collection="publicityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="periodValidityList!=null and not periodValidityList.isEmpty()">
                and period_validity
                in
                <foreach collection="periodValidityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>


            <if test="lifeCycleList != null and lifeCycleList.size() > 0">
                and life_cycle
                in
                <foreach collection="lifeCycleList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="applicationScene != null and applicationScene != ''">
                AND application_scene = #{applicationScene}
            </if>
            <if test="applicationSceneList != null and applicationSceneList.size() > 0">
                AND (
                <foreach collection="applicationSceneList" item="sceneItem" separator=" OR ">
                    application_scene  ~* ('(^|,)' || #{sceneItem} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="knowledgeOrigin != null and knowledgeOrigin != ''">
                AND knowledge_origin = #{knowledgeOrigin}
            </if>
            <if test="flowScene != null and flowScene != ''">
                AND flow_scene = #{flowScene}
            </if>
            <if test="flowSceneList != null and flowSceneList.size() > 0">
                AND (
                <foreach collection="flowSceneList" item="flowSceneItem" separator=" OR ">
                    flow_scene  ~* ('(^|,)' || #{flowSceneItem} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="questionClassify != null and questionClassify != ''">
                AND question_classify = #{questionClassify}
            </if>
            <if test="questionClassifyList != null and questionClassifyList.size() > 0">
                AND (
                <foreach collection="questionClassifyList" item="classifyItem" separator=" OR ">
                    question_classify  ~* ('(^|,)' || #{classifyItem} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="questionKeyword != null and questionKeyword != ''">
                AND (
                    question ILIKE CONCAT('%', #{questionKeyword}, '%')
                    OR answer ILIKE CONCAT('%', #{questionKeyword}, '%')
                )
            </if>

            <if test="state != null and state.size() > 0">
                AND state IN
                <foreach collection="state" item="stateItem" open="(" separator="," close=")">
                    #{stateItem}
                </foreach>
            </if>
            <if test="region != null">
                AND region = #{region}
            </if>
            <if test="regionList != null and regionList.size() > 0">
                AND region IN
                <foreach collection="regionList" item="regionItem" open="(" separator="," close=")">
                    #{regionItem}
                </foreach>
            </if>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
            <if test="startTime != null">
                AND created_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_time &lt;= #{endTime}
            </if>
            <if test="stateMajor != null and stateMajor.size() > 0">
                AND (
                <foreach collection="stateMajor" item="majorItem" separator=" OR ">
                    major  ~* ('(^|,)' || #{majorItem} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="stateRegion != null and stateRegion.size() > 0">
                AND region IN
                <foreach collection="stateRegion" item="regionItem" open="(" separator="," close=")">
                    #{regionItem}
                </foreach>
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="whetherAuditOrder != null and whetherAuditOrder">
                submit_time ASC
            </when>
            <otherwise>
                updated_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询专业统计 -->
    <select id="selectMajorStatistics" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT major)
        FROM
            kg.kg_qa_cot_d
        <where>
            is_deleted = '0'
            <if test="qaCotIds != null and qaCotIds.size() > 0">
                AND kg_qa_cot_id IN
                <foreach collection="qaCotIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询所有专业统计信息 -->
    <select id="selectAllMajorStatistics" resultType="com.ffcs.oss.kg.data.model.vm.know.MajorStatisticsVm">
        SELECT
            major AS majorName,
            COUNT(1) AS majorCount
        FROM
            kg.kg_qa_cot_d
        <where>
            is_deleted = '0'
            <if test="qaCotIds != null and qaCotIds.size() > 0">
                AND kg_qa_cot_id IN
                <foreach collection="qaCotIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY major
        ORDER BY majorCount DESC
    </select>

    <!-- 根据问题内容检查是否存在重复 -->
    <select id="checkQuestionRepeat" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            kg.kg_qa_cot_d
        WHERE
            is_deleted = '0'
            AND question = #{question}
    </select>

    <!-- 批量检查问题是否重复 -->
    <select id="batchCheckQuestionRepeat" resultType="java.lang.String">
        SELECT question 
        FROM kg.kg_qa_cot_d 
        WHERE is_deleted = '0' 
        AND question IN
        <foreach collection="questions" item="question" open="(" separator="," close=")">
            #{question}
        </foreach>
    </select>

    <!-- 批量插入问答对 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="kgQaCotId">
        INSERT INTO kg.kg_qa_cot_d (
            kg_knowledge_bases_id,kg_qa_pair_id, institution, author, major, application_scene,
            knowledge_origin, flow_scene, publicity, period_validity, life_cycle,
            question_classify, question, answer,thought_process, category_id, region,
            is_deleted, search_number, state, audit_status, click_count,
            tenant_code, created_user_name, created_time, updated_user_name, updated_time,permission_type,
            report_status,export_status,report_description,report_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.kgKnowledgeBasesId},
                #{item.kgQaPairId},
                #{item.institution},
                #{item.author},
                #{item.major},
                #{item.applicationScene},
                #{item.knowledgeOrigin},
                #{item.flowScene},
                #{item.publicity},
                #{item.periodValidity},
                #{item.lifeCycle},
                #{item.questionClassify},
                #{item.question},
                #{item.answer},
                #{item.thoughtProcess},
                #{item.categoryId},
                #{item.region},
                #{item.isDeleted},
                #{item.searchNumber},
                #{item.state},
                #{item.auditStatus},
                #{item.clickCount},
                #{item.tenantCode},
                #{item.createdUserName},
                #{item.createdTime},
                #{item.updatedUserName},
                #{item.updatedTime},
                #{item.permissionType},
                #{item.reportStatus},
                #{item.exportStatus},
                #{item.reportDescription},
                #{item.reportTime}
            )
        </foreach>
    </insert>


    <select id="checkQuestionsRepeat" resultType="com.ffcs.oss.kg.data.model.entity.KgQaCotD">
        SELECT
        question,
        kg_qa_cot_id
        FROM kg_qa_cot_d
        WHERE question IN
        <foreach collection="questions" item="q" open="(" separator="," close=")">
            #{q}
        </foreach>
    </select>
</mapper> 