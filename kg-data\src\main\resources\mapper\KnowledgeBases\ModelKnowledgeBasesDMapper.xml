<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.ModelKnowledgeBasesDMapper">

    <update id="updateBykgBasesId">
        UPDATE kg_knowledge_bases_d set is_deleted ='1' WHERE kg_knowledge_bases_id=#{id}
    </update>

    <select id="selectBasesListByHighLevel" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        select *,
        P.*
        from kg_knowledge_bases_d bases
        LEFT JOIN (SELECT * FROM cm_application_permission WHERE application_data_type_code = #{evt.applicationDataTypeCode}) P ON bases.kg_knowledge_bases_id = P.application_id
        <where>
            1=1
            <if test="evt.operativeWordList != null and not evt.operativeWordList.isEmpty()">
                and(  <foreach collection="evt.operativeWordList" item="item" separator=" and ">
                bases.operative_word ~ ('(^|,)' || #{item, jdbcType=VARCHAR} || '(,|$)')
            </foreach>
                )
            </if>
            <if test="evt.kgKnowledgeBasesIds!=null and not evt.kgKnowledgeBasesIds.isEmpty()">
                and bases.kg_knowledge_bases_id
                in
                <foreach collection="evt.kgKnowledgeBasesIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <!--权限相关-->
            <if test="evt.stateMajor!=null  and not evt.stateMajor.isEmpty()">
                and (
                <foreach collection="evt.stateMajor"  item="item" separator=" or ">
                    bases.major  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>

            <if test="evt.stateRegion!=null and not evt.stateRegion.isEmpty()">
                and bases.region
                in
                <foreach collection="evt.stateRegion" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <!--权限相关-->

            <!--展示 文档或者问答对-->
            <if test="evt.basesType!=null and evt.basesType!=''">
                and bases_type = #{evt.basesType}
            </if>
            <if test="evt.categoryId!=null">
                and category_id in (
                select
                a.category_id
                from (
                select category_id, full_category_id, unnest(string_to_array(full_category_id, ','))::numeric as hasRefId from kg_common_category_c
                where is_del = '0'
                ) a where a.hasRefId = #{evt.categoryId}
                )
            </if>
            <if test="evt.knowledgeName!=null and evt.knowledgeName!=''">
                and bases.knowledge_name like '%' || #{evt.knowledgeName, jdbcType=VARCHAR} || '%'
            </if>
            <if test="evt.question!=null and evt.question!=''">
                and bases.question like '%' || #{evt.question, jdbcType=VARCHAR} || '%'
            </if>
            <if test="evt.region!=null and not evt.region.isEmpty()">
                and bases.region
                in
                <foreach collection="evt.region" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <!-- 文档类型查询（document_type）-->
            <if test="evt.documentTypeList!=null  and not evt.documentTypeList.isEmpty()">
                and (
                <foreach collection="evt.documentTypeList"  item="item" separator=" or ">
                    bases.document_type  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>

            <!-- 文档格式查询（document_format）-->
            <if test="evt.documentFormatList!=null  and not evt.documentFormatList.isEmpty()">
                and (
                <foreach collection="evt.documentFormatList"  item="item" separator=" or ">
                    bases.document_format  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>

            <!-- 应用场景查询（application_scene）-->
            <if test="evt.applicationSceneList!=null  and not evt.applicationSceneList.isEmpty()">
                and (
                <foreach collection="evt.applicationSceneList"  item="item" separator=" or ">
                    bases.application_scene  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.knowledgeOriginList!=null  and not evt.knowledgeOriginList.isEmpty()">
                and (
                <foreach collection="evt.knowledgeOriginList"  item="item" separator=" or ">
                    bases.knowledge_origin  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.state!=null and not evt.state.isEmpty()">
                and bases.state
                in
                <foreach collection="evt.state" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
<!--            <if test="evt.state!=null  and not evt.state.isEmpty()">-->
<!--                and (-->
<!--                <foreach collection="evt.state"  item="item" separator=" or ">-->
<!--                    #{item} = ANY(string_to_array(bases.state,  ','))-->
<!--                </foreach>-->
<!--                )-->
<!--            </if>-->
            <if test="evt.flowSceneList!=null  and not evt.flowSceneList.isEmpty()">
                and (
                <foreach collection="evt.flowSceneList"  item="item" separator=" or ">
                    bases.flow_scene  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.major!=null  and not evt.major.isEmpty()">
                and (
                <foreach collection="evt.major"  item="item" separator=" or ">
                    bases.major  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.secretLevelList!=null and not evt.secretLevelList.isEmpty()">
                and bases.secret_level
                in
                <foreach collection="evt.secretLevelList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="evt.lifeCycleList!=null and not evt.lifeCycleList.isEmpty()">
                and bases.life_cycle
                in
                <foreach collection="evt.lifeCycleList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="evt.periodValidityList!=null and not evt.periodValidityList.isEmpty()">
                and bases.period_validity
                in
                <foreach collection="evt.periodValidityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="evt.publicityList!=null and not evt.publicityList.isEmpty()">
                and bases.publicity
                in
                <foreach collection="evt.publicityList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="evt.reportStatusList!=null and not evt.reportStatusList.isEmpty()">
                and bases.report_status
                in
                <foreach collection="evt.reportStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="evt.authorList!=null  and not evt.authorList.isEmpty()">
                and (
                <foreach collection="evt.authorList"  item="item" separator=" or ">
                    bases.author  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.createdUserName!=null and evt.createdUserName!=''">
                and bases.created_user_name =#{evt.createdUserName}
            </if>
            <if test="evt.reviewer!=null and evt.reviewer!=''">
                and bases.reviewer =#{evt.reviewer}
            </if>

            <if test="evt.startTime!=null and evt.endTime!=null">
                and created_time &gt;=#{evt.startTime}::timestamp
                and created_time &lt;=#{evt.endTime}::timestamp
            </if>
            <if test="evt.userType != 1">
                AND( bases.created_user_name = #{evt.beforeOneUser}
                OR (
                ( bases.permission_type != '4'
                AND
                (
                (bases.created_user_name IN
                <foreach collection="evt.perUserNames" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>AND bases.same_group_permission &lt; '3')
                OR (bases.created_user_name NOT IN
                <foreach collection="evt.perUserNames" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                AND bases.
                other_person_permission &lt; '3')
                )
                )
                OR
                (
                bases.permission_type = '4'
                AND (
                P.view_permission = '0'
                OR (P.view_permission = '1' AND P.view_group_ids &amp;&amp;
                ARRAY[
                <foreach item="id" collection="evt.groupIds" separator=",">
                    #{id}
                </foreach>
                ]::bigint[]
                )
                OR P.edit_permission = '0'
                OR (P.edit_permission = '1' AND P.edit_group_ids &amp;&amp;
                ARRAY[
                <foreach item="id" collection="evt.groupIds" separator=",">
                    #{id}
                </foreach>
                ]::bigint[]
                )
                )
                )
                )
                )
            </if>
            and bases.is_deleted='0'
        </where>
        <choose>
            <when test="evt.whetherClickCountSort == 1">
                ORDER BY click_count DESC, bases.top_up DESC, bases.updated_time DESC, bases.created_time DESC
            </when>
            <when test="evt.whetherClickCountSort == 0">
                ORDER BY bases.top_up DESC, bases.updated_time DESC, bases.created_time DESC
            </when>
            <otherwise>
                ORDER BY bases.updated_time DESC, bases.created_time DESC
            </otherwise>
        </choose>

    </select>
    <select id="selectListByCategoryId" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        select *
        from kg_raw_case_d
        where category_id in (
            select
                a.category_id
            from (
                     select category_id, full_category_id, unnest(string_to_array(full_category_id, ','))::numeric as hasRefId from kg_common_category_c
                     where is_del = '0'
                 ) a where a.hasRefId = #{categoryId}
        )
          and is_deleted = '0'
          and case_file_path is not null
    </select>
    <select id="selectAllMajorStatistics" resultType="com.ffcs.oss.kg.data.model.vm.bases.MajorStatisticsVm">
        SELECT
        d.bdp_dir_id AS code_value,
        d.code_name,
        COUNT(DISTINCT split_data.kg_knowledge_bases_id) AS nums  -- 避免重复计数 --
        FROM
        kg_dictionary_c d
        -- 横向展开知识库表的多选专业字段 --
        LEFT JOIN (
        SELECT
        kg_knowledge_bases_id,
        TRIM(unnest(string_to_array(c.major, ',')))::numeric AS major_code  -- 拆分并转换类型 --
        FROM
        kg_knowledge_bases_d c
        WHERE
        c.is_deleted = '0'
        <if test="kgbsIdsList != null and not kgbsIdsList.isEmpty()">
            AND c.kg_knowledge_bases_id IN
            <foreach collection="kgbsIdsList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) split_data ON d.bdp_dir_id = split_data.major_code
        WHERE
        d.code_type = 'bases_major_area'
        GROUP BY
        d.bdp_dir_id,
        d.code_name,
        d.order_no  -- 保持原有分组逻辑 --
        ORDER BY
        d.order_no
    </select>
    <select id="selectMajorStatistics" resultType="java.lang.Integer">
        SELECT
            SUM(counts) AS major_total
        FROM (
                 SELECT
                     COUNT(major) AS counts
                 FROM
                     kg_knowledge_bases_d c
                 WHERE
                     major IS NOT null
                   and is_deleted='0'
            <if test="kgbsIdsList!=null and not kgbsIdsList.isEmpty()">
                and c.kg_knowledge_bases_id in
                <foreach collection="kgbsIdsList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
                 GROUP BY
                     major
             ) AS subquery
    </select>
    <select id="getTop5" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        select *
        from kg_knowledge_bases_d bases
        <where>
            1=1
            <if test="evt.operativeWordList != null and not evt.operativeWordList.isEmpty()">
                and(  <foreach collection="evt.operativeWordList" item="item" separator=" and ">
                bases.operative_word ~ ('(^|,)' || #{item, jdbcType=VARCHAR} || '(,|$)')
            </foreach>
                )
            </if>
            <if test="evt.categoryId!=null">
                and category_id in (
                select
                a.category_id
                from (
                select category_id, full_category_id, unnest(string_to_array(full_category_id, ','))::numeric as hasRefId from kg_common_category_c
                where is_del = '0'
                ) a where a.hasRefId = #{evt.categoryId}
                )
            </if>
            and bases.is_deleted='0'
        </where>

    </select>

    <select id="selectBasesListByHighLevels" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesSpaceC">
        select
            s.*
        from kg_knowledge_bases_d bases
        LEFT JOIN kg_bases_space_relation_c r
        ON bases.kg_knowledge_bases_id::bigint = r.kg_knowledge_bases_id
        AND r.is_deleted = false
        LEFT JOIN kg_bases_space_c s
        ON r.kg_bases_space_c_id = s.kg_bases_space_c_id
        AND s.is_deleted = false
        <where>
            1=1
            <if test="evt.operativeWordList != null and not evt.operativeWordList.isEmpty()">
                and(  <foreach collection="evt.operativeWordList" item="item" separator=" and ">
                bases.operative_word ~ ('(^|,)' || #{item, jdbcType=VARCHAR} || '(,|$)')
            </foreach>
                )
            </if>
            <if test="evt.kgKnowledgeBasesIds!=null and not evt.kgKnowledgeBasesIds.isEmpty()">
                and bases.kg_knowledge_bases_id
                in
                <foreach collection="evt.kgKnowledgeBasesIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="evt.categoryId!=null">
                and category_id in (
                select
                a.category_id
                from (
                select category_id, full_category_id, unnest(string_to_array(full_category_id, ','))::numeric as hasRefId from kg_common_category_c
                where is_del = '0'
                ) a where a.hasRefId = #{evt.categoryId}
                )
            </if>
            <if test="evt.knowledgeName!=null and evt.knowledgeName!=''">
                and bases.knowledge_name like '%' || #{evt.knowledgeName, jdbcType=VARCHAR} || '%'
            </if>
            <if test="evt.region!=null and not evt.region.isEmpty()">
                and bases.region
                in
                <foreach collection="evt.region" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <!-- 文档类型查询（document_type）-->
            <if test="evt.documentTypeList!=null  and not evt.documentTypeList.isEmpty()">
                and (
                <foreach collection="evt.documentTypeList"  item="item" separator=" or ">
                    bases.document_type  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>

            <!-- 文档格式查询（document_format）-->
            <if test="evt.documentFormatList!=null  and not evt.documentFormatList.isEmpty()">
                and bases.document_format in
                <foreach collection="evt.documentFormatList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>



            <!-- 应用场景查询（application_scene）-->
            <if test="evt.applicationSceneList!=null  and not evt.applicationSceneList.isEmpty()">
                and (
                <foreach collection="evt.applicationSceneList"  item="item" separator=" or ">
                    bases.application_scene  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.knowledgeOriginList!=null  and not evt.knowledgeOriginList.isEmpty()">
                and (
                <foreach collection="evt.knowledgeOriginList"  item="item" separator=" or ">
                    bases.knowledge_origin  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.state!=null and not evt.state.isEmpty()">
                and bases.state
                in
                <foreach collection="evt.state" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <!--            <if test="evt.state!=null  and not evt.state.isEmpty()">-->
            <!--                and (-->
            <!--                <foreach collection="evt.state"  item="item" separator=" or ">-->
            <!--                    #{item} = ANY(string_to_array(bases.state,  ','))-->
            <!--                </foreach>-->
            <!--                )-->
            <!--            </if>-->
            <if test="evt.flowSceneList!=null  and not evt.flowSceneList.isEmpty()">
                and (
                <foreach collection="evt.flowSceneList"  item="item" separator=" or ">
                    bases.flow_scene  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.major!=null  and not evt.major.isEmpty()">
                and (
                <foreach collection="evt.major"  item="item" separator=" or ">
                    bases.major  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.authorList!=null  and not evt.authorList.isEmpty()">
                and (
                <foreach collection="evt.authorList"  item="item" separator=" or ">
                    bases.author  ~* ('(^|,)' || #{item} || '(,|$)')
                </foreach>
                )
            </if>
            <if test="evt.secretLevelList!=null and not evt.secretLevelList.isEmpty()">
                and bases.secret_level
                in
                <foreach collection="evt.secretLevelList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="evt.createdUserName!=null and evt.createdUserName!=''">
                and bases.created_user_name =#{evt.createdUserName}
            </if>
            <if test="evt.reviewer!=null and evt.reviewer!=''">
                and bases.reviewer =#{evt.reviewer}
            </if>

            <if test="evt.startTime!=null and evt.endTime!=null">
                and created_time &gt;=#{evt.startTime}::timestamp
                and created_time &lt;=#{evt.endTime}::timestamp
            </if>
            <if test="evt.userType != 1">
                <choose>
                    <when test="evt.perUserNames != null and not evt.perUserNames.isEmpty()">
                        AND (
                        (bases.created_user_name IN
                        <foreach collection="evt.perUserNames" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>

                        AND (bases.same_group_permission &lt; '3' or bases.created_user_name = #{evt.beforeOneUser} )
                        )
                        OR
                        (bases.created_user_name NOT IN
                        <foreach collection="evt.perUserNames" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        AND bases.other_person_permission &lt; '3'
                        )
                        )
                    </when>
                    <otherwise>
                        AND other_person_permission &lt; '3'
                    </otherwise>
                </choose>
            </if>
            and bases.is_deleted='0'
        </where>
        <choose>
            <when test="evt.whetherClickCountSort == 1">
                ORDER BY click_count DESC, bases.top_up DESC, bases.updated_time DESC, bases.created_time DESC
            </when>
            <when test="evt.whetherClickCountSort == 0">
                ORDER BY bases.top_up DESC, bases.updated_time DESC, bases.created_time DESC
            </when>
            <otherwise>
                ORDER BY bases.updated_time DESC, bases.created_time DESC
            </otherwise>
        </choose>

    </select>


    <select id="selectBasesListByHighLevelss" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesSpaceC">
        select
        s.*
        from kg_bases_space_c s
        LEFT JOIN kg_bases_space_relation_c r ON r.kg_bases_space_c_id = s.kg_bases_space_c_id
        AND r.is_deleted = false
        LEFT JOIN kg_knowledge_bases_d bases ON bases.kg_knowledge_bases_id = r.kg_knowledge_bases_id
        AND bases.is_deleted = '0'
        where s.is_deleted = false
        <if test="evt.mtPlatStaffId != null and evt.mtPlatStaffId.size() > 0">
            AND s.kg_bases_space_c_id
            <choose>
                <when test="evt.staffType != null and evt.staffType == 1">
                    NOT IN
                </when>
                <otherwise>
                    IN
                </otherwise>
            </choose>
            <foreach collection="evt.mtPlatStaffId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

    </select>

    <!-- 查询没有查重结果的知识库ID列表 -->
    <select id="selectIdsWithNoCheckResult" resultType="java.lang.Long">
        SELECT kg_knowledge_bases_id
        FROM kg_knowledge_bases_d
        WHERE is_deleted = '0'
        AND (check_repeat_result IS NULL 
             OR check_repeat_result::numeric IS NULL)
        <if test="startTime != null">
            AND created_time &gt;= #{startTime}::timestamp
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}::timestamp
        </if>
        ORDER BY created_time DESC
    </select>
    
    <!-- 批量更新查重结果 -->
    <update id="batchUpdateCheckResult" parameterType="java.util.List">
        UPDATE kg_knowledge_bases_d
        SET check_repeat_result = #{entities[0].checkRepeatResult},
        updated_time = now()
        WHERE kg_knowledge_bases_id IN
        <foreach collection="entities" item="item" open="(" separator="," close=")">
            #{item.kgKnowledgeBasesId}
        </foreach>
    </update>
    
    <!-- 查询待上报集团的知识库数据 -->
    <select id="selectPendingReportKnowledgeBases" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        SELECT * FROM kg_knowledge_bases_d
        WHERE report_status in ('11634023')
        AND is_deleted = '0'
        AND state IN ('4', '7', '8')
        AND knowledge_file_path ~ '\.(pdf|doc|docx|ppt|pptx|html|xls|xlsx)$'
        ORDER BY created_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <!-- 查询指定时间前仍在查重中的知识库ID列表（用于定时任务重新处理） -->
    <select id="selectStuckCheckingIds" resultType="java.lang.Long">
        SELECT kg_knowledge_bases_id
        FROM kg_knowledge_bases_d
        WHERE is_deleted = '0'
        AND check_repeat_result = '-1'
        AND created_time &lt;= #{beforeTime}::timestamp
        ORDER BY created_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <!-- 查询指定时间前仍在查重中的知识库实体列表（用于定时任务重新处理） -->
    <select id="selectStuckCheckingEntities" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        SELECT *
        FROM kg_knowledge_bases_d
        WHERE is_deleted = '0'
        AND check_repeat_result = -1
        AND created_time &lt;= #{beforeTime}::timestamp
        ORDER BY created_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <!-- 查询需要重新查重的知识库ID列表（优化版本） -->
    <select id="selectRetryableCheckingIds" resultType="java.lang.Long">
        SELECT kg_knowledge_bases_id
        FROM kg_knowledge_bases_d
        WHERE is_deleted = '0'
        AND created_time &lt;= #{beforeTime}::timestamp
        AND (
            -- 未查重的数据
            (check_repeat_status IS NULL OR check_repeat_status = 0)
            OR 
            -- 查重失败且重试次数未超限的数据
            (check_repeat_status = 3 AND (check_repeat_retry_count IS NULL OR check_repeat_retry_count &lt; #{maxRetryCount}))
            OR
            -- 旧数据兼容：check_repeat_result = -1 但没有设置状态的数据
            (check_repeat_result = -1 AND check_repeat_status IS NULL)
        )
        ORDER BY created_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <!-- 查询需要重新查重的知识库实体列表（优化版本） -->
    <select id="selectRetryableCheckingEntities" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        SELECT *
        FROM kg_knowledge_bases_d
        WHERE is_deleted = '0'
        AND created_time &lt;= #{beforeTime}::timestamp
        AND (
            -- 未查重的数据
            (check_repeat_status IS NULL OR check_repeat_status = 0)
            OR 
            -- 查重失败且重试次数未超限的数据
            (check_repeat_status = 3 AND (check_repeat_retry_count IS NULL OR check_repeat_retry_count &lt; #{maxRetryCount}))
            OR
            -- 旧数据兼容：check_repeat_result = -1 但没有设置状态的数据
            (check_repeat_result = -1 AND check_repeat_status IS NULL)
        )
        ORDER BY created_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <!-- 批量更新查重状态 -->
    <update id="batchUpdateCheckStatus" parameterType="java.util.List">
        <foreach collection="entities" item="item" separator=";">
            UPDATE kg_knowledge_bases_d
            SET 
                <if test="item.checkRepeatStatus != null">
                    check_repeat_status = #{item.checkRepeatStatus},
                </if>
                <if test="item.checkRepeatResult != null">
                    check_repeat_result = #{item.checkRepeatResult},
                </if>
                <if test="item.checkRepeatRetryCount != null">
                    check_repeat_retry_count = #{item.checkRepeatRetryCount},
                </if>
                <if test="item.checkRepeatFailReason != null">
                    check_repeat_fail_reason = #{item.checkRepeatFailReason},
                </if>
                updated_time = now()
            WHERE kg_knowledge_bases_id = #{item.kgKnowledgeBasesId}
        </foreach>
    </update>
    
    <!-- 更新查重状态为查重中 -->
    <update id="updateCheckStatusToChecking">
        UPDATE kg_knowledge_bases_d
        SET check_repeat_status = 1,
            check_repeat_result = -1,
            updated_time = now()
        WHERE kg_knowledge_bases_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = '0'
    </update>
    
    <!-- 更新查重状态为成功 -->
    <update id="updateCheckStatusToSuccess">
        UPDATE kg_knowledge_bases_d
        SET check_repeat_status = 2,
            check_repeat_result = #{checkRepeatResult},
            updated_time = now()
        WHERE kg_knowledge_bases_id = #{id}
        AND is_deleted = '0'
    </update>
    
    <!-- 更新查重状态为失败 -->
    <update id="updateCheckStatusToFailed">
        UPDATE kg_knowledge_bases_d
        SET check_repeat_status = 3,
            check_repeat_fail_reason = #{failReason},
            check_repeat_retry_count = COALESCE(check_repeat_retry_count, 0) + 1,
            updated_time = now()
        WHERE kg_knowledge_bases_id = #{id}
        AND is_deleted = '0'
    </update>

    <!-- 查询待上报集团的知识库数据（支持动态文件格式） -->
    <select id="selectPendingReportKnowledgeBasesWithFormats" resultType="com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity">
        SELECT * FROM kg_knowledge_bases_d
        WHERE (report_status = '11634023' or report_status is null)
        AND is_deleted = '0'
        <!-- todo 默认先全部上传-->
        <!--AND state IN ('4', '7', '8','2')-->
        <if test="supportedFormats != null and supportedFormats.size() > 0">
            AND document_format IN
            <foreach collection="supportedFormats" item="format" open="(" separator="," close=")">
                #{format}
            </foreach>
        </if>
        ORDER BY created_time ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <!-- 批量更新知识库上报状态 -->
    <update id="batchUpdateReportStatus">
        UPDATE kg_knowledge_bases_d 
        SET report_status = #{reportStatus},
            report_time = #{reportTime},
            report_batch_no = #{batchNo},
            is_sync = #{isSync},
            report_description = #{reportDescription},
            updated_time = #{updatedTime}
        WHERE kg_knowledge_bases_id IN
        <foreach collection="knowledgeBasesIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>