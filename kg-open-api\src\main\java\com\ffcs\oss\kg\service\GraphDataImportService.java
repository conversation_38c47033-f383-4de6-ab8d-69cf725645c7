package com.ffcs.oss.kg.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.data.enums.SyncType;
import com.ffcs.oss.kg.data.model.vo.api.GraphDataImportDTO;
import com.ffcs.oss.kg.data.model.vo.api.ImportMetaCache;
import com.ffcs.oss.kg.data.model.vo.api.ImportResult;
import com.ffcs.oss.kg.data.model.vo.api.ValidationResult;
import com.ffcs.oss.kg.data.rd.entity.common.KgImportLog;
import com.ffcs.oss.kg.data.rd.entity.graph.TempEntityInstance;
import com.ffcs.oss.kg.data.rd.entity.graph.TempRelationInstance;
import com.ffcs.oss.kg.data.rd.entity.thematicGraph.ThematicGraphEntity;
import com.ffcs.oss.kg.data.rd.mapper.common.KgImportLogMapper;
import com.ffcs.oss.kg.data.rd.mapper.thematicGraph.ThematicGraphMapper;
import com.ffcs.oss.kg.evt.SyncContext;
import com.ffcs.oss.kg.evt.SyncDataItem;
import com.ffcs.oss.kg.evt.SyncTask;
import com.ffcs.oss.kg.exception.ApiException;
import com.ffcs.oss.kg.service.common.*;
import com.ffcs.oss.kg.utils.CommonUtils;
import com.ffcs.oss.kg.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/3/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GraphDataImportService {

    private final TempEntityService tempEntityService;
    private final TempRelationService tempRelationService;

    private final TransactionTemplate transactionTemplate;

    private final ThematicGraphMapper graphMapper;

    //private final GraphDataSyncService graphDataSyncService;
    private final DataValidator dataValidator;
    private final ImportMetaService importMetaService;

    private final SyncCoordinator syncCoordinator;

    private final Executor asyncExecutor;

    private final KgImportLogMapper kgImportLogMapper;

    @Transactional(rollbackFor = Exception.class)
    public ImportResult processBatchImport(GraphDataImportDTO importData) {
        //todo 单点 和 传入点边都可以删除成功  但是只传边删除会有问题
        // 1. 元数据准备
        long startTime = System.currentTimeMillis();  // 记录开始时间
        KgImportLog importLog = new KgImportLog();    // 创建日志对象

        // 1. 参数校验（使用 Optional 避免显式 null 检查）
        try {
            Objects.requireNonNull(importData, "Import data cannot be null");
        } catch (Exception e) {
            throw new KnowledgeGraphException(e.getMessage());
        }

        List<GraphDataImportDTO.EntityImportItem> entities = Optional.ofNullable(importData.getEntityList())
                .orElseGet(ArrayList::new)
                .stream().distinct().collect(Collectors.toList());

        List<GraphDataImportDTO.RelationImportItem> relationships = Optional.ofNullable(importData.getRsList())
                .orElseGet(ArrayList::new)
                .stream().distinct().collect(Collectors.toList());

        // 2. 业务校验：entityList 和 rsList 不能同时为空
        if (entities.isEmpty() && relationships.isEmpty()) {
            return handleImportException(new ApiException("实体列表和关系列表不能同时为空"));
        }


        String uuid = UUID.randomUUID().toString();
        importLog.setBatchNum(uuid);
        ImportResult finalResult = null;
        importData.setEntityList(entities);
        importData.setRsList(relationships);
        try {
            // 1. 元数据准备
            ThematicGraphEntity tgInfo = resolveTgId(importData);
            importLog.setTgId(tgInfo.getTgId());  // 设置专题图ID

            ImportMetaCache metaCache = prepareMetadata(importData, tgInfo, uuid);

            // 2. 并行处理实体和关系
            CompletableFuture<ImportResult<TempEntityInstance>> entityFuture = processEntitiesAsync(importData, metaCache);
            CompletableFuture<ImportResult<TempRelationInstance>> relationFuture = processRelationsAsync(importData, metaCache);

            // 3. 合并处理结果
            finalResult = CompletableFuture.allOf(entityFuture, relationFuture)
                    .thenApplyAsync(v -> {
                        ImportResult<Object> combined = combineResults(
                                entityFuture.join(), relationFuture.join()
                        );

                        // 记录临时处理结果
                        importLog.setTotalCount(combined.getTotalCount());  // 总数据量

                        // 4. 触发后续同步流程
                        if (!combined.getDealList().isEmpty()) {
                            SyncContext syncContext = buildSyncContext(combined, metaCache);
                            ImportResult<Object> syncResult = syncCoordinator.coordinate(syncContext);
                            ImportResult<Object> mergedResult = mergeFinalResults(combined, syncResult);

                            // 记录同步结果
                            importLog.setSuccessCount(mergedResult.getSyncSuccessCount());  // 成功数量
                            importLog.setErrorCount(importLog.getTotalCount() - importLog.getSuccessCount());  // 错误数量
                            importLog.setSyncSuccessCount(mergedResult.getSyncSuccessCount());
                            return mergedResult;
                        }
                        return combined;
                    }, asyncExecutor)
                    .exceptionally(e -> {
                        log.error("批量导入异常", e);
                        throw new RuntimeException("批量导入异常", e);
                    })
                    .join();
            return finalResult;
        } catch (Exception e) {
            // 日志处理 非事务
            logImportResult(importLog, finalResult, startTime, e);
            throw e;
        } finally {
            // 打印处理日志
            log.info("数据导入完成 - TG_ID: {} 总数量: {} 成功: {} 失败: {} 耗时: {}ms",
                    importLog.getTgId(),
                    importLog.getTotalCount(),
                    importLog.getSuccessCount(),
                    importLog.getErrorCount(),
                    System.currentTimeMillis() - startTime);
        }
    }

    //@Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void logImportResult(KgImportLog importLog, ImportResult result, long startTime, Exception e) {
        try {
            // 记录耗时
            long costTime = System.currentTimeMillis() - startTime;
            importLog.setCostTime(costTime);
            importLog.setCreateTime(LocalDateTime.now());

            if (Objects.isNull(importLog.getTgId())) {
                importLog.setTgId(0L);
            }

            // 判断是否完全成功
            if (result != null) {
                boolean fullSuccess = result.getTotalCount() == result.getSyncSuccessCount();
                importLog.setStatus(fullSuccess ? 1 : 2);  // 1-成功 2-部分失败

                // 记录错误信息
                if (!fullSuccess && !result.getErrors().isEmpty()) {
                    importLog.setErrorDetails(JsonUtils.objectMapper().readValue(JSON.toJSONString(result.getErrors()), JsonNode.class));
                }
            }

            kgImportLogMapper.insert(importLog);
        } catch (Exception logEx) {
            log.error("日志记录失败", logEx);
        }
    }


    // 异常处理


    private SyncContext buildSyncContext(ImportResult<Object> combinedResult, ImportMetaCache metaCache) {
        SyncContext context = new SyncContext();
        context.setTgId(metaCache.getTgId());
        context.setGraphSpaceCode(metaCache.getGraphSpaceCode());
        context.setVidType(metaCache.getVidType());
        context.setEntityMetaMap(metaCache.getEntityMetaMap());
        context.setRelationMetaMap(metaCache.getRelationMetaMap());
        // 分组处理不同类型数据
        Map<Boolean, List<Object>> partitioned = combinedResult.getDealList().stream()
                .collect(Collectors.partitioningBy(
                        item -> item instanceof TempEntityInstance
                ));

        // 实体任务
        Map<String, Set<String>> entityTasks = partitioned.get(true).stream()
                .map(e -> (TempEntityInstance) e)
                .collect(Collectors.groupingBy(
                        TempEntityInstance::getModelCode,
                        Collectors.mapping(TempEntityInstance::getName, Collectors.toSet())
                ));

        // 关系任务
        Map<String, Set<String>> relationTasks = partitioned.get(false).stream()
                .map(r -> (TempRelationInstance) r)
                .collect(Collectors.groupingBy(
                        TempRelationInstance::getModelCode,
                        Collectors.mapping(TempRelationInstance::getName, Collectors.toSet())
                ));

        combinedResult.getDealList().forEach(item -> {
            if (item instanceof TempEntityInstance) {
                TempEntityInstance entity = (TempEntityInstance) item;
                ImportMetaCache.EntityMeta meta = metaCache.getEntityMetaMap().get(entity.getModelCode());
                context.getPendingData().add(new SyncDataItem<>(
                        SyncType.ENTITY,
                        meta.getTableName(),
                        meta.getModelCode(),
                        entity,
                        meta,null
                ));
            } else {
                // 关系处理
                TempRelationInstance relation = (TempRelationInstance) item;
                ImportMetaCache.RelationMeta relMeta = metaCache.getRelationMetaMap().get(relation.getModelCode());
                context.getPendingData().add(new SyncDataItem<>(
                        SyncType.RELATION,
                        relMeta.getTableName(),
                        relMeta.getModelCode(),
                        relation,
                        null,
                        relMeta
                ));
            }
        });


        List<SyncTask> tasks = new ArrayList<>();
        entityTasks.forEach((k, v) -> tasks.add(new SyncTask(SyncType.ENTITY, k, v)));
        relationTasks.forEach((k, v) -> tasks.add(new SyncTask(SyncType.RELATION, k, v)));
        context.setPendingTasks(tasks);
        return context;
    }

    private ImportResult<Object> mergeFinalResults(ImportResult<Object> tempResult,
                                                   ImportResult<Object> syncResult) {
        // 合并最终结果
        tempResult.setSuccessCount(tempResult.getSuccessCount());
        tempResult.setSyncSuccessCount(syncResult.getSuccessCount());
        tempResult.getErrors().addAll(syncResult.getErrors());
        tempResult.setDealList(syncResult.getDealList()); // 保留最终处理数据
        return tempResult;
    }


    private ImportMetaCache prepareMetadata(GraphDataImportDTO importData, ThematicGraphEntity tgInfo, String uuid) {
        ImportMetaCache metaCache = importMetaService.loadMetadata(tgInfo.getTgId(), uuid,importData.getGraphSpaceCode());

        // 转换存储结构：modelCode -> EntityMeta
        Map<String, ImportMetaCache.EntityMeta> codeMetaMap = new HashMap<>();
        metaCache.getEntityMetaMap().values().forEach(meta ->
                codeMetaMap.put(meta.getModelCode(), meta)
        );
        metaCache.setEntityMetaMap(codeMetaMap);

        return metaCache;
    }

    private CompletableFuture<ImportResult<TempEntityInstance>> processEntitiesAsync(GraphDataImportDTO data,
                                                                 ImportMetaCache metaCache) {
        return CompletableFuture.supplyAsync(() ->
                transactionTemplate.execute(status -> {
                    // 业务逻辑
                    List<ValidationResult<TempEntityInstance>> results = data.getEntityList().stream()
                            .map(item -> dataValidator.validateEntity(item, metaCache))
                            .collect(Collectors.toList());
                            
                    // 区分删除和新增/更新操作
                    List<ValidationResult<TempEntityInstance>> toInsertResults = new ArrayList<>();
                    List<ValidationResult<TempEntityInstance>> toDeleteResults = new ArrayList<>();
                    
                    for (ValidationResult<TempEntityInstance> result : results) {
                        if (result.isValid()) {
                            TempEntityInstance instance = result.getData();
                            if (instance.getStatus().equals(CommonUtils.STATUS_DELETE)) {
                                toDeleteResults.add(result);
                            } else {
                                toInsertResults.add(result);
                            }
                        } else {
                            // 无效结果加入到新增/更新列表中，保持原有错误处理逻辑
                            toInsertResults.add(result);
                        }
                    }
                    
                    // 处理新增/更新
                    ImportResult<TempEntityInstance> insertResult = 
                            toInsertResults.isEmpty() ? new ImportResult<>() : tempEntityService.batchInsertEntities(toInsertResults);
                    
                    // 处理删除
                    ImportResult<TempEntityInstance> deleteResult = 
                            toDeleteResults.isEmpty() ? new ImportResult<>() : tempEntityService.batchDeleteEntities(toDeleteResults);
                    
                    // 合并结果
                    ImportResult<TempEntityInstance> combinedResult = new ImportResult<>();
                    combinedResult.setTotalCount(insertResult.getTotalCount() + deleteResult.getTotalCount());
                    combinedResult.setSuccessCount(insertResult.getSuccessCount() + deleteResult.getSuccessCount());
                    combinedResult.getErrors().addAll(insertResult.getErrors());
                    combinedResult.getErrors().addAll(deleteResult.getErrors());
                    
                    // 合并处理的数据
                    List<TempEntityInstance> allDeals = new ArrayList<>();
                    if (insertResult.getDealList() != null) {
                        allDeals.addAll(insertResult.getDealList());
                    }
                    if (deleteResult.getDealList() != null) {
                        allDeals.addAll(deleteResult.getDealList());
                    }
                    combinedResult.setDealList(allDeals);
                    combinedResult.setType(insertResult.getType());
                    
                    return combinedResult;
                }), asyncExecutor);
    }

    private CompletableFuture<ImportResult<TempRelationInstance>> processRelationsAsync(GraphDataImportDTO data,
                                                                  ImportMetaCache metaCache) {

        return CompletableFuture.supplyAsync(() ->
                transactionTemplate.execute(status -> {
                    // 业务逻辑
                    Map<String, Set<String>> endpointCache = buildEndpointCache(data.getEntityList());

                    List<ValidationResult<TempRelationInstance>> results = data.getRsList().stream()
                            .map(item -> dataValidator.validateRelation(item, metaCache, endpointCache))
                            .collect(Collectors.toList());

                    // 区分删除和新增/更新操作
                    List<ValidationResult<TempRelationInstance>> toInsertResults = new ArrayList<>();
                    List<ValidationResult<TempRelationInstance>> toDeleteResults = new ArrayList<>();
                    
                    for (ValidationResult<TempRelationInstance> result : results) {
                        if (result.isValid()) {
                            TempRelationInstance instance = result.getData();
                            if (instance.getStatus().equals(CommonUtils.STATUS_DELETE)) {
                                toDeleteResults.add(result);
                            } else {
                                toInsertResults.add(result);
                            }
                        } else {
                            // 无效结果加入到新增/更新列表中，保持原有错误处理逻辑
                            toInsertResults.add(result);
                        }
                    }
                    
                    // 处理新增/更新
                    ImportResult<TempRelationInstance> insertResult = 
                            toInsertResults.isEmpty() ? new ImportResult<>() : tempRelationService.batchProcess(toInsertResults);
                    
                    // 处理删除
                    ImportResult<TempRelationInstance> deleteResult = 
                            toDeleteResults.isEmpty() ? new ImportResult<>() : tempRelationService.batchDelete(toDeleteResults);
                    
                    // 合并结果
                    ImportResult<TempRelationInstance> combinedResult = new ImportResult<>();
                    combinedResult.setTotalCount(insertResult.getTotalCount() + deleteResult.getTotalCount());
                    combinedResult.setSuccessCount(insertResult.getSuccessCount() + deleteResult.getSuccessCount());
                    combinedResult.getErrors().addAll(insertResult.getErrors());
                    combinedResult.getErrors().addAll(deleteResult.getErrors());
                    
                    // 合并处理的数据
                    List<TempRelationInstance> allDeals = new ArrayList<>();
                    if (insertResult.getDealList() != null) {
                        allDeals.addAll(insertResult.getDealList());
                    }
                    if (deleteResult.getDealList() != null) {
                        allDeals.addAll(deleteResult.getDealList());
                    }
                    combinedResult.setDealList(allDeals);
                    combinedResult.setType(insertResult.getType());
                    
                    return combinedResult;
                }), asyncExecutor);
    }

    private ImportResult<Object> combineResults(ImportResult<TempEntityInstance> entityResult, ImportResult<TempRelationInstance> relationResult) {

        ImportResult<Object> combined = new ImportResult<>();

        // 合并基础统计
        combined.setTotalCount(entityResult.getTotalCount() + relationResult.getTotalCount());
        combined.setSuccessCount(entityResult.getSuccessCount() + relationResult.getSuccessCount());

        // 合并错误信息
        List<ImportResult.ErrorDetail> allErrors = Stream.concat(
                entityResult.getErrors().stream(),
                relationResult.getErrors().stream()
        ).collect(Collectors.toList());
        combined.setErrors(allErrors);

        // 合并处理数据列表（保持泛型）
        List<Object> allDealList = Stream.concat(
                entityResult.getDealList().stream(),
                relationResult.getDealList().stream()
        ).collect(Collectors.toList());
        combined.setDealList(allDealList);

        return combined;
    }

    private ThematicGraphEntity resolveTgId(GraphDataImportDTO data) {
        // 根据fullCategoryName和tgName查询专题图谱ID
        // 实现略
        ThematicGraphEntity graph = graphMapper.queryTgByApi(data.getTgName(), data.getFullCategoryName(), data.getTenantCode(), data.getGraphSpaceCode());
        if (graph == null) {
            log.error("图谱不存在,图谱名称:{},分类名称：{}，租户编码:{},图空间编码:{}",data.getTgName(), data.getFullCategoryName(), data.getTenantCode(), data.getGraphSpaceCode());
            throw new ApiException("图谱不存在");
        }
        return graph;
    }

    private Map<String, Set<String>> buildEndpointCache(List<GraphDataImportDTO.EntityImportItem> entities) {
        Map<String, Set<String>> cache = new ConcurrentHashMap<>();

        // 1. 当前批次端点
        entities.forEach(e ->
                cache.computeIfAbsent(e.getModelCode(), k -> ConcurrentHashMap.newKeySet())
                        .add(e.getName())
        );

        // 2. 数据库已有端点（批量查询优化）
        //todo 查询优化
        /*if (!cache.isEmpty()) {
            List<Map<String, Object>> dbEndpoints = entityTypeMapper.batchSelectEndpoints(
                    new ArrayList<>(cache.keySet())
            );
            dbEndpoints.forEach(e ->
                    cache.computeIfAbsent(e.get("model_code").toString(),
                                    k -> ConcurrentHashMap.newKeySet())
                            .add(e.get("name").toString())
            );
        }*/

        return cache;
    }


    //todo 后续补充
    private ImportResult handleImportException(Throwable ex) {
        // 异常处理逻辑
        // 实现略
        ImportResult importResult = new ImportResult();
        importResult.setTotalCount(0);
        importResult.setSuccessCount(0);
        importResult.setErrors(Collections.singletonList(new ImportResult.ErrorDetail("", "", ex.getMessage())));
        return importResult;
    }

}
