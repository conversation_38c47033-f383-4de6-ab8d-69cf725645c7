package com.ffcs.oss.kg.similarity.client;

import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 知识库上报服务客户端
 * 
 * <AUTHOR> Assistant
 */
@FeignClient(name = "kg-system-srv")
//@FeignClient(name = "kg-system-srv",url = "http://127.0.0.1:8088")
public interface KnowledgeReportClient {

    /**
     * 单个知识库上报到集团
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 上报结果
     */
    @PostMapping("/kg-web-srv/api/knowledge-report/single-report/{knowledgeBaseId}")
    ServiceResp<Map<String, Object>> singleReportKnowledgeBase(@PathVariable("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * 批量上报知识库到集团
     * 
     * @param knowledgeBaseIds 知识库ID列表
     * @return 批量上报结果
     */
    @PostMapping("/kg-web-srv/api/knowledge-report/batch-report")
    ServiceResp<Map<String, Object>> batchReportKnowledgeBases(@RequestBody List<Long> knowledgeBaseIds);
    
    /**
     * 查询上报任务状态
     * 
     * @return 任务状态
     */
    @GetMapping("/kg-web-srv/api/knowledge-report/status")
    ServiceResp<Map<String, Object>> getReportStatus();
} 