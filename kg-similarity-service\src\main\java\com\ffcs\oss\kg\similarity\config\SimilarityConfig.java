package com.ffcs.oss.kg.similarity.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * 相似度服务配置类
 * 支持配置热刷新
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "similarity")
@RefreshScope
public class SimilarityConfig {
    
    /**
     * 相似度阈值
     */
    private Double threshold = 0.7;
    
    /**
     * 提前终止阈值，高于此阈值则提前终止比较
     * 如设置为0.9，则一旦发现相似度大于90%的文件，将不再继续比较
     */
    private Double earlyStopThreshold = 0.9;
    
    /**
     * 是否启用提前终止
     */
    private Boolean enableEarlyStop = true;
    
    /**
     * 是否启用文件查重
     */
    private Boolean enabled = true;
    
    /**
     * 相似度计算方法：SIMHASH, COSINE, COMBINED
     */
    private String method = "SIMHASH";
    
    /**
     * 批处理大小
     */
    private Integer batchSize = 20;
    
    /**
     * 比较线程池并行度
     * 决定并行比较时用多少线程同时进行
     */
    private Integer compareParallelism = 4;
    
    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();
    
    @Data
    public static class ThreadPool {
        /**
         * 核心线程数
         */
        private Integer coreSize = 5;
        
        /**
         * 最大线程数
         */
        private Integer maxSize = 10;
        
        /**
         * 队列容量
         */
        private Integer queueCapacity = 100;
        
        /**
         * 线程保持活跃时间(秒)
         */
        private Integer keepAlive = 60;
    }
} 