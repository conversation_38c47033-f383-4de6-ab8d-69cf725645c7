package com.ffcs.oss.kg.similarity.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.Collections;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.SimilarFileInfo;
import com.ffcs.oss.kg.similarity.service.FileSimilarityService;
import com.ffcs.oss.kg.similarity.service.FileSimilarityQueueService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件相似度服务控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/similarity")
@Api(tags = "文件相似度接口")
public class FileSimilarityController {

    @Autowired
    private FileSimilarityService fileSimilarityService;
    
    @Autowired
    private FileSimilarityQueueService fileSimilarityQueueService;
    
    /**
     * 计算文件相似度并将结果存储到Redis
     * 
     * @param kgKnowledgeBasesId 知识库ID
     * @param fromPage 是否来自页面请求
     * @return 操作状态
     */
    @PostMapping("/check")
    public String checkFileSimilarity(@RequestParam("kgKnowledgeBasesId") Long kgKnowledgeBasesId, 
                                     @RequestParam(value = "fromPage", defaultValue = "false") boolean fromPage) {
        log.info("接收到文件相似度检查请求，知识库ID: {}, fromPage: {}", kgKnowledgeBasesId, fromPage);
        try {
            // 将单个ID转换为列表，复用批量处理逻辑
            List<Long> idList = Collections.singletonList(kgKnowledgeBasesId);
            
            // 使用队列服务处理
            boolean addSuccess = fileSimilarityQueueService.addToQueue(idList);
            if (addSuccess) {
                return "文件相似度检查请求已加入队列";
            } else {
                return "文件相似度检查请求添加失败";
            }
        } catch (Exception e) {
            log.error("文件相似度检查失败", e);
            return "文件相似度检查请求提交失败: " + e.getMessage();
        }
    }
    
    /**
     * 批量计算文件相似度并将结果存储到Redis
     * 优化版本：使用队列缓冲机制，避免多并发导致的性能问题
     * 
     * @param kgKnowledgeBasesIds 知识库ID列表
     * @return 操作状态
     */
    @PostMapping("/batchCheck")
    @ApiOperation(value = "批量文件相似度检查", notes = "使用队列缓冲机制，避免多并发导致的性能问题")
    public ServiceResp<Object> batchCheckFileSimilarity(@RequestBody List<Long> kgKnowledgeBasesIds) {
        log.info("接收到批量文件相似度检查请求，知识库ID数量: {}", kgKnowledgeBasesIds.size());
        try {
            // 使用队列服务处理，避免直接执行导致的并发问题
            boolean addSuccess = fileSimilarityQueueService.addToQueue(kgKnowledgeBasesIds);
            if (addSuccess) {
                return ServiceResp.success("批量文件相似度检查请求已加入队列，系统将自动处理");
            } else {
                return ServiceResp.fail("批量文件相似度检查请求添加失败");
            }
        } catch (Exception e) {
            log.error("批量文件相似度检查失败", e);
            return ServiceResp.fail("批量文件相似度检查请求提交失败: " + e.getMessage());
        }
    }
    
    /**
     * 从Redis获取文件相似度列表
     * 
     * @param kgKnowledgeBasesId 知识库ID
     * @return 相似文件信息列表
     */
    @GetMapping("/list")
    public List<SimilarFileInfo> getFileSimilarityList(@RequestParam("kgKnowledgeBasesId") Long kgKnowledgeBasesId) {
        log.debug("获取文件相似度列表，知识库ID: {}", kgKnowledgeBasesId);
        return fileSimilarityService.getFileSimilarityList(kgKnowledgeBasesId);
    }
    
    /**
     * 清除文件相似度缓存
     * 
     * @param kgKnowledgeBasesId 知识库ID
     * @return 是否成功
     */
    @PostMapping("/clear")
    public boolean clearFileSimilarityCache(@RequestParam("kgKnowledgeBasesId") Long kgKnowledgeBasesId) {
        log.info("清除文件相似度缓存，知识库ID: {}", kgKnowledgeBasesId);
        return fileSimilarityService.clearFileSimilarityCache(kgKnowledgeBasesId);
    }


    /**
     * 手动触发处理查重中断数据（优化版本）
     * 使用查重状态字段优化查询，避免重复处理失败数据
     *
     * @param hoursAgo 多少小时前的数据，默认3小时
     * @param batchSize 每批处理的数据量，默认15条
     * @param maxRetryCount 最大重试次数，默认3次
     * @return 服务响应，包含处理结果信息
     */
    @PostMapping("/process-stuck-checking-optimized")
    @ApiOperation(value = "手动处理查重中断数据（优化版本）", notes = "使用查重状态字段优化查询，避免重复处理失败数据，支持重试机制")
    public ServiceResp<Object> processStuckCheckingDataOptimized(
            @ApiParam(value = "多少小时前的数据", example = "3") 
            @RequestParam(required = false) Integer hoursAgo,
            
            @ApiParam(value = "批处理大小", example = "15") 
            @RequestParam(required = false) Integer batchSize,
            
            @ApiParam(value = "最大重试次数", example = "3") 
            @RequestParam(required = false) Integer maxRetryCount) {
        
        log.info("接收到手动处理查重中断数据请求（优化版本）, hoursAgo={}, batchSize={}, maxRetryCount={}", 
                hoursAgo, batchSize, maxRetryCount);
        
        return fileSimilarityService.processStuckCheckingDataOptimized(hoursAgo, batchSize, maxRetryCount);
    }
    
    /**
     * 获取队列状态信息
     * 
     * @return 队列状态详情
     */
    @GetMapping("/queue/status")
    @ApiOperation(value = "获取队列状态", notes = "查看当前队列的运行状态和统计信息")
    public ServiceResp<Object> getQueueStatus() {
        log.debug("获取队列状态信息");
        try {
            Map<String, Object> status = fileSimilarityQueueService.getQueueStatus();
            return ServiceResp.success("获取队列状态成功", status);
        } catch (Exception e) {
            log.error("获取队列状态失败", e);
            return ServiceResp.fail("获取队列状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发队列处理
     * 
     * @return 处理的数量
     */
    @PostMapping("/queue/trigger")
    @ApiOperation(value = "手动触发队列处理", notes = "强制触发当前队列中的所有待处理任务")
    public ServiceResp<Object> manualTriggerQueue() {
        log.info("接收到手动触发队列处理请求");
        try {
            int processedCount = fileSimilarityQueueService.manualTriggerProcess();
            String message = processedCount > 0 ? 
                    "手动触发队列处理成功，处理数量: " + processedCount : 
                    "队列为空，无需处理";
            return ServiceResp.success(message, processedCount);
        } catch (Exception e) {
            log.error("手动触发队列处理失败", e);
            return ServiceResp.fail("手动触发队列处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 清空队列（紧急情况使用）
     * 
     * @return 清空的数量
     */
    @PostMapping("/queue/clear")
    @ApiOperation(value = "清空队列", notes = "紧急情况下清空所有待处理的队列数据")
    public ServiceResp<Object> clearQueue() {
        log.warn("接收到清空队列请求");
        try {
            int clearedCount = fileSimilarityQueueService.clearQueue();
            String message = clearedCount > 0 ? 
                    "队列清空成功，清空数量: " + clearedCount : 
                    "队列已为空";
            return ServiceResp.success(message, clearedCount);
        } catch (Exception e) {
            log.error("清空队列失败", e);
            return ServiceResp.fail("清空队列失败: " + e.getMessage());
        }
    }
} 