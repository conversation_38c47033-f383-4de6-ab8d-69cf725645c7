package com.ffcs.oss.kg.similarity.schedule;

import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.similarity.service.FileSimilarityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 文件相似度定时任务
 * 定时处理查重中断的数据，避免查重状态长期卡在"查重中"
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "similarity.schedule.enabled", havingValue = "true", matchIfMissing = false)
public class FileSimilarityScheduleTask {

    @Autowired
    private FileSimilarityService fileSimilarityService;
    
    /**
     * 查询多少小时前的数据（默认3小时）
     */
    @Value("${similarity.schedule.hours-ago:3}")
    private Integer hoursAgo;
    
    /**
     * 每次处理的批次大小（默认15条）
     */
    @Value("${similarity.schedule.batch-size:15}")
    private Integer batchSize;
    
    /**
     * 最大重试次数（默认3次）
     */
    @Value("${similarity.schedule.max-retry-count:3}")
    private Integer maxRetryCount;
    
    /**
     * 是否启用定时任务
     */
    @Value("${similarity.schedule.enabled:false}")
    private Boolean scheduleEnabled;
    
    /**
     * 定时处理查重中断数据任务
     */
    @Scheduled(cron = "${similarity.schedule.cron:0 0/15 * * * ?}") // 每半小时执行一次
    public void processStuckCheckingDataTask() {
        if (!scheduleEnabled) {
            log.debug("定时任务已禁用，跳过执行");
            return;
        }
        
        long startTime = System.currentTimeMillis();
        log.info("🕐 定时任务开始执行：处理查重中断数据, hoursAgo={}, batchSize={}, maxRetryCount={}", 
                hoursAgo, batchSize, maxRetryCount);
        
        try {
            // 使用优化版本的查重中断数据处理
            ServiceResp<Object> result = fileSimilarityService.processStuckCheckingDataOptimized(hoursAgo, batchSize, maxRetryCount);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (result.isSuccess()) {
                log.info("✅ 定时任务执行成功, 耗时={}ms, 结果={}", duration, result.getHead().getRespMsg());
                if (result.getBody() != null) {
                    log.info("📊 处理详情: {}", result.getBody());
                }
            } else {
                log.error("❌ 定时任务执行失败, 耗时={}ms, 错误={}", duration, result.getHead().getRespMsg());
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("💥 定时任务执行异常, 耗时={}ms", duration, e);
        }
    }
    
    /**
     * 应用启动后延迟执行的初始化任务
     */
/*    @Scheduled(initialDelay = 5 * 60 * 1000, fixedDelay = Long.MAX_VALUE) // 启动5分钟后执行一次
    public void initialProcessStuckCheckingDataTask() {
        if (!scheduleEnabled) {
            log.debug("定时任务已禁用，跳过初始化任务");
            return;
        }
        
        long startTime = System.currentTimeMillis();
        log.info("🚀 应用启动初始化任务开始：处理查重中断数据");
        
        try {
            // 执行一次清理任务
            processStuckCheckingDataTask();
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("🎉 应用启动初始化任务完成, 耗时={}ms", duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("💥 应用启动初始化任务异常, 耗时={}ms", duration, e);
        }
    }*/
} 