package com.ffcs.oss.kg.similarity.service;

import com.ffcs.oss.kg.similarity.service.FileSimilarityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 文件相似度队列服务
 * 负责管理查重请求的缓冲和批量触发，避免多并发导致的性能问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileSimilarityQueueService {

    @Autowired
    private FileSimilarityService fileSimilarityService;

    /**
     * 队列触发阈值（默认10个）
     */
    @Value("${similarity.queue.trigger-size:10}")
    private Integer triggerSize;

    /**
     * 队列超时时间（分钟，默认3分钟）
     */
    @Value("${similarity.queue.timeout-minutes:3}")
    private Integer timeoutMinutes;

    /**
     * 是否启用队列模式
     */
    @Value("${similarity.queue.enabled:true}")
    private Boolean queueEnabled;

    /**
     * 队列检查间隔（秒，默认30秒）
     */
    @Value("${similarity.queue.check-interval-seconds:30}")
    private Integer checkIntervalSeconds;

    /**
     * 待处理队列
     */
    private final Queue<Long> pendingQueue = new ConcurrentLinkedQueue<>();

    /**
     * 队列最后更新时间
     */
    private volatile LocalDateTime lastUpdateTime = LocalDateTime.now();

    /**
     * 处理锁，防止同时触发多次处理
     */
    private final ReentrantLock processLock = new ReentrantLock();

    /**
     * 是否正在处理中
     */
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);

    /**
     * 队列处理统计
     */
    private final AtomicLong totalReceived = new AtomicLong(0);
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong batchCount = new AtomicLong(0);

    @PostConstruct
    public void init() {
        log.info("文件相似度队列服务初始化完成，配置: triggerSize={}, timeoutMinutes={}, enabled={}, checkInterval={}s", 
                triggerSize, timeoutMinutes, queueEnabled, checkIntervalSeconds);
    }

    /**
     * 添加ID到队列
     * 
     * @param kgKnowledgeBasesIds 知识库ID列表
     * @return 是否添加成功
     */
    public boolean addToQueue(List<Long> kgKnowledgeBasesIds) {
        if (!queueEnabled) {
            log.warn("队列功能已禁用，直接调用批量处理");
            long startTime = System.currentTimeMillis();
            try {
                //fileSimilarityService.batchCheckFileSimilarityAsync(kgKnowledgeBasesIds);
                // todo 暂时不考虑非队列的情况
                long duration = System.currentTimeMillis() - startTime;
                log.info("队列禁用模式下直接处理完成, 数量={}, 耗时={}ms", kgKnowledgeBasesIds.size(), duration);
                return true;
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("队列禁用模式下直接处理异常, 数量={}, 耗时={}ms", kgKnowledgeBasesIds.size(), duration, e);
                return false;
            }
        }
        
        if (kgKnowledgeBasesIds == null || kgKnowledgeBasesIds.isEmpty()) {
            log.warn("尝试添加空的ID列表到队列");
            return false;
        }
        
        long startTime = System.currentTimeMillis();
        int addedCount = 0;
        
        synchronized (pendingQueue) {
            for (Long id : kgKnowledgeBasesIds) {
                if (id != null && !pendingQueue.contains(id)) {
                    pendingQueue.offer(id);
                    addedCount++;
                }
            }
            lastUpdateTime = LocalDateTime.now();
        }
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("📥 添加到队列: 请求数量={}, 实际添加={}, 当前队列大小={}, 耗时={}ms", 
                kgKnowledgeBasesIds.size(), addedCount, pendingQueue.size(), duration);
        
        // 检查是否需要触发处理
        checkAndTriggerProcess();
        
        return addedCount > 0;
    }
    
    /**
     * 检查并触发处理
     */
    private void checkAndTriggerProcess() {
        int currentSize = pendingQueue.size();
        
        // 检查大小触发条件
        if (currentSize >= triggerSize) {
            triggerBatchProcess("队列大小达到阈值: " + currentSize + "/" + triggerSize);
            return;
        }
        
        // 检查时间触发条件
        LocalDateTime now = LocalDateTime.now();
        long minutesElapsed = Duration.between(lastUpdateTime, now).toMinutes();
        
        if (currentSize > 0 && minutesElapsed >= timeoutMinutes) {
            triggerBatchProcess("队列超时触发: " + minutesElapsed + "分钟, 队列大小: " + currentSize);
        }
    }
    
    /**
     * 定时检查队列状态
     */
    @Scheduled(cron = "${similarity.queue.check-interval-cron:*/30 * * * * ?}")
    public void scheduledCheckQueue() {
        if (!queueEnabled) {
            return;
        }
        
        log.debug("🔍 定时检查队列状态, 当前大小={}", pendingQueue.size());
        checkAndTriggerProcess();
    }
    
    /**
     * 触发批量处理
     */
    private void triggerBatchProcess(String reason) {
        if (!processLock.tryLock()) {
            log.debug("⏳ 队列处理正在进行中，跳过本次触发: {}", reason);
            return;
        }
        
        try {
            if (isProcessing.get()) {
                log.debug("🔄 队列正在处理中，跳过本次触发: {}", reason);
                return;
            }
            
            List<Long> idsToProcess = new ArrayList<>();
            synchronized (pendingQueue) {
                // 获取所有待处理的ID
                while (!pendingQueue.isEmpty() && idsToProcess.size() < triggerSize * 2) { // 最多处理2倍触发量
                    Long id = pendingQueue.poll();
                    if (id != null) {
                        idsToProcess.add(id);
                    }
                }
            }
            
            if (idsToProcess.isEmpty()) {
                log.debug("📭 队列为空，无需处理");
                return;
            }
            
            log.info("🚀 触发队列批量处理: {}, 处理数量={}", reason, idsToProcess.size());
            processAsync(idsToProcess, reason);
            
        } finally {
            processLock.unlock();
        }
    }
    
    /**
     * 异步处理任务
     */
    private void processAsync(List<Long> idsToProcess, String reason) {
        isProcessing.set(true);
        
        // 避免线程池嵌套，直接使用新线程
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            try {
                log.info("📋 开始执行批量查重，数量: {}，原因: {}", idsToProcess.size(), reason);
                
                // 使用新的状态管理方法设置查重状态
                long statusStartTime = System.currentTimeMillis();
                boolean setStatusSuccess = fileSimilarityService.updateCheckStatusToChecking(idsToProcess);
                long statusDuration = System.currentTimeMillis() - statusStartTime;
                
                if (!setStatusSuccess) {
                    long totalDuration = System.currentTimeMillis() - startTime;
                    log.error("❌ 设置查重状态失败，ID数量: {}, 状态设置耗时={}ms, 总耗时={}ms", 
                            idsToProcess.size(), statusDuration, totalDuration);
                    return;
                }
                
                log.info("✅ 状态设置成功, 耗时={}ms", statusDuration);
                
                // 执行查重
                long processStartTime = System.currentTimeMillis();
                fileSimilarityService.unifiedSimilarityCheck(idsToProcess, "队列处理")
                    .whenComplete((result, throwable) -> {
                        long processDuration = System.currentTimeMillis() - processStartTime;
                        long totalDuration = System.currentTimeMillis() - startTime;
                        
                        if (throwable != null) {
                            log.error("❌ 队列批量查重执行异常, 数量={}, 处理耗时={}ms, 总耗时={}ms", 
                                    idsToProcess.size(), processDuration, totalDuration, throwable);
                        } else {
                            log.info("✅ 队列批量查重执行完成, 数量={}, 处理耗时={}ms, 总耗时={}ms", 
                                    idsToProcess.size(), processDuration, totalDuration);
                        }
                    });
                
            } catch (Exception e) {
                long totalDuration = System.currentTimeMillis() - startTime;
                log.error("💥 队列异步处理异常, 数量={}, 总耗时={}ms", idsToProcess.size(), totalDuration, e);
            } finally {
                isProcessing.set(false);
            }
        }, "similarity-queue-processor").start();
    }

    /**
     * 手动触发处理（用于紧急情况）
     * 
     * @return 处理的数量
     */
    public int manualTriggerProcess() {
        int queueSize = pendingQueue.size();
        if (queueSize > 0) {
            log.info("手动触发队列处理，当前队列大小: {}", queueSize);
            triggerBatchProcess("手动触发");
        } else {
            log.info("队列为空，无需处理");
        }
        return queueSize;
    }

    /**
     * 获取队列状态信息
     * 
     * @return 队列状态
     */
    public Map<String, Object> getQueueStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", queueEnabled);
        status.put("currentQueueSize", pendingQueue.size());
        status.put("triggerSize", triggerSize);
        status.put("timeoutMinutes", timeoutMinutes);
        status.put("isProcessing", isProcessing.get());
        status.put("lastUpdateTime", lastUpdateTime);
        status.put("totalReceived", totalReceived.get());
        status.put("totalProcessed", totalProcessed.get());
        status.put("batchCount", batchCount.get());
        
        LocalDateTime now = LocalDateTime.now();
        long minutesSinceLastUpdate = java.time.Duration.between(lastUpdateTime, now).toMinutes();
        status.put("minutesSinceLastUpdate", minutesSinceLastUpdate);
        
        return status;
    }

    /**
     * 清空队列（紧急情况使用）
     * 
     * @return 清空的数量
     */
    public int clearQueue() {
        int clearedCount = pendingQueue.size();
        pendingQueue.clear();
        log.warn("队列已被手动清空，清空数量: {}", clearedCount);
        return clearedCount;
    }
} 