package com.ffcs.oss.kg.similarity.service;

import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.SimilarFileInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.math.BigDecimal;

/**
 * 文件相似度服务接口
 */
public interface FileSimilarityService {


    /**
     * 从Redis获取文件相似度列表
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @return 相似文件信息列表
     */
    List<SimilarFileInfo> getFileSimilarityList(Long kgKnowledgeBasesId);
    
    /**
     * 清除文件相似度缓存
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @return 是否成功
     */
    boolean clearFileSimilarityCache(Long kgKnowledgeBasesId);

    /**
     * 处理查重中断数据（优化版本）
     * 使用查重状态字段优化查询，避免重复处理失败数据
     *
     * @param hoursAgo 多少小时前的数据，默认3小时
     * @param batchSize 每批处理的数据量，默认15条
     * @param maxRetryCount 最大重试次数，默认3次
     * @return 处理结果
     */
    ServiceResp<Object> processStuckCheckingDataOptimized(Integer hoursAgo, Integer batchSize, Integer maxRetryCount);
    
    /**
     * 更新查重状态为查重中
     *
     * @param kgKnowledgeBasesIds 知识库ID列表
     * @return 是否更新成功
     */
    boolean updateCheckStatusToChecking(List<Long> kgKnowledgeBasesIds);
    
    /**
     * 更新查重状态为成功
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param checkRepeatResult 查重结果
     * @return 是否更新成功
     */
    boolean updateCheckStatusToSuccess(Long kgKnowledgeBasesId, BigDecimal checkRepeatResult);
    
    /**
     * 更新查重状态为失败
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param failReason 失败原因
     * @return 是否更新成功
     */
    boolean updateCheckStatusToFailed(Long kgKnowledgeBasesId, String failReason);

    /**
     * 统一的文件相似度处理方法
     * 供队列、定时任务、手动触发等所有场景使用
     * 
     * @param kgKnowledgeBasesIds 知识库ID列表
     * @param source 调用来源（用于日志）
     * @return 处理结果
     */
    CompletableFuture<Void> unifiedSimilarityCheck(List<Long> kgKnowledgeBasesIds, String source);
} 