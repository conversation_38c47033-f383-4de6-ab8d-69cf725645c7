package com.ffcs.oss.kg.similarity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.collection.CollectionUtil;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.FormatUtil;
import com.ffcs.oss.kg.common.cache.CtgRedisService;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.enums.knowledgeBases.AuditEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.CheckRepeatStatusEnum;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.DocumentFingerprint;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgKnowledgeFile;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.SimilarFileInfo;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesAuditDimensionConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesAuditDimensionD;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.BasesAuditDimensionConfigMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.BasesAuditDimensionMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.DocumentFingerprintMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.ModelKnowledgeBasesDMapper;
import com.ffcs.oss.kg.data.enums.CaseStateEnum;
import com.ffcs.oss.kg.similarity.algorithm.EnhancedSimHashUtil;
import com.ffcs.oss.kg.similarity.algorithm.EnhancedSimHashUtil.SimilarityMethod;
import com.ffcs.oss.kg.similarity.algorithm.SimHashUtil;
import com.ffcs.oss.kg.similarity.algorithm.TextPlagiarismCheckUtil;
import com.ffcs.oss.kg.similarity.client.KnowledgeReportClient;
import com.ffcs.oss.kg.similarity.config.KnowledgeConstant;
import com.ffcs.oss.kg.similarity.config.SimilarityConfig;
import com.ffcs.oss.kg.similarity.model.CompareTask;
import com.ffcs.oss.kg.similarity.model.ComparisonProgressTracker;
import com.ffcs.oss.kg.similarity.service.FileSimilarityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.Semaphore;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件相似度服务实现类
 */
@Slf4j
@Service
public class FileSimilarityServiceImpl implements FileSimilarityService {

    @Autowired
    private SimilarityConfig similarityConfig;

    @Autowired
    private CtgRedisService ctgRedisService;
    
    @Autowired
    private ModelKnowledgeBasesDMapper knowledgeBasesDMapper;
    
    @Autowired
    private DocumentFingerprintMapper documentFingerprintMapper;
    
    @Autowired
    private KnowledgeReportClient knowledgeReportClient;

    @Autowired
    private BasesAuditDimensionConfigMapper basesAuditDimensionConfigMapper;

    @Autowired
    private BasesAuditDimensionMapper basesAuditDimensionMapper;
    
    /**
     * 批量处理线程池
     */
    @Autowired
    @Qualifier("similarityBatchThreadPool")
    private ThreadPoolExecutor similarityBatchThreadPool;
    
    /**
     * 比较处理线程池
     */
    @Autowired
    @Qualifier("similarityCompareThreadPool")
    private ThreadPoolExecutor similarityCompareThreadPool;
    
    /**
     * 兼容旧代码，使用批量处理线程池
     * @deprecated 使用专用的similarityBatchThreadPool和similarityCompareThreadPool替代
     */
    @Autowired
    @Deprecated
    private ThreadPoolExecutor similarityThreadPool;
    

    
    @Override
    public List<SimilarFileInfo> getFileSimilarityList(Long kgKnowledgeBasesId) {
        String json = (String) ctgRedisService.get(KnowledgeConstant.FILE_REDIS + kgKnowledgeBasesId);
        if (StringUtils.isNotBlank(json)) {
            log.debug("从Redis获取到文件信息, kgKnowledgeBasesId={}, jsonLength={}", kgKnowledgeBasesId, json.length());
            KgKnowledgeFile file = JSONObject.parseObject(json, KgKnowledgeFile.class);
            if (file != null && file.getSimilarFiles() != null) {
                List<SimilarFileInfo> similarFiles = file.getSimilarFiles();
                log.debug("获取到相似文件列表, kgKnowledgeBasesId={}, 相似文件数量={}", kgKnowledgeBasesId, similarFiles.size());
                int returnSize = Math.min(KnowledgeConstant.MAX_SIMILAR_FILES, similarFiles.size());
                log.debug("返回前{}个相似文件, kgKnowledgeBasesId={}", returnSize, kgKnowledgeBasesId);
                return similarFiles.subList(0, returnSize);
            }
        }
        log.debug("未找到相似文件列表或文件信息不存在, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
        return new ArrayList<>();
    }
    
    @Override
    public boolean clearFileSimilarityCache(Long kgKnowledgeBasesId) {
        log.info("开始清除文件相似度缓存, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
        try {
            //ctgRedisService.delete(KnowledgeConstant.FILE_REDIS + kgKnowledgeBasesId);
            log.debug("开始从数据库删除文档指纹, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
            int deleted = documentFingerprintMapper.deleteByKnowledgeBasesId(kgKnowledgeBasesId);
            log.info("成功清除文件相似度缓存, kgKnowledgeBasesId={}, 删除的记录数={}", kgKnowledgeBasesId, deleted);
            return true;
        } catch (Exception e) {
            log.error("清除文件相似度缓存异常, kgKnowledgeBasesId={}", kgKnowledgeBasesId, e);
            return false;
        }
    }
    
    /**
     * 获取相似度计算方法
     */
    private SimilarityMethod getSimilarityMethod() {
        String method = similarityConfig.getMethod();
        if ("COSINE".equalsIgnoreCase(method)) {
            return SimilarityMethod.COSINE;
        } else if ("COMBINED".equalsIgnoreCase(method)) {
            return SimilarityMethod.COMBINED;
        } else {
            return SimilarityMethod.SIMHASH;
        }
    }
    
    /**
     * 安全获取文件后缀，避免空指针
     */
    private String getSafeFileSuffix(String fileSuffix) {
        if (fileSuffix == null) {
            log.debug("文件后缀为null，返回空字符串");
            return "";
        }
        String result = fileSuffix.replace(".", "").toLowerCase();
        log.debug("文件后缀处理: 原始值={}, 处理后={}", fileSuffix, result);
        return result;
    }
    
    /**
     * 判断两个文件类型是否匹配
     * 按照文件类型分组规则进行比对
     */
    private boolean isFileTypeMatched(String fileType1, String fileType2) {
        log.debug("判断文件类型是否匹配, type1={}, type2={}", fileType1, fileType2);
        // 如果都为空，则认为匹配
        if (StringUtils.isBlank(fileType1) && StringUtils.isBlank(fileType2)) {
            log.debug("两个文件类型都为空，视为匹配");
            return true;
        }
        
        // 如果有一个为空，则不匹配
        if (StringUtils.isBlank(fileType1) || StringUtils.isBlank(fileType2)) {
            log.debug("有一个文件类型为空，视为不匹配");
            return false;
        }
        
        // 按分组规则判断
        List<String> textGroup = Arrays.asList(KnowledgeConstant.FILE_TYPE_GROUP_TEXT);
        if (textGroup.contains(fileType1) && textGroup.contains(fileType2)) {
            log.debug("两个文件都属于文本组，视为匹配");
            return true;
        }
        
        if (KnowledgeConstant.FILE_TYPE_GROUP_HTML.equals(fileType1) && 
            KnowledgeConstant.FILE_TYPE_GROUP_HTML.equals(fileType2)) {
            log.debug("两个文件都属于HTML组，视为匹配");
            return true;
        }
        
        if (KnowledgeConstant.FILE_TYPE_GROUP_EXCEL.equals(fileType1) && 
            KnowledgeConstant.FILE_TYPE_GROUP_EXCEL.equals(fileType2)) {
            log.debug("两个文件都属于Excel组，视为匹配");
            return true;
        }
        
        log.debug("两个文件类型不匹配");
        return false;
    }
    
    /**
     * 批量计算文件相似度 - 优化版本
     * 使用多线程并行处理单个文件与其他文件的相似度比较
     * 支持提前终止比较功能
     * 支持分段批量上报，避免服务挂掉导致已完成数据无法上报
     * 
     * @param batchIds 批次内的知识库ID列表
     */
    private void calculateBatchFileSimilarity(List<Long> batchIds) {
        if (batchIds == null || batchIds.isEmpty()) {
            log.warn("批量计算文件相似度任务已跳过，ID列表为空");
            return;
        }
        
        log.info("开始批量计算文件相似度, 批次大小={}", batchIds.size());
        
        // 分段上报配置：每完成指定数量的查重就触发一次批量上报检查
        final int SEGMENT_REPORT_SIZE = Math.max(5, Math.min(10, batchIds.size() / 5)); // 动态调整分段大小，最小5个，最大10个
        List<Long> completedIds = new ArrayList<>(); // 已完成查重的知识库ID列表
        
        log.info("分段批量上报配置 - 当前批次大小: {}, 分段阈值: {}", batchIds.size(), SEGMENT_REPORT_SIZE);
        
        // 1. 批量获取当前批次中的文件内容并生成指纹
        Map<Long, KgKnowledgeFile> batchFiles = new HashMap<>();
        Map<Long, String> batchFingerprints = new HashMap<>();
        Map<Long, String> batchFileSuffixes = new HashMap<>();
        Map<Long, DocumentFingerprint> batchDocumentFingerprints = new HashMap<>();
        
        log.info("开始获取批次中的文件内容并生成指纹, 批次大小={}", batchIds.size());
        
        for (Long id : batchIds) {
            try {
                // 从Redis获取文件内容
                String prefix = KnowledgeConstant.FILE_REDIS + id;
                String json = (String) ctgRedisService.get(prefix);
                
                if (StringUtils.isBlank(json)) {
                    log.warn("未找到文件内容, kgKnowledgeBasesId={}, 跳过处理", id);
                    continue;
                }
                
                KgKnowledgeFile file = JSON.parseObject(json, KgKnowledgeFile.class);
                if (file == null || StringUtils.isBlank(file.getFileContent())) {
                    log.warn("文件内容为空, kgKnowledgeBasesId={}, 跳过处理", id);
                    continue;
                }
                
                // 缓存文件对象
                batchFiles.put(id, file);
                
                // 获取文件类型
                String fileSuffix = getSafeFileSuffix(file.getFileSuffix());
                batchFileSuffixes.put(id, fileSuffix);
                
                // 生成SimHash指纹
                String simHash = EnhancedSimHashUtil.generateSimHash(file.getFileContent());
                batchFingerprints.put(id, simHash);
                
                // 创建指纹对象
                DocumentFingerprint fingerprint = DocumentFingerprint.builder()
                        .kgKnowledgeBasesId(id)
                        .operativeWord(file.getOperativeWord())
                        .fingerprint(simHash)
                        .fileType(fileSuffix)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build();
                
                batchDocumentFingerprints.put(id, fingerprint);
                
                log.debug("成功处理文件, kgKnowledgeBasesId={}, 文件类型={}, 内容长度={}", 
                        id, fileSuffix, file.getFileContent().length());
            } catch (Exception e) {
                log.error("处理文件异常, kgKnowledgeBasesId={}", id, e);
            }
        }
        
        int validFileCount = batchFiles.size();
        log.info("成功获取{}个有效文件内容和指纹 (总批次大小: {})", validFileCount, batchIds.size());
        
        if (validFileCount == 0) {
            log.warn("批次中没有有效文件, 跳过后续处理");
            return;
        }
        
        // 2. 批量保存或更新指纹到数据库
        log.info("开始批量保存或更新指纹到数据库, 数量={}", batchDocumentFingerprints.size());
        
        // 查询当前批次文件的已有指纹
        List<DocumentFingerprint> existingFingerprints = documentFingerprintMapper.selectBatchByKnowledgeBasesIds(batchIds);
        Map<Long, DocumentFingerprint> existingFingerprintsMap = existingFingerprints.stream()
                .collect(Collectors.toMap(DocumentFingerprint::getKgKnowledgeBasesId, fp -> fp));
        
        log.info("查询到{}个已存在的文档指纹记录", existingFingerprintsMap.size());
        
        List<DocumentFingerprint> toInsert = new ArrayList<>();
        List<DocumentFingerprint> toUpdate = new ArrayList<>();
        
        for (Map.Entry<Long, DocumentFingerprint> entry : batchDocumentFingerprints.entrySet()) {
            Long id = entry.getKey();
            DocumentFingerprint fingerprint = entry.getValue();
            
            if (existingFingerprintsMap.containsKey(id)) {
                // 更新已有指纹
                DocumentFingerprint existingFingerprint = existingFingerprintsMap.get(id);
                fingerprint.setKgDocumentFingerprintId(existingFingerprint.getKgDocumentFingerprintId());
                toUpdate.add(fingerprint);
            } else {
                // 插入新指纹
                toInsert.add(fingerprint);
            }
        }
        
        // 批量插入和更新
        if (!toInsert.isEmpty()) {
            log.info("批量插入{}个新指纹", toInsert.size());
            documentFingerprintMapper.batchInsert(toInsert);
        }
        
        if (!toUpdate.isEmpty()) {
            log.info("批量更新{}个已有指纹", toUpdate.size());
            documentFingerprintMapper.batchUpdate(toUpdate);
        }
        
        // 3. 获取所有文档指纹（包括数据库中已有的和当前批次的）
        log.info("获取所有文档指纹进行比对");
        List<DocumentFingerprint> allFingerprints = documentFingerprintMapper.selectList(null);
        log.info("从数据库获取到{}个文档指纹记录", allFingerprints.size());
        
        // 4. 解析相似度计算方法
        SimilarityMethod method = getSimilarityMethod();
        log.info("使用相似度计算方法: {}, 相似度阈值={}, 提前终止阈值={}",
                method, similarityConfig.getThreshold(), similarityConfig.getEarlyStopThreshold());
        
        // 5. 为每个文件计算相似度
        for (Long currentId : batchFiles.keySet()) {
            KgKnowledgeFile currentFile = batchFiles.get(currentId);
            String currentContent = currentFile.getFileContent();
            String currentFileSuffix = batchFileSuffixes.get(currentId);
            String currentSimHash = batchFingerprints.get(currentId);
            
            log.info("开始计算文件相似度, kgKnowledgeBasesId={}, 文件类型={}", currentId, currentFileSuffix);
            
            // 记录比较统计数据
            int processedCount = 0;
            int skippedSelfCount = 0;
            int skippedTypeCount = 0;
            int skippedEmptyCount = 0;
            int comparedCount = 0;
            
            // 筛选出要比较的文件列表
            List<CompareTask> compareTasks = new ArrayList<>();
            for (DocumentFingerprint otherFingerprint : allFingerprints) {
                processedCount++;
                
                // 跳过自己
                if (Objects.equals(otherFingerprint.getKgKnowledgeBasesId(), currentId)) {
                    skippedSelfCount++;
                    continue;
                }
                
                // 按文件类型分组进行比对
                if (!isFileTypeMatched(currentFileSuffix, otherFingerprint.getFileType())) {
                    skippedTypeCount++;
                    continue;
                }
                
                // 获取其他文件的内容
                String otherContent;
                if (batchFiles.containsKey(otherFingerprint.getKgKnowledgeBasesId())) {
                    KgKnowledgeFile otherFile = batchFiles.get(otherFingerprint.getKgKnowledgeBasesId());
                    otherContent = otherFile.getFileContent();
                    if (StringUtils.isBlank(otherContent)) {
                        skippedEmptyCount++;
                        continue;
                    }
                } else {
                    // 获取其他文件的内容
                    String otherJson = (String) ctgRedisService.get(KnowledgeConstant.FILE_REDIS + otherFingerprint.getKgKnowledgeBasesId());
                    if (StringUtils.isBlank(otherJson)) {
                        skippedEmptyCount++;
                        continue;
                    }
                    
                    KgKnowledgeFile otherFile = JSON.parseObject(otherJson, KgKnowledgeFile.class);
                    if (otherFile == null || StringUtils.isBlank(otherFile.getFileContent())) {
                        skippedEmptyCount++;
                        continue;
                    }
                    otherContent = otherFile.getFileContent();
                }
                
                // 创建比较任务并添加到列表
                CompareTask task = CompareTask.builder()
                        .currentFile(currentFile)
                        .currentId(currentId)
                        .currentContent(currentContent)
                        .currentSimHash(currentSimHash)
                        .currentFileSuffix(currentFileSuffix)
                        .otherFingerprint(otherFingerprint)
                        .otherContent(otherContent)
                        .method(method)
                        .threshold(similarityConfig.getThreshold())
                        .build();
                
                compareTasks.add(task);
                comparedCount++;
            }
            
            log.info("文件相似度比较筛选统计: 总处理={}, 跳过自身={}, 类型不匹配={}, 内容为空={}, 实际比对={}",
                    processedCount, skippedSelfCount, skippedTypeCount, skippedEmptyCount, comparedCount);
            
            // 如果没有可比较的任务，直接设置结果为0并继续下一个文件
            if (compareTasks.isEmpty()) {
                log.info("没有可比较的文件, 直接设置查重结果为0, kgKnowledgeBasesId={}", currentId);
                KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
                knowledgeBases.setKgKnowledgeBasesId(currentId);
                knowledgeBases.setCheckRepeatResult(BigDecimal.ZERO);
                knowledgeBasesDMapper.updateById(knowledgeBases);
                
                // 更新Redis中的相似文件列表（设置为空列表）
                currentFile.setSimilarFiles(new ArrayList<>());
                ctgRedisService.set(KnowledgeConstant.FILE_REDIS + currentId, JSONObject.toJSONString(currentFile));
                continue;
            }
            
            // 创建进度跟踪器
            ComparisonProgressTracker progressTracker = new ComparisonProgressTracker(
                    currentId, 
                    compareTasks.size(), 
                    similarityConfig.getEarlyStopThreshold(),
                    similarityConfig.getEnableEarlyStop()
            );
            
            // 6. 并行执行比较任务
            int compareParallelism = similarityConfig.getCompareParallelism();
            log.info("开始并行计算文件相似度, kgKnowledgeBasesId={}, 任务数={}, 并行度={}",
                    currentId, compareTasks.size(), compareParallelism);
            
            // 使用限制并发度的方式执行任务
            CompletionService<Void> completionService = new ExecutorCompletionService<>(similarityCompareThreadPool);
            List<Future<Void>> futures = new ArrayList<>();
            int submittedTasks = 0;
            
            for (CompareTask task : compareTasks) {
                // 每提交一批任务检查一次是否应该提前终止
                if (submittedTasks > 0 && submittedTasks % compareParallelism == 0) {
                    // 处理已完成的任务，更新进度
                    for (int i = 0; i < compareParallelism; i++) {
                        try {
                            Future<Void> future = completionService.poll(100, TimeUnit.MILLISECONDS);
                            if (future != null) {
                                future.get(); // 确保任务完成且无异常
                            }
                        } catch (Exception e) {
                            log.error("等待比较任务完成异常", e);
                        }
                    }
                    
                    // 检查是否应该提前终止
                    if (progressTracker.shouldEarlyStop()) {
                        log.info("提前终止比较, 已达到提前终止条件, kgKnowledgeBasesId={}, 最大相似度={}",
                                currentId, progressTracker.getMaxSimilarity());
                        break;
                    }
                }
                
                // 创建任务副本，以便在任务执行完后更新进度
                final CompareTask taskCopy = task;
                
                // 提交任务
                Future<Void> future = completionService.submit(() -> {
                    try {
                        taskCopy.run();
                        progressTracker.updateProgress(taskCopy);
                    } catch (Exception e) {
                        log.error("比较任务执行异常", e);
                    }
                    return null;
                });
                
                futures.add(future);
                submittedTasks++;
                
                // 如果发现高相似度文件，提前终止
                if (progressTracker.shouldEarlyStop()) {
                    log.info("提前终止提交任务, 已达到提前终止条件, kgKnowledgeBasesId={}, 最大相似度={}, 提交任务数/总任务数: {}/{}",
                            currentId, progressTracker.getMaxSimilarity(), submittedTasks, compareTasks.size());
                    break;
                }
            }
            
            // 等待所有已提交的任务完成
            log.debug("等待所有已提交的比较任务完成, kgKnowledgeBasesId={}, 提交任务数={}", currentId, submittedTasks);
            for (int i = 0; i < futures.size(); i++) {
                try {
                    futures.get(i).get();
                } catch (Exception e) {
                    log.error("等待比较任务完成异常", e);
                }
                
                // 每50个任务记录一次日志
                if ((i + 1) % 50 == 0 || i == futures.size() - 1) {
                    log.debug("比较任务进度: {}/{}, kgKnowledgeBasesId={}", i + 1, futures.size(), currentId);
                }
            }
            
            // 7. 处理比较结果
            List<SimilarFileInfo> similarList = progressTracker.getSimilarFiles();
            
            log.info("文件相似度计算完成: 提交任务数/总任务数={}/{}, 找到相似文件数={}, 最大相似度={}, kgKnowledgeBasesId={}",
                    submittedTasks, compareTasks.size(), similarList.size(), 
                    progressTracker.getMaxSimilarity(), currentId);
            
            // 8. 处理相似文件列表
            if (!similarList.isEmpty()) {
                // 按相似度降序排序
                similarList.sort((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()));
                
                // 只取前N个（如果列表不足N个，则取全部）
                int endIndex = Math.min(KnowledgeConstant.MAX_SIMILAR_FILES, similarList.size());
                similarList = similarList.subList(0, endIndex);
                
                // 更新Redis中的相似文件列表
                currentFile.setSimilarFiles(similarList);
                ctgRedisService.set(KnowledgeConstant.FILE_REDIS + currentId, JSONObject.toJSONString(currentFile));
                
                // 更新数据库中的查重结果
                if (!similarList.isEmpty()) {
                    Double maxSimilarity = similarList.get(0).getSimilarity();
                    KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
                    knowledgeBases.setKgKnowledgeBasesId(currentId);
                    knowledgeBases.setCheckRepeatResult(BigDecimal.valueOf(maxSimilarity));
                    knowledgeBasesDMapper.updateById(knowledgeBases);
                    log.info("更新数据库中的查重结果, kgKnowledgeBasesId={}, 最大相似度={}", currentId, maxSimilarity);
                }
            } else {
                log.info("未找到相似文件, kgKnowledgeBasesId={}", currentId);
                // 未找到相似文件，设置查重结果为0
                KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
                knowledgeBases.setKgKnowledgeBasesId(currentId);
                knowledgeBases.setCheckRepeatResult(BigDecimal.ZERO);
                knowledgeBasesDMapper.updateById(knowledgeBases);
                
                // 更新Redis中的相似文件列表（设置为空列表）
                currentFile.setSimilarFiles(new ArrayList<>());
                ctgRedisService.set(KnowledgeConstant.FILE_REDIS + currentId, JSONObject.toJSONString(currentFile));
            }
            
            // 查重完成，添加到已完成列表，并检查是否需要分段上报
            completedIds.add(currentId);
            log.debug("知识库查重完成，添加到待上报队列 - 知识库ID: {}, 当前队列大小: {}", currentId, completedIds.size());
            
            // 当达到分段大小时，触发批量上报检查
            if (completedIds.size() >= SEGMENT_REPORT_SIZE) {
                log.info("达到分段上报阈值，开始分段批量上报检查 - 当前分段大小: {}, 阈值: {}", completedIds.size(), SEGMENT_REPORT_SIZE);
                try {
                    List<Long> segmentIds = new ArrayList<>(completedIds); // 创建副本避免并发修改
                    batchCheckAndAutoReportAfterSimilarityCheck(segmentIds);
                    completedIds.clear(); // 清空已处理的ID列表
                    log.info("分段批量上报检查完成，队列已清空");
                } catch (Exception e) {
                    log.error("分段批量上报检查异常，继续处理后续文件 - 分段大小: {}", completedIds.size(), e);
                    // 即使上报失败也要清空列表，避免重复上报
                    completedIds.clear();
                }
            }
        }
        
        log.info("批量文件相似度计算全部完成, 批次大小={}", batchIds.size());
        
        // 处理剩余的未达到分段阈值的已完成ID（最后一次上报检查）
        if (!completedIds.isEmpty()) {
            log.info("处理剩余的已完成查重文件，开始最后一次批量上报检查 - 剩余数量: {}", completedIds.size());
            try {
                batchCheckAndAutoReportAfterSimilarityCheck(completedIds);
                log.info("最后一次批量上报检查完成 - 处理数量: {}", completedIds.size());
            } catch (Exception e) {
                log.error("最后一次批量上报检查异常 - 剩余数量: {}", completedIds.size(), e);
            }
        } else {
            log.info("无剩余待上报的已完成文件");
        }
    }

    /**
     * 批量查重完成后检查是否需要自动上报（优化性能版本）
     * 
     * @param batchIds 批次知识库ID列表
     */
    private void batchCheckAndAutoReportAfterSimilarityCheck(List<Long> batchIds) {
        try {
            if (batchIds == null || batchIds.isEmpty()) {
                log.warn("批次ID列表为空，跳过自动上报检查");
                return;
            }
            
            log.info("开始批量检查查重完成后的自动上报条件 - 批次大小: {}, ID列表: {}", batchIds.size(), batchIds);
            
            // 1. 批量查询知识库信息
            List<KnowledgeBasesEntity> knowledgeBases = knowledgeBasesDMapper.selectBatchIds(batchIds);
            if (knowledgeBases == null || knowledgeBases.isEmpty()) {
                log.warn("未找到有效的知识库数据，跳过自动上报检查");
                return;
            }
            
            // 2. 批量检查条件并筛选出符合上报条件的知识库
            List<Long> readyToReportIds = new ArrayList<>();
            List<Long> highRepeatRateIds = new ArrayList<>();  // 新增：高重复率ID列表
            Map<Long, String> notReadyReasons = new HashMap<>();
            
            for (KnowledgeBasesEntity knowledgeBase : knowledgeBases) {
                Long knowledgeBaseId = knowledgeBase.getKgKnowledgeBasesId();
                try {
                    // 检查查重率是否合格（小于0.9即90%）
                    BigDecimal checkRepeatResult = knowledgeBase.getCheckRepeatResult();
                    if (checkRepeatResult == null || checkRepeatResult.compareTo(BigDecimal.valueOf(-1)) == 0) {
                        notReadyReasons.put(knowledgeBaseId, "查重未完成");
                        continue;
                    }
                    
                    // 新增：处理高重复率的情况
                    if (checkRepeatResult.compareTo(BigDecimal.valueOf(0.9)) >= 0) {
                        log.info("发现高重复率知识库，查重率: {}, 知识库ID: {}", checkRepeatResult, knowledgeBaseId);
                        highRepeatRateIds.add(knowledgeBaseId);
                        notReadyReasons.put(knowledgeBaseId, String.format("查重率超标，已处理为审核不通过: %s", checkRepeatResult));
                        continue;
                    }

                    
                    // 检查文档格式是否符合要求
                    String documentFormat = knowledgeBase.getDocumentFormat();
                    if (!isDocumentFormatEligible(documentFormat)) {
                        notReadyReasons.put(knowledgeBaseId, String.format("文档格式不符合要求: %s", documentFormat));
                        continue;
                    }
                    
                    // 检查是否已经上报过（避免重复上报）
                    String reportStatus = knowledgeBase.getReportStatus();
                    if (isAlreadyReported(reportStatus)) {
                        notReadyReasons.put(knowledgeBaseId, String.format("已上报过: %s", reportStatus));
                        continue;
                    }
                    
                    // 所有条件都满足，添加到待上报列表
                    readyToReportIds.add(knowledgeBaseId);
                    log.info("知识库满足上报条件 - 知识库ID: {}, 文档格式: {}, 查重率: {}",
                            knowledgeBaseId, documentFormat, checkRepeatResult);
                    
                } catch (Exception e) {
                    notReadyReasons.put(knowledgeBaseId, "检查条件异常: " + e.getMessage());
                    log.error("检查知识库上报条件异常 - 知识库ID: {}", knowledgeBaseId, e);
                }
            }
            
            // 2.5. 新增：批量处理高重复率的知识库
            if (!highRepeatRateIds.isEmpty()) {
                log.info("开始批量处理高重复率知识库，数量: {}, ID列表: {}", highRepeatRateIds.size(), highRepeatRateIds);
                batchHandleHighRepeatRate(highRepeatRateIds);
            }
            
            // 3. 记录检查结果
            log.info("批量查重完成后上报条件检查完成 - 总数: {}, 满足条件: {}, 高重复率: {}, 不满足条件: {}", 
                    batchIds.size(), readyToReportIds.size(), highRepeatRateIds.size(), notReadyReasons.size());
            
            if (!notReadyReasons.isEmpty()) {
                log.info("不满足上报条件的知识库统计: {}", notReadyReasons);
            }
            
            // 4. 批量自动上报（性能优化：一次性调用批量接口）
            if (!readyToReportIds.isEmpty()) {
                log.info("开始批量自动上报满足条件的知识库，数量: {}, ID列表: {}", readyToReportIds.size(), readyToReportIds);
                performBatchAutoReport(readyToReportIds);
            } else {
                log.info("没有满足上报条件的知识库，跳过自动上报");
            }
            
        } catch (Exception e) {
            log.error("批量查重完成后检查自动上报条件异常 - 批次大小: {}", batchIds.size(), e);
        }
    }

    /**
     * 批量处理高重复率知识库
     * 将待审核或待发布状态的知识库改为审核不通过，并创建相应的审核维度记录
     * 
     * @param highRepeatRateIds 高重复率知识库ID列表
     */
    private void batchHandleHighRepeatRate(List<Long> highRepeatRateIds) {
        try {
            if (highRepeatRateIds == null || highRepeatRateIds.isEmpty()) {
                log.warn("高重复率知识库ID列表为空，跳过处理");
                return;
            }
            
            log.info("开始批量处理高重复率知识库 - 数量: {}", highRepeatRateIds.size());
            
            // 1. 批量查询知识库状态，筛选出待审核或待发布状态的知识库
            List<KnowledgeBasesEntity> knowledgeBases = knowledgeBasesDMapper.selectBatchIds(highRepeatRateIds);
            List<Long> toProcessIds = new ArrayList<>();
            
            for (KnowledgeBasesEntity knowledgeBase : knowledgeBases) {
                String state = knowledgeBase.getState();
                // 检查是否为待审核(2)或待发布(4)状态
                if (CaseStateEnum.PENDING_AUDIT.getStateValue().equals(state) ||
                    CaseStateEnum.TO_BE_RELEASED.getStateValue().equals(state)) {
                    toProcessIds.add(knowledgeBase.getKgKnowledgeBasesId());
                    log.info("发现需要处理的高重复率知识库 - 知识库ID: {}, 当前状态: {}", 
                            knowledgeBase.getKgKnowledgeBasesId(), state);
                } else {
                    log.info("知识库状态不需要处理 - 知识库ID: {}, 当前状态: {}", 
                            knowledgeBase.getKgKnowledgeBasesId(), state);
                }
            }
            
            if (toProcessIds.isEmpty()) {
                log.info("没有需要处理状态的高重复率知识库");
                return;
            }
            
            log.info("筛选出需要处理的高重复率知识库数量: {}, ID列表: {}", toProcessIds.size(), toProcessIds);

            // 2. 批量处理每个知识库
            for (Long knowledgeBaseId : toProcessIds) {
                try {
                    handleSingleHighRepeatRate(knowledgeBaseId);
                    log.info("成功处理高重复率知识库 - 知识库ID: {}", knowledgeBaseId);
                } catch (Exception e) {
                    log.error("处理高重复率知识库异常 - 知识库ID: {}", knowledgeBaseId, e);
                }
            }
            
            log.info("批量处理高重复率知识库完成 - 总处理数量: {}", toProcessIds.size());
            
        } catch (Exception e) {
            log.error("批量处理高重复率知识库异常 - 数量: {}", highRepeatRateIds.size(), e);
        }
    }
    
    /**
     * 处理单个高重复率知识库
     * 
     * @param knowledgeBaseId 知识库ID
     */
    private void handleSingleHighRepeatRate(Long knowledgeBaseId) {
        try {
            log.info("开始处理单个高重复率知识库 - 知识库ID: {}", knowledgeBaseId);
            KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesDMapper.selectById(knowledgeBaseId);
            if (Objects.isNull(knowledgeBasesEntity)) {
                log.error("未找到知识库 - 知识库ID: {}", knowledgeBaseId);
                return;
            }
            if (!CaseStateEnum.isRepeatFail(knowledgeBasesEntity.getState())) {
                log.error("知识库状态不符合要求 - 知识库ID: {}, 当前状态: {}",knowledgeBasesEntity.getState());
                return;
            }

            // 1. 查询审核维度配置（查找isSystem=1且comment="repeat"的记录）
            QueryWrapper<KgBasesAuditDimensionConfigD> configQuery = new QueryWrapper<>();
            configQuery.eq("kg_knowledge_bases_id", knowledgeBaseId)
                      .eq("is_system", "1")
                      .eq("comment", "repeat");
            
            List<KgBasesAuditDimensionConfigD> repeatConfigs = basesAuditDimensionConfigMapper.selectList(configQuery);
            
            if (CollectionUtil.isEmpty(repeatConfigs)) {
                log.warn("未找到重复检查的审核维度配置 - 知识库ID: {}", knowledgeBaseId);
                // 如果没有找到repeat配置，查询所有的审核维度配置
                QueryWrapper<KgBasesAuditDimensionConfigD> allConfigQuery = new QueryWrapper<>();
                allConfigQuery.eq("kg_knowledge_bases_id", knowledgeBaseId);
                List<KgBasesAuditDimensionConfigD> allConfigs = basesAuditDimensionConfigMapper.selectList(allConfigQuery);
                log.info("找到所有审核维度配置数量: {} - 知识库ID: {}", allConfigs.size(), knowledgeBaseId);
                
                // 创建所有维度的审核记录，但只对系统类型的repeat维度设置不通过
                createAuditDimensionRecords(knowledgeBaseId, allConfigs, true);
            } else {
                log.info("找到重复检查的审核维度配置数量: {} - 知识库ID: {}", repeatConfigs.size(), knowledgeBaseId);
                
                // 查询所有审核维度配置，创建完整的审核记录
                QueryWrapper<KgBasesAuditDimensionConfigD> allConfigQuery = new QueryWrapper<>();
                allConfigQuery.eq("kg_knowledge_bases_id", knowledgeBaseId);
                List<KgBasesAuditDimensionConfigD> allConfigs = basesAuditDimensionConfigMapper.selectList(allConfigQuery);
                
                createAuditDimensionRecords(knowledgeBaseId, allConfigs, false);
            }
            
            // 2. 更新知识库状态为审核不通过
            KnowledgeBasesEntity updateEntity = new KnowledgeBasesEntity();
            updateEntity.setKgKnowledgeBasesId(knowledgeBaseId);
            updateEntity.setState(CaseStateEnum.AUDIT_NOT_PASS.getStateValue());
            updateEntity.setAuditStatus(AuditEnum.NO_PASS.getAuditStatus()); // 3-审核不通过
            int updateResult = knowledgeBasesDMapper.updateById(updateEntity);
            
            if (updateResult > 0) {
                log.info("成功更新知识库状态为审核不通过 - 知识库ID: {}", knowledgeBaseId);
            } else {
                log.warn("更新知识库状态失败 - 知识库ID: {}", knowledgeBaseId);
            }
            
        } catch (Exception e) {
            log.error("处理单个高重复率知识库异常 - 知识库ID: {}", knowledgeBaseId, e);
        }
    }
    
    /**
     * 创建审核维度记录
     * 
     * @param knowledgeBaseId 知识库ID
     * @param configs 审核维度配置列表
     * @param createDefaultRepeat 是否创建默认的repeat维度记录
     */
    private void createAuditDimensionRecords(Long knowledgeBaseId, List<KgBasesAuditDimensionConfigD> configs, boolean createDefaultRepeat) {
        try {
            log.info("开始创建审核维度记录 - 知识库ID: {}, 配置数量: {}, 创建默认repeat: {}", 
                    knowledgeBaseId, configs.size(), createDefaultRepeat);
            
            // 先删除现有的审核记录
            QueryWrapper<KgBasesAuditDimensionD> deleteQuery = new QueryWrapper<>();
            deleteQuery.eq("kg_knowledge_bases_id", knowledgeBaseId);
            basesAuditDimensionMapper.delete(deleteQuery);
            log.info("已删除现有审核维度记录 - 知识库ID: {}", knowledgeBaseId);
            
            AtomicReference<Integer> orderNum = new AtomicReference<>(0);
            String currentUser = getCurrentUser();
            Date now = new Date();
            
            // 如果需要创建默认的repeat记录且没有找到repeat配置
            if (createDefaultRepeat) {
                log.info("创建默认的repeat审核维度记录 - 知识库ID: {}", knowledgeBaseId);
                
                KgBasesAuditDimensionD repeatRecord = new KgBasesAuditDimensionD();
                repeatRecord.setCreatedTime(now);
                repeatRecord.setCreatedUserName(currentUser);
                repeatRecord.setUpdatedTime(now);
                repeatRecord.setUpdatedUserName(currentUser);
                orderNum.set(orderNum.get() + 1);
                repeatRecord.setOrder(orderNum.get());
                repeatRecord.setComment("重复率检查不通过，重复率超过90%");
                repeatRecord.setWhetherPass(1); // 1为不通过
                repeatRecord.setKgKnowledgeBasesId(knowledgeBaseId);
                repeatRecord.setKgBasesAuditDimensionId(null);
                repeatRecord.setDimensionName("重复率检查");
                repeatRecord.setScore("0");
                
                basesAuditDimensionMapper.insert(repeatRecord);
                log.info("成功创建默认repeat审核维度记录 - 知识库ID: {}", knowledgeBaseId);
            }
            
            // 创建所有配置对应的审核记录
            for (KgBasesAuditDimensionConfigD config : configs) {
                KgBasesAuditDimensionD auditRecord = new KgBasesAuditDimensionD();
                auditRecord.setCreatedTime(now);
                auditRecord.setCreatedUserName(currentUser);
                auditRecord.setUpdatedTime(now);
                auditRecord.setUpdatedUserName(currentUser);
                orderNum.set(orderNum.get() + 1);
                auditRecord.setOrder(orderNum.get());
                auditRecord.setKgKnowledgeBasesId(knowledgeBaseId);
                auditRecord.setKgBasesAuditDimensionId(null);
                auditRecord.setDimensionName(config.getDimensionName());
                auditRecord.setScore(config.getScore());
                
                // 判断是否为系统的repeat维度
                boolean isSystemRepeat = "1".equals(config.getIsSystem()) && "repeat".equals(config.getComment());
                
                if (isSystemRepeat) {
                    // 系统的repeat维度设置为不通过
                    auditRecord.setComment("重复率检查不通过，重复率超过90%");
                    auditRecord.setWhetherPass(1); // 1为不通过
                    log.info("设置系统repeat维度为不通过 - 知识库ID: {}, 维度名称: {}", 
                            knowledgeBaseId, config.getDimensionName());
                } else {
                    // 其他维度先空着
                    auditRecord.setComment("");
                    auditRecord.setWhetherPass(null);
                    log.debug("创建空的审核维度记录 - 知识库ID: {}, 维度名称: {}", 
                            knowledgeBaseId, config.getDimensionName());
                }
                
                basesAuditDimensionMapper.insert(auditRecord);
            }
            
            log.info("成功创建审核维度记录 - 知识库ID: {}, 总记录数: {}", knowledgeBaseId, configs.size());
            
        } catch (Exception e) {
            log.error("创建审核维度记录异常 - 知识库ID: {}", knowledgeBaseId, e);
        }
    }
    
    /**
     * 获取当前用户名
     * 
     * @return 当前用户名
     */
    private String getCurrentUser() {
        try {
            // 尝试使用反射调用PtSecurityUtils.getUsername()
            return PtSecurityUtils.getUsername();
        } catch (Exception e) {
            log.debug("无法获取当前用户，使用默认用户：{}", e.getMessage());
            return "system";
        }
    }

    /**
     * 检查文档格式是否符合上报要求
     * 简化版实现，实际项目中应该从字典表动态获取
     * 
     * @param documentFormat 文档格式
     * @return 是否符合要求
     */
    private boolean isDocumentFormatEligible(String documentFormat) {
        if (StringUtils.isBlank(documentFormat)) {
            return false;
        }
        
        // 支持的文档格式列表（简化版，实际应该从字典表获取）
        Set<String> supportedFormats = new HashSet<>(Arrays.asList(
                "pdf", "doc", "docx", "ppt", "pptx", "html", "xls", "xlsx"
        ));
        
        return supportedFormats.contains(documentFormat.toLowerCase());
    }

    /**
     * 检查是否已经上报过
     * 
     * @param reportStatus 上报状态
     * @return 是否已上报
     */
    private boolean isAlreadyReported(String reportStatus) {
        if (StringUtils.isBlank(reportStatus)) {
            return false;
        }
        
        // 已上报的状态包括：已上报集团、集团已下线、集团已删除
        return "11633876".equals(reportStatus) || 
               "11633877".equals(reportStatus) || 
               "11633878".equals(reportStatus);
    }

    /**
     * 执行批量自动上报（性能优化版本）
     * 
     * @param knowledgeBaseIds 知识库ID列表
     */
    private void performBatchAutoReport(List<Long> knowledgeBaseIds) {
        try {
            if (knowledgeBaseIds == null || knowledgeBaseIds.isEmpty()) {
                log.warn("知识库ID列表为空，跳过批量自动上报");
                return;
            }
            
            log.info("开始执行批量自动上报 - 知识库数量: {}, ID列表: {}", knowledgeBaseIds.size(), knowledgeBaseIds);
            
            // 调用批量上报服务接口
            ServiceResp<Map<String, Object>> response = knowledgeReportClient.batchReportKnowledgeBases(knowledgeBaseIds);
            
            if (response != null && response.isSuccess()) {
                log.info("批量自动上报成功 - 知识库数量: {}, 响应: {}", knowledgeBaseIds.size(), response.getHead().getRespMsg());
                
                // 记录上报详情
                Map<String, Object> data = response.getBody();
                if (data != null) {
                    Object successCount = data.get("successCount");
                    Object failCount = data.get("failCount");
                    Object successIds = data.get("successIds");
                    Object failedIds = data.get("failedIds");
                    
                    log.info("批量上报详细结果 - 成功: {}, 失败: {}, 成功ID: {}, 失败ID: {}", 
                            successCount, failCount, successIds, failedIds);
                }
            } else {
                String errorMsg = response != null ? response.getHead().getRespMsg() : "响应为空";
                log.error("批量自动上报失败 - 知识库数量: {}, 错误: {}", knowledgeBaseIds.size(), errorMsg);
            }
            
        } catch (Exception e) {
            log.error("执行批量自动上报异常 - 知识库数量: {}", knowledgeBaseIds.size(), e);
        }
    }
    
    @Override
    public ServiceResp<Object> processStuckCheckingDataOptimized(Integer hoursAgo, Integer batchSize, Integer maxRetryCount) {
        // 默认参数
        int defaultHoursAgo = (hoursAgo != null && hoursAgo > 0) ? hoursAgo : 3;
        int defaultBatchSize = (batchSize != null && batchSize > 0) ? batchSize : 15;
        int defaultMaxRetryCount = (maxRetryCount != null && maxRetryCount > 0) ? maxRetryCount : 3;
        
        log.info("开始处理查重中断数据（优化版本）, hoursAgo={}, batchSize={}, maxRetryCount={}", 
                defaultHoursAgo, defaultBatchSize, defaultMaxRetryCount);
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 计算查询时间点
            LocalDateTime beforeTime = LocalDateTime.now().minusHours(defaultHoursAgo);
            log.info("查询时间阈值: {}", beforeTime);
            
            // 2. 使用优化的查询方法，避免重复处理失败数据
            long queryStartTime = System.currentTimeMillis();
            List<Long> retryableIds = knowledgeBasesDMapper.selectRetryableCheckingIds(beforeTime, defaultMaxRetryCount, defaultBatchSize);
            long queryDuration = System.currentTimeMillis() - queryStartTime;
            
            if (retryableIds == null || retryableIds.isEmpty()) {
                long totalDuration = System.currentTimeMillis() - startTime;
                log.info("没有找到需要重新处理的查重数据, 查询耗时={}ms, 总耗时={}ms", queryDuration, totalDuration);
                return ServiceResp.success("没有找到需要重新处理的查重数据");
            }
            
            log.info("找到{}条需要重新处理的查重数据: {}, 查询耗时={}ms", retryableIds.size(), retryableIds, queryDuration);
            
            // 3. 更新状态为查重中
            long updateStartTime = System.currentTimeMillis();
            boolean updateSuccess = updateCheckStatusToChecking(retryableIds);
            long updateDuration = System.currentTimeMillis() - updateStartTime;
            
            if (!updateSuccess) {
                long totalDuration = System.currentTimeMillis() - startTime;
                log.error("更新查重状态失败, 更新耗时={}ms, 总耗时={}ms", updateDuration, totalDuration);
                return ServiceResp.fail("更新查重状态失败");
            }
            
            log.info("成功更新{}条记录的查重状态, 更新耗时={}ms", retryableIds.size(), updateDuration);
            
            // 4. 使用统一查重处理
            log.info("成功更新{}条记录的查重状态, 更新耗时={}ms", retryableIds.size(), updateDuration);
            
            long processStartTime = System.currentTimeMillis();
            CompletableFuture<Void> future = unifiedSimilarityCheck(retryableIds, "定时任务优化版");
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", retryableIds.size());
            result.put("maxRetryCount", defaultMaxRetryCount);
            result.put("hoursAgo", defaultHoursAgo);
            result.put("taskSubmitted", true);
            result.put("queryDuration", queryDuration);
            result.put("updateDuration", updateDuration);
            
            long totalDuration = System.currentTimeMillis() - startTime;
            log.info("查重中断数据处理完成，总数: {}, 最大重试次数: {}, 总耗时: {}ms", 
                    retryableIds.size(), defaultMaxRetryCount, totalDuration);
            
            return ServiceResp.success("处理完成", result);
            
        } catch (Exception e) {
            long totalDuration = System.currentTimeMillis() - startTime;
            log.error("处理查重中断数据异常, 总耗时: {}ms", totalDuration, e);
            return ServiceResp.fail("处理查重中断数据异常: " + e.getMessage());
        }
    }
    
    @Override
    public boolean updateCheckStatusToChecking(List<Long> kgKnowledgeBasesIds) {
        if (kgKnowledgeBasesIds == null || kgKnowledgeBasesIds.isEmpty()) {
            log.warn("更新查重状态的ID列表为空");
            return false;
        }
        
        log.info("批量更新查重状态为查重中, 数量={}", kgKnowledgeBasesIds.size());
        try {
            int updateCount = knowledgeBasesDMapper.updateCheckStatusToChecking(kgKnowledgeBasesIds);
            log.info("成功更新{}条记录的查重状态为查重中", updateCount);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("批量更新查重状态异常, 数量={}", kgKnowledgeBasesIds.size(), e);
            return false;
        }
    }
    
    @Override
    public boolean updateCheckStatusToSuccess(Long kgKnowledgeBasesId, BigDecimal checkRepeatResult) {
        if (kgKnowledgeBasesId == null) {
            log.warn("更新查重状态的ID为空");
            return false;
        }
        
        log.debug("更新查重状态为成功, kgKnowledgeBasesId={}, result={}", kgKnowledgeBasesId, checkRepeatResult);
        try {
            int updateCount = knowledgeBasesDMapper.updateCheckStatusToSuccess(kgKnowledgeBasesId, checkRepeatResult);
            if (updateCount > 0) {
                log.debug("成功更新查重状态为成功, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
                return true;
            } else {
                log.warn("更新查重状态为成功失败，可能记录不存在, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新查重状态为成功异常, kgKnowledgeBasesId={}", kgKnowledgeBasesId, e);
            return false;
        }
    }
    
    @Override
    public boolean updateCheckStatusToFailed(Long kgKnowledgeBasesId, String failReason) {
        if (kgKnowledgeBasesId == null) {
            log.warn("更新查重状态的ID为空");
            return false;
        }
        
        String safeFailReason = failReason;
        if (safeFailReason != null && safeFailReason.length() > 500) {
            safeFailReason = safeFailReason.substring(0, 500) + "...";
        }
        
        log.warn("更新查重状态为失败, kgKnowledgeBasesId={}, reason={}", kgKnowledgeBasesId, safeFailReason);
        try {
            int updateCount = knowledgeBasesDMapper.updateCheckStatusToFailed(kgKnowledgeBasesId, safeFailReason, null);
            if (updateCount > 0) {
                log.info("成功更新查重状态为失败, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
                return true;
            } else {
                log.warn("更新查重状态为失败失败，可能记录不存在, kgKnowledgeBasesId={}", kgKnowledgeBasesId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新查重状态为失败异常, kgKnowledgeBasesId={}", kgKnowledgeBasesId, e);
            return false;
        }
    }
    
    /**
     * 轻量级批量查重处理（带状态管理）
     * 重新设计：使用批量处理逻辑，提升性能
     */
    private CompletableFuture<Void> lightweightBatchCheckSimilarityWithStatusAsync(List<Long> kgKnowledgeBasesIds) {
        log.info("开始轻量级批量查重处理（带状态管理）, 数量={}", kgKnowledgeBasesIds.size());
        long startTime = System.currentTimeMillis();
        
        return CompletableFuture.runAsync(() -> {
            try {
                // 使用批量处理逻辑，而不是单条处理
                batchCheckSimilarityWithStatusManagement(kgKnowledgeBasesIds);
                
                long duration = System.currentTimeMillis() - startTime;
                log.info("轻量级批量查重处理（带状态管理）完成, 数量={}, 耗时={}ms", kgKnowledgeBasesIds.size(), duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("轻量级批量查重处理（带状态管理）异常, 数量={}, 耗时={}ms", kgKnowledgeBasesIds.size(), duration, e);
            }
        }, similarityBatchThreadPool);
    }
    
    /**
     * 批量查重处理（带状态管理）
     * 复用原有的批量处理逻辑，加上状态管理
     */
    private void batchCheckSimilarityWithStatusManagement(List<Long> kgKnowledgeBasesIds) {
        if (kgKnowledgeBasesIds == null || kgKnowledgeBasesIds.isEmpty()) {
            log.warn("批量查重处理（带状态管理）：ID列表为空");
            return;
        }
        
        log.info("开始批量查重处理（带状态管理）, 数量={}", kgKnowledgeBasesIds.size());
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 批量获取文件内容（复用原有逻辑）
            Map<Long, KgKnowledgeFile> fileContentMap = batchGetFileContents(kgKnowledgeBasesIds);
            
            if (fileContentMap.isEmpty()) {
                log.warn("没有获取到任何文件内容，更新失败状态");
                // 批量更新为失败状态
                for (Long id : kgKnowledgeBasesIds) {
                    updateCheckStatusToFailed(id, "文件内容不存在于Redis");
                }
                return;
            }
            
            log.info("成功获取{}个文件内容，开始相似度计算", fileContentMap.size());
            
            // 2. 使用原有的批量相似度计算逻辑（保留90%停止等优化）
            batchCalculateSimilarityWithStatus(fileContentMap);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("批量查重处理（带状态管理）完成, 处理数量={}, 耗时={}ms", fileContentMap.size(), duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("批量查重处理（带状态管理）异常, 数量={}, 耗时={}ms", kgKnowledgeBasesIds.size(), duration, e);
            
            // 异常情况下批量更新为失败状态
            for (Long id : kgKnowledgeBasesIds) {
                updateCheckStatusToFailed(id, "查重处理异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 批量获取文件内容（优化版本）
     */
    private Map<Long, KgKnowledgeFile> batchGetFileContents(List<Long> kgKnowledgeBasesIds) {
        Map<Long, KgKnowledgeFile> fileContentMap = new ConcurrentHashMap<>();
        
        // 使用并发获取文件内容，但控制并发数
        Semaphore semaphore = new Semaphore(Math.min(10, kgKnowledgeBasesIds.size()));
        CountDownLatch latch = new CountDownLatch(kgKnowledgeBasesIds.size());
        
        for (Long id : kgKnowledgeBasesIds) {
            try {
                semaphore.acquire();
                similarityCompareThreadPool.execute(() -> {
                    try {
                        String prefix = KnowledgeConstant.FILE_REDIS + id;
                        String json = (String) ctgRedisService.get(prefix);
                        
                        if (StringUtils.isNotBlank(json)) {
                            KgKnowledgeFile file = JSON.parseObject(json, KgKnowledgeFile.class);
                            if (file != null && StringUtils.isNotBlank(file.getFileContent())) {
                                fileContentMap.put(id, file);
                            } else {
                                log.warn("文件内容为空，ID: {}", id);
                                updateCheckStatusToFailed(id, "文件内容为空");
                            }
                        } else {
                            log.warn("Redis中无文件内容，ID: {}", id);
                            updateCheckStatusToFailed(id, "文件内容不存在于Redis");
                        }
                    } catch (Exception e) {
                        log.error("获取文件内容异常，ID: {}", id, e);
                        updateCheckStatusToFailed(id, "获取文件内容异常: " + e.getMessage());
                    } finally {
                        semaphore.release();
                        latch.countDown();
                    }
                });
            } catch (InterruptedException e) {
                log.error("获取信号量被中断", e);
                latch.countDown();
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        try {
            boolean finished = latch.await(60, TimeUnit.SECONDS);
            if (!finished) {
                log.warn("批量获取文件内容超时");
            }
        } catch (InterruptedException e) {
            log.error("等待文件内容获取被中断", e);
            Thread.currentThread().interrupt();
        }
        
        return fileContentMap;
    }
    
    /**
     * 批量相似度计算（带状态管理）
     * 复用原有的批量处理逻辑，保留90%停止等优化
     */
    private void batchCalculateSimilarityWithStatus(Map<Long, KgKnowledgeFile> fileContentMap) {
        if (fileContentMap.isEmpty()) {
            return;
        }
        
        log.info("开始批量相似度计算（带状态管理），文件数量: {}", fileContentMap.size());
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 查询所有需要比较的文档指纹
            List<DocumentFingerprint> allFingerprints = getAllDocumentFingerprints();
            
            if (allFingerprints.isEmpty()) {
                log.info("没有已存在的文档指纹，所有文件查重率为0");
                // 批量更新为成功状态，查重率为0
                for (Long id : fileContentMap.keySet()) {
                    updateCheckResultAndStatus(id, BigDecimal.ZERO);
                }
                return;
            }
            
            log.info("获取到{}个已存在的文档指纹，开始相似度计算", allFingerprints.size());
            
            // 2. 批量计算相似度（复用原有逻辑，保留性能优化）
            batchCalculateSimilarityOptimized(fileContentMap, allFingerprints);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("批量相似度计算（带状态管理）完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("批量相似度计算（带状态管理）异常，耗时: {}ms", duration, e);
            
            // 异常情况下批量更新为失败状态
            for (Long id : fileContentMap.keySet()) {
                updateCheckStatusToFailed(id, "相似度计算异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 批量相似度计算（优化版本）
     * 保留原有的90%停止等性能优化
     */
    private void batchCalculateSimilarityOptimized(Map<Long, KgKnowledgeFile> fileContentMap, 
                                                  List<DocumentFingerprint> allFingerprints) {
        
        // 控制并发数，避免线程池嵌套过多
        int maxConcurrent = Math.min(6, fileContentMap.size());
        Semaphore semaphore = new Semaphore(maxConcurrent);
        CountDownLatch latch = new CountDownLatch(fileContentMap.size());
        
        log.info("开始批量相似度计算，并发数: {}，文件数量: {}", maxConcurrent, fileContentMap.size());
        
        for (Map.Entry<Long, KgKnowledgeFile> entry : fileContentMap.entrySet()) {
            Long kgKnowledgeBasesId = entry.getKey();
            KgKnowledgeFile currentFile = entry.getValue();
            
            try {
                semaphore.acquire();
                
                // 直接在当前线程池中执行，避免线程池嵌套
                similarityCompareThreadPool.execute(() -> {
                    try {
                        calculateSingleFileSimilarityOptimized(kgKnowledgeBasesId, currentFile, allFingerprints);
                    } finally {
                        semaphore.release();
                        latch.countDown();
                    }
                });
                
            } catch (InterruptedException e) {
                log.error("获取相似度计算信号量被中断", e);
                latch.countDown();
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        try {
            boolean finished = latch.await(300, TimeUnit.SECONDS); // 5分钟超时
            if (!finished) {
                log.warn("批量相似度计算超时，部分任务可能仍在执行");
            }
        } catch (InterruptedException e) {
            log.error("等待相似度计算完成被中断", e);
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 单个文件相似度计算（优化版本）
     * 保留原有的90%停止等性能优化
     */
    private void calculateSingleFileSimilarityOptimized(Long kgKnowledgeBasesId, KgKnowledgeFile currentFile, 
                                                        List<DocumentFingerprint> allFingerprints) {
        log.debug("开始计算文件相似度，ID: {}", kgKnowledgeBasesId);
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 计算当前文件的文档指纹
            DocumentFingerprint currentFingerprint = calculateDocumentFingerprint(currentFile.getFileContent());
            
            // 2. 与现有文档比较相似度（保留90%停止优化）
            BigDecimal maxSimilarity = BigDecimal.ZERO;
            List<SimilarFileInfo> similarFiles = new ArrayList<>();
            
            for (DocumentFingerprint existingFingerprint : allFingerprints) {
                // 跳过自己
                if (existingFingerprint.getKgKnowledgeBasesId().equals(kgKnowledgeBasesId)) {
                    continue;
                }
                
                // 计算相似度
                BigDecimal similarity = calculateCosineSimilarity(currentFingerprint, existingFingerprint);
                
                if (similarity.compareTo(FormatUtil.bigDecimalParse(similarityConfig.getThreshold())) > 0) {
                    SimilarFileInfo similarFile = createSimilarFileInfo(existingFingerprint, similarity);
                    similarFiles.add(similarFile);
                    
                    // 更新最大相似度
                    if (similarity.compareTo(maxSimilarity) > 0) {
                        maxSimilarity = similarity;
                    }
                    
                    // 性能优化：如果相似度超过90%，停止继续比较
                    if (similarity.compareTo(new BigDecimal("0.90")) >= 0) {
                        log.debug("文件相似度超过90%，停止继续比较，ID: {}, 相似度: {}", kgKnowledgeBasesId, similarity);
                        break;
                    }
                }
            }
            
            // 3. 保存结果到Redis
            if (!similarFiles.isEmpty()) {
                saveToRedis(kgKnowledgeBasesId, similarFiles);
            }
            
            // 4. 更新数据库状态和结果
            updateCheckResultAndStatus(kgKnowledgeBasesId, maxSimilarity);
            
            // 5. 调用后续业务逻辑（上报、审核等）
            if (maxSimilarity.compareTo(BigDecimal.ZERO) > 0) {
                handleSimilarityFound(kgKnowledgeBasesId, maxSimilarity, similarFiles);
            }
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("文件相似度计算完成，ID: {}, 最大相似度: {}, 耗时: {}ms", 
                    kgKnowledgeBasesId, maxSimilarity, duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("文件相似度计算异常，ID: {}, 耗时: {}ms", kgKnowledgeBasesId, duration, e);
            updateCheckStatusToFailed(kgKnowledgeBasesId, "相似度计算异常: " + e.getMessage());
        }
    }
    
    /**
     * 更新查重结果和状态（成功）
     */
    private void updateCheckResultAndStatus(Long kgKnowledgeBasesId, BigDecimal checkRepeatResult) {
        try {
            // 更新查重结果
            KnowledgeBasesEntity entity = new KnowledgeBasesEntity();
            entity.setKgKnowledgeBasesId(kgKnowledgeBasesId);
            entity.setCheckRepeatResult(checkRepeatResult);
            knowledgeBasesDMapper.updateById(entity);
            
            // 更新状态为成功
            updateCheckStatusToSuccess(kgKnowledgeBasesId, checkRepeatResult);
            
        } catch (Exception e) {
            log.error("更新查重结果和状态异常，ID: {}", kgKnowledgeBasesId, e);
            updateCheckStatusToFailed(kgKnowledgeBasesId, "更新结果异常: " + e.getMessage());
        }
    }
    
    /**
     * 处理发现相似文件的后续逻辑
     */
    private void handleSimilarityFound(Long kgKnowledgeBasesId, BigDecimal maxSimilarity, List<SimilarFileInfo> similarFiles) {
        try {
            // 调用上报逻辑
            /*if (maxSimilarity.compareTo(similarityConfig.getReportThreshold()) >= 0) {
                // 这里可以调用上报相关的方法
                log.info("文件相似度达到上报阈值，ID: {}, 相似度: {}", kgKnowledgeBasesId, maxSimilarity);
            }
            
            // 调用自动审核逻辑
            if (maxSimilarity.compareTo(similarityConfig.getAutoAuditThreshold()) >= 0) {
                // 这里可以调用自动审核不通过的方法
                log.info("文件相似度达到自动审核阈值，ID: {}, 相似度: {}", kgKnowledgeBasesId, maxSimilarity);
            }
            */
        } catch (Exception e) {
            log.error("处理相似文件后续逻辑异常，ID: {}", kgKnowledgeBasesId, e);
        }
    }
    
    /**
     * 获取所有文档指纹（优化版本）
     */
    private List<DocumentFingerprint> getAllDocumentFingerprints() {
        try {
            // 使用现有的查询方法
            return documentFingerprintMapper.selectList(null);
        } catch (Exception e) {
            log.error("获取文档指纹异常", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 计算文档指纹（复用现有逻辑）
     */
    private DocumentFingerprint calculateDocumentFingerprint(String content) {
        // 使用现有的SimHash指纹计算方法
        String simHash = EnhancedSimHashUtil.generateSimHash(content);
        
        DocumentFingerprint fingerprint = new DocumentFingerprint();
        fingerprint.setFingerprint(simHash);
        fingerprint.setCreateTime(new Date());
        fingerprint.setUpdateTime(new Date());
        
        return fingerprint;
    }
    
    /**
     * 计算余弦相似度（复用现有逻辑）
     */
    private BigDecimal calculateCosineSimilarity(DocumentFingerprint current, DocumentFingerprint existing) {
        try {
            // 使用现有的相似度计算方法
            String currentHash = current.getFingerprint();
            String existingHash = existing.getFingerprint();
            
            if (StringUtils.isBlank(currentHash) || StringUtils.isBlank(existingHash)) {
                return BigDecimal.ZERO;
            }
            
            // 计算海明距离
            int hammingDistance = SimHashUtil.hammingDistance(currentHash, existingHash);
            
            // 转换为相似度（海明距离越小，相似度越高）
            double similarity = 1.0 - (double) hammingDistance / 64.0; // SimHash是64位
            similarity = Math.max(0.0, similarity); // 确保不为负数
            
            return BigDecimal.valueOf(similarity).setScale(4, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("计算相似度异常", e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 创建相似文件信息
     */
    private SimilarFileInfo createSimilarFileInfo(DocumentFingerprint fingerprint, BigDecimal similarity) {
        SimilarFileInfo similarFile = new SimilarFileInfo();
        similarFile.setKgKnowledgeBasesId(fingerprint.getKgKnowledgeBasesId());
        similarFile.setSimilarity(FormatUtil.doubleParse(similarity));
        similarFile.setOperativeWord(fingerprint.getOperativeWord());
        return similarFile;
    }
    
    /**
     * 保存相似文件列表到Redis
     */
    private void saveToRedis(Long kgKnowledgeBasesId, List<SimilarFileInfo> similarFiles) {
        try {
            // 获取原有的文件信息
            String prefix = KnowledgeConstant.FILE_REDIS + kgKnowledgeBasesId;
            String json = (String) ctgRedisService.get(prefix);
            
            if (StringUtils.isNotBlank(json)) {
                KgKnowledgeFile file = JSON.parseObject(json, KgKnowledgeFile.class);
                if (file != null) {
                    // 更新相似文件列表
                    file.setSimilarFiles(similarFiles);
                    
                    // 重新保存到Redis
                    String updatedJson = JSON.toJSONString(file);
                    ctgRedisService.set(prefix, 24 * 60 * 60, updatedJson); // 24小时过期
                    
                    log.debug("已更新Redis中的相似文件列表, kgKnowledgeBasesId={}, 相似文件数量={}", 
                            kgKnowledgeBasesId, similarFiles.size());
                }
            }
        } catch (Exception e) {
            log.error("保存相似文件列表到Redis异常，ID: {}", kgKnowledgeBasesId, e);
        }
    }
    
    /**
     * 获取当前查重结果（辅助方法）
     */
    private BigDecimal getCurrentCheckResult(Long kgKnowledgeBasesId) {
        try {
            KnowledgeBasesEntity entity = knowledgeBasesDMapper.selectById(kgKnowledgeBasesId);
            return entity != null ? entity.getCheckRepeatResult() : null;
        } catch (Exception e) {
            log.error("获取当前查重结果异常, kgKnowledgeBasesId={}", kgKnowledgeBasesId, e);
            return null;
        }
    }

    /**
     * 统一的文件相似度处理方法
     * 供队列、定时任务、手动触发等所有场景使用
     * 
     * @param kgKnowledgeBasesIds 知识库ID列表
     * @param source 调用来源（用于日志）
     * @return 处理结果
     */
    public CompletableFuture<Void> unifiedSimilarityCheck(List<Long> kgKnowledgeBasesIds, String source) {
        if (kgKnowledgeBasesIds == null || kgKnowledgeBasesIds.isEmpty()) {
            log.warn("[{}] 知识库ID列表为空，跳过处理", source);
            return CompletableFuture.completedFuture(null);
        }

        log.info("[{}] 开始统一查重处理, 数量={}", source, kgKnowledgeBasesIds.size());
        long overallStartTime = System.currentTimeMillis();

        return CompletableFuture.runAsync(() -> {
            try {
                // 1. 批量获取文件内容
                Map<Long, KgKnowledgeFile> fileContentMap = batchGetFileContents(kgKnowledgeBasesIds, source);
                
                if (fileContentMap.isEmpty()) {
                    log.warn("[{}] 没有获取到任何有效文件内容", source);
                    return;
                }

                log.info("[{}] 成功获取{}个文件内容，开始相似度计算", source, fileContentMap.size());

                // 2. 批量相似度计算
                batchCalculateSimilarity(fileContentMap, source);

                long duration = System.currentTimeMillis() - overallStartTime;
                log.info("[{}] 统一查重处理完成, 处理数量={}, 总耗时={}ms", source, fileContentMap.size(), duration);

                // 3. 调用后续业务处理（上报和审核）
                try {
                    List<Long> processedIds = new ArrayList<>(fileContentMap.keySet());
                    batchCheckAndAutoReportAfterSimilarityCheck(processedIds);
                    log.info("[{}] 后续业务处理完成", source);
                } catch (Exception e) {
                    log.error("[{}] 后续业务处理异常", source, e);
                }

            } catch (Exception e) {
                long duration = System.currentTimeMillis() - overallStartTime;
                log.error("[{}] 统一查重处理异常, 总耗时={}ms", source, duration, e);
            }
        }, similarityBatchThreadPool);
    }

    /**
     * 批量获取文件内容
     */
    private Map<Long, KgKnowledgeFile> batchGetFileContents(List<Long> kgKnowledgeBasesIds, String source) {
        Map<Long, KgKnowledgeFile> fileContentMap = new ConcurrentHashMap<>();
        
        // 控制并发获取文件内容
        int maxConcurrent = Math.min(10, kgKnowledgeBasesIds.size());
        Semaphore semaphore = new Semaphore(maxConcurrent);
        CountDownLatch latch = new CountDownLatch(kgKnowledgeBasesIds.size());
        
        log.debug("[{}] 开始批量获取文件内容, 并发数={}", source, maxConcurrent);
        
        for (Long id : kgKnowledgeBasesIds) {
            try {
                semaphore.acquire();
                similarityCompareThreadPool.execute(() -> {
                    try {
                        String prefix = KnowledgeConstant.FILE_REDIS + id;
                        String json = (String) ctgRedisService.get(prefix);
                        
                        if (StringUtils.isNotBlank(json)) {
                            KgKnowledgeFile file = JSON.parseObject(json, KgKnowledgeFile.class);
                            if (file != null && StringUtils.isNotBlank(file.getFileContent())) {
                                fileContentMap.put(id, file);
                                log.debug("[{}] 成功获取文件内容, ID={}", source, id);
                            } else {
                                log.warn("[{}] 文件内容为空, ID={}", source, id);
                                updateCheckStatusToFailed(id, "文件内容为空");
                            }
                        } else {
                            log.warn("[{}] Redis中无文件内容, ID={}", source, id);
                            updateCheckStatusToFailed(id, "文件内容不存在于Redis");
                        }
                    } catch (Exception e) {
                        log.error("[{}] 获取文件内容异常, ID={}", source, id, e);
                        updateCheckStatusToFailed(id, "获取文件内容异常: " + e.getMessage());
                    } finally {
                        semaphore.release();
                        latch.countDown();
                    }
                });
            } catch (InterruptedException e) {
                log.error("[{}] 获取信号量被中断", source, e);
                latch.countDown();
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        try {
            boolean finished = latch.await(60, TimeUnit.SECONDS);
            if (!finished) {
                log.warn("[{}] 批量获取文件内容超时", source);
            }
        } catch (InterruptedException e) {
            log.error("[{}] 等待文件内容获取被中断", source, e);
            Thread.currentThread().interrupt();
        }
        
        return fileContentMap;
    }

    /**
     * 批量相似度计算
     */
    private void batchCalculateSimilarity(Map<Long, KgKnowledgeFile> fileContentMap, String source) {
        if (fileContentMap.isEmpty()) {
            return;
        }
        
        log.info("[{}] 开始批量相似度计算, 文件数量={}", source, fileContentMap.size());
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 获取所有现有文档指纹
            List<DocumentFingerprint> allFingerprints = documentFingerprintMapper.selectList(null);
            
            if (allFingerprints.isEmpty()) {
                log.info("[{}] 没有现有文档指纹，所有文件查重率为0", source);
                // 批量更新为成功状态，查重率为0
                for (Long id : fileContentMap.keySet()) {
                    updateCheckResultAndStatus(id, BigDecimal.ZERO);
                }
                return;
            }
            
            log.info("[{}] 获取到{}个现有文档指纹，开始相似度计算", source, allFingerprints.size());
            
            // 2. 并发计算每个文件的相似度
            batchCalculateSimilarityOptimized(fileContentMap, allFingerprints, source);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("[{}] 批量相似度计算完成, 耗时={}ms", source, duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[{}] 批量相似度计算异常, 耗时={}ms", source, duration, e);
            
            // 异常情况下批量更新为失败状态
            for (Long id : fileContentMap.keySet()) {
                updateCheckStatusToFailed(id, "相似度计算异常: " + e.getMessage());
            }
        }
    }

    /**
     * 优化的批量相似度计算
     */
    private void batchCalculateSimilarityOptimized(Map<Long, KgKnowledgeFile> fileContentMap, 
                                                  List<DocumentFingerprint> allFingerprints, String source) {
        
        int maxConcurrent = Math.min(6, fileContentMap.size());
        Semaphore semaphore = new Semaphore(maxConcurrent);
        CountDownLatch latch = new CountDownLatch(fileContentMap.size());
        
        log.info("[{}] 开始优化批量相似度计算, 并发数={}, 文件数量={}", source, maxConcurrent, fileContentMap.size());
        
        for (Map.Entry<Long, KgKnowledgeFile> entry : fileContentMap.entrySet()) {
            Long kgKnowledgeBasesId = entry.getKey();
            KgKnowledgeFile currentFile = entry.getValue();
            
            try {
                semaphore.acquire();
                
                similarityCompareThreadPool.execute(() -> {
                    try {
                        calculateSingleFileSimilarity(kgKnowledgeBasesId, currentFile, allFingerprints, source);
                    } finally {
                        semaphore.release();
                        latch.countDown();
                    }
                });
                
            } catch (InterruptedException e) {
                log.error("[{}] 获取相似度计算信号量被中断", source, e);
                latch.countDown();
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        try {
            boolean finished = latch.await(300, TimeUnit.SECONDS); // 5分钟超时
            if (!finished) {
                log.warn("[{}] 批量相似度计算超时，部分任务可能仍在执行", source);
            }
        } catch (InterruptedException e) {
            log.error("[{}] 等待相似度计算完成被中断", source, e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 单个文件相似度计算（优化版本）
     * 保留90%停止等性能优化
     */
    private void calculateSingleFileSimilarity(Long kgKnowledgeBasesId, KgKnowledgeFile currentFile, 
                                              List<DocumentFingerprint> allFingerprints, String source) {
        log.debug("[{}] 开始计算文件相似度, ID={}", source, kgKnowledgeBasesId);
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 生成当前文件的SimHash指纹
            String currentContent = currentFile.getFileContent();
            String currentFileSuffix = getSafeFileSuffix(currentFile.getFileSuffix());
            String simHash = EnhancedSimHashUtil.generateSimHash(currentContent);
            
            // 2. 保存当前文件的指纹到数据库
            saveDocumentFingerprint(kgKnowledgeBasesId, currentFile, simHash, currentFileSuffix);
            
            // 3. 计算与现有文档的相似度
            BigDecimal maxSimilarity = BigDecimal.ZERO;
            List<SimilarFileInfo> similarFiles = new ArrayList<>();
            
            // 获取相似度阈值
            Double thresholdDouble = similarityConfig.getThreshold();
            BigDecimal threshold = BigDecimal.valueOf(thresholdDouble != null ? thresholdDouble : 0.7);
            
            // 获取90%停止阈值
            Double earlyStopDouble = similarityConfig.getEarlyStopThreshold();
            BigDecimal earlyStopThreshold = BigDecimal.valueOf(earlyStopDouble != null ? earlyStopDouble : 0.9);
            
            for (DocumentFingerprint existingFingerprint : allFingerprints) {
                // 跳过自己
                if (existingFingerprint.getKgKnowledgeBasesId().equals(kgKnowledgeBasesId)) {
                    continue;
                }
                
                // 文件类型匹配检查
                if (!isFileTypeMatched(currentFileSuffix, existingFingerprint.getFileType())) {
                    continue;
                }
                
                // 计算相似度
                BigDecimal similarity = calculateSimilarityScore(simHash, existingFingerprint.getFingerprint(), 
                                                               currentContent, existingFingerprint.getKgKnowledgeBasesId());
                
                if (similarity.compareTo(threshold) > 0) {
                    SimilarFileInfo similarFile = createSimilarFileInfo(existingFingerprint, similarity);
                    similarFiles.add(similarFile);
                    
                    // 更新最大相似度
                    if (similarity.compareTo(maxSimilarity) > 0) {
                        maxSimilarity = similarity;
                    }
                    
                    // 性能优化：如果相似度超过90%，停止继续比较
                    if (Boolean.TRUE.equals(similarityConfig.getEnableEarlyStop()) && 
                        similarity.compareTo(earlyStopThreshold) >= 0) {
                        log.debug("[{}] 文件相似度超过{}%，停止继续比较, ID={}, 相似度={}", 
                                source, earlyStopThreshold.multiply(BigDecimal.valueOf(100)), kgKnowledgeBasesId, similarity);
                        break;
                    }
                }
            }
            
            // 4. 保存结果到Redis
            if (!similarFiles.isEmpty()) {
                // 按相似度降序排序，只保留前N个
                similarFiles.sort((a, b) -> b.getSimilarity().compareTo(a.getSimilarity()));
                int maxCount = Math.min(KnowledgeConstant.MAX_SIMILAR_FILES, similarFiles.size());
                similarFiles = similarFiles.subList(0, maxCount);
                
                saveToRedis(kgKnowledgeBasesId, similarFiles, currentFile);
            } else {
                // 清空相似文件列表
                saveToRedis(kgKnowledgeBasesId, new ArrayList<>(), currentFile);
            }
            
            // 5. 更新数据库状态和结果
            updateCheckResultAndStatus(kgKnowledgeBasesId, maxSimilarity);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 文件相似度计算完成, ID={}, 最大相似度={}, 相似文件数={}, 耗时={}ms", 
                    source, kgKnowledgeBasesId, maxSimilarity, similarFiles.size(), duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[{}] 文件相似度计算异常, ID={}, 耗时={}ms", source, kgKnowledgeBasesId, duration, e);
            updateCheckStatusToFailed(kgKnowledgeBasesId, "相似度计算异常: " + e.getMessage());
        }
    }

    /**
     * 保存文档指纹
     */
    private void saveDocumentFingerprint(Long kgKnowledgeBasesId, KgKnowledgeFile currentFile, 
                                       String simHash, String currentFileSuffix) {
        try {
            DocumentFingerprint fingerprint = DocumentFingerprint.builder()
                    .kgKnowledgeBasesId(kgKnowledgeBasesId)
                    .operativeWord(currentFile.getOperativeWord())
                    .fingerprint(simHash)
                    .fileType(currentFileSuffix)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();
            
            // 先查询是否已存在
            DocumentFingerprint existFingerprint = documentFingerprintMapper.selectByKnowledgeBasesId(kgKnowledgeBasesId);
            if (existFingerprint != null) {
                fingerprint.setKgDocumentFingerprintId(existFingerprint.getKgDocumentFingerprintId());
                documentFingerprintMapper.updateById(fingerprint);
            } else {
                documentFingerprintMapper.insert(fingerprint);
            }
        } catch (Exception e) {
            log.error("保存文档指纹异常, ID={}", kgKnowledgeBasesId, e);
        }
    }

    /**
     * 计算相似度分数
     */
    private BigDecimal calculateSimilarityScore(String currentHash, String existingHash, 
                                               String currentContent, Long existingId) {
        try {
            // 获取相似度计算方法
            SimilarityMethod method = getSimilarityMethod();
            double similarity;
            
            if (method == SimilarityMethod.SIMHASH) {
                // 使用SimHash方法
                similarity = EnhancedSimHashUtil.calculateSimHashSimilarity(currentHash, existingHash);
            } else if (method == SimilarityMethod.COSINE) {
                // 使用余弦相似度方法
                String existingContent = getFileContentFromRedis(existingId);
                if (StringUtils.isBlank(existingContent)) {
                    return BigDecimal.ZERO;
                }
                similarity = TextPlagiarismCheckUtil.calculateCosSimilarity(currentContent, existingContent);
            } else {
                // 使用结合方法
                String existingContent = getFileContentFromRedis(existingId);
                if (StringUtils.isBlank(existingContent)) {
                    return BigDecimal.ZERO;
                }
                similarity = EnhancedSimHashUtil.calculateCombinedSimilarity(currentContent, existingContent);
            }
            
            return BigDecimal.valueOf(similarity).setScale(4, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("计算相似度异常, existingId={}", existingId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 从Redis获取文件内容
     */
    private String getFileContentFromRedis(Long kgKnowledgeBasesId) {
        try {
            String json = (String) ctgRedisService.get(KnowledgeConstant.FILE_REDIS + kgKnowledgeBasesId);
            if (StringUtils.isNotBlank(json)) {
                KgKnowledgeFile file = JSON.parseObject(json, KgKnowledgeFile.class);
                if (file != null) {
                    return file.getFileContent();
                }
            }
        } catch (Exception e) {
            log.error("从Redis获取文件内容异常, ID={}", kgKnowledgeBasesId, e);
        }
        return null;
    }


    /**
     * 文件类型匹配检查
     */
    /*private boolean isFileTypeMatched(String currentType, String otherType) {
        if (StringUtils.isBlank(currentType) || StringUtils.isBlank(otherType)) {
            return true; // 如果类型为空，允许比较
        }
        return currentType.equalsIgnoreCase(otherType);
    }*/

    /**
     * 获取安全的文件后缀
     */
    /*private String getSafeFileSuffix(String fileSuffix) {
        return StringUtils.isBlank(fileSuffix) ? "unknown" : fileSuffix.toLowerCase();
    }*/

    /**
     * 更新查重结果和状态（成功）
     */
    /*private void updateCheckResultAndStatus(Long kgKnowledgeBasesId, BigDecimal checkRepeatResult) {
        try {
            // 更新查重结果
            KnowledgeBasesEntity entity = new KnowledgeBasesEntity();
            entity.setKgKnowledgeBasesId(kgKnowledgeBasesId);
            entity.setCheckRepeatResult(checkRepeatResult);
            knowledgeBasesDMapper.updateById(entity);
            
            // 更新状态为成功
            updateCheckStatusToSuccess(kgKnowledgeBasesId, checkRepeatResult);
            
        } catch (Exception e) {
            log.error("更新查重结果和状态异常, ID={}", kgKnowledgeBasesId, e);
            updateCheckStatusToFailed(kgKnowledgeBasesId, "更新结果异常: " + e.getMessage());
        }
    }

    *//**
     * 创建相似文件信息
     *//*
    private SimilarFileInfo createSimilarFileInfo(DocumentFingerprint fingerprint, BigDecimal similarity) {
        SimilarFileInfo similarFile = new SimilarFileInfo();
        similarFile.setKgKnowledgeBasesId(fingerprint.getKgKnowledgeBasesId());
        similarFile.setSimilarity(similarity.doubleValue()); // 转换为double类型
        similarFile.setOperativeWord(fingerprint.getOperativeWord());
        return similarFile;
    }*/

    /**
     * 保存相似文件列表到Redis
     */
    private void saveToRedis(Long kgKnowledgeBasesId, List<SimilarFileInfo> similarFiles, KgKnowledgeFile currentFile) {
        try {
            String prefix = KnowledgeConstant.FILE_REDIS + kgKnowledgeBasesId;
            
            // 更新相似文件列表
            currentFile.setSimilarFiles(similarFiles);
            
            // 保存到Redis
            String updatedJson = JSON.toJSONString(currentFile);
            ctgRedisService.set(prefix, 24 * 60 * 60, updatedJson); // 24小时过期
            
            log.debug("已更新Redis中的相似文件列表, ID={}, 相似文件数量={}", 
                    kgKnowledgeBasesId, similarFiles.size());
        } catch (Exception e) {
            log.error("保存相似文件列表到Redis异常, ID={}", kgKnowledgeBasesId, e);
        }
    }

    /*@Override
    public CompletableFuture<Void> batchCheckFileSimilarityAsync(List<Long> kgKnowledgeBasesIds) {
        log.info("调用批量查重方法，转发到统一处理方法, 数量={}", kgKnowledgeBasesIds == null ? 0 : kgKnowledgeBasesIds.size());
        return unifiedSimilarityCheck(kgKnowledgeBasesIds, "批量查重");
    }*/

    /*@Override
    public boolean batchSetCheckingStatus(List<Long> kgKnowledgeBasesIds) {
        if (kgKnowledgeBasesIds == null || kgKnowledgeBasesIds.isEmpty()) {
            log.warn("批量设置查重状态的ID列表为空");
            return false;
        }
        
        log.info("批量设置文件查重状态为查重中(-1), 数量={}", kgKnowledgeBasesIds.size());
        try {
            // 优先使用新的状态管理方法
            return updateCheckStatusToChecking(kgKnowledgeBasesIds);
        } catch (Exception e) {
            log.error("批量设置文件查重状态异常, 数量={}, 尝试使用旧方法", kgKnowledgeBasesIds.size(), e);
            
            // 如果新方法失败，回退到旧方法（兼容性处理）
            try {
                List<KnowledgeBasesEntity> entities = new ArrayList<>(kgKnowledgeBasesIds.size());
                for (Long id : kgKnowledgeBasesIds) {
                    KnowledgeBasesEntity entity = new KnowledgeBasesEntity();
                    entity.setKgKnowledgeBasesId(id);
                    entity.setCheckRepeatResult(new BigDecimal(-1));
                    entities.add(entity);
                }
                
                knowledgeBasesDMapper.batchUpdateCheckResult(entities);
                log.info("使用旧方法成功批量设置文件查重状态为查重中, 数量={}", kgKnowledgeBasesIds.size());
                return true;
            } catch (Exception e2) {
                log.error("使用旧方法批量设置文件查重状态也失败, 数量={}", kgKnowledgeBasesIds.size(), e2);
                return false;
            }
        }
    }*/
} 