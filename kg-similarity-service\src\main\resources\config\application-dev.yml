spring:
  datasource:
    url: ******************************************************************************=${spring.application.name}
    username: pg5gc_api_dev
    password: <PERSON><PERSON>(o9eG14ZSVvS4tKzRIDIVzJ36xXGFIcGm)
    driver-class-name: org.postgresql.Driver
  elasticsearch:
    rest:
      uris: 192.168.35.61:9201,192.168.35.62:9201,192.168.35.63:9201
      username: elastic
      password: <PERSON><PERSON>(nVmyxeMcK36G5CO9KCe8TDgsno15Vw5i)

ctg-cache:
  maxTotal: 500
  maxIdle: 100
  minIdle: 5
  maxWaitMillis: 3000
  period: 3000
  monitorTimeout: 200
  enabled: false
  expireTime: 36000
  ins-config:
    - name: PORTAL-CENTER
      password: ENC(j+f/lPPIhjmx6xDKqDIjrJJtYAPa1dKq)
      cluster-nodes: 192.168.35.61:7001,192.168.35.61:7002,192.168.35.62:7001,192.168.35.62:7002,192.168.35.63:7001,192.168.35.63:7002,192.168.35.64:7001,192.168.35.64:7002,192.168.35.65:7001,192.168.35.65:7002
  db-config:
    - name: PORTAL_CATCH_LOGIN
      ins-name: PORTAL-CENTER
      database: 1

logging:
  level:
    root: info


#查重处理 核心配置
similarity:
  # 相似度阈值
  threshold: 0.7
  # 提前终止阈值
  early-stop-threshold: 0.9
  # 是否启用提前终止
  enable-early-stop: true
  # 是否启用文件查重
  enabled: true
  # 相似度计算方法：SIMHASH, COSINE, COMBINED
  method: COSINE
  # 批处理大小
  batch-size: 20
  # 比较线程池并行度
  compare-parallelism: 8
  
  # 队列缓冲配置
  queue:
    # 是否启用队列模式
    enabled: true
    # 队列触发阈值（默认10个）
    trigger-size: 10
    # 队列超时时间（分钟，默认3分钟）
    timeout-minutes: 3
    # 队列检查间隔（秒，默认30秒）
    check-interval-cron: "*/30 * * * * ?"
    
  # 定时任务配置
  schedule:
    # 是否启用定时任务
    enabled: true
    # 定时任务cron表达式（默认每15分钟执行一次）
    cron: "0 0/15 * * * ?"
    # 查询多少小时前的数据（默认3小时）
    hours-ago: 3
    # 每次处理的批次大小（默认15条）
    batch-size: 15
    # 最大重试次数（默认3次）
    max-retry-count: 1
    
  # 线程池配置
  thread:
    # 批量处理线程池配置
    batch-core-size: 2
    batch-max-size: 3
    batch-queue-capacity: 10
    # 比较处理线程池配置
    compare-core-size: 2
    compare-max-size: 4
    compare-queue-capacity: 15
    # 线程存活时间(秒)
    keep-alive-seconds: 60
