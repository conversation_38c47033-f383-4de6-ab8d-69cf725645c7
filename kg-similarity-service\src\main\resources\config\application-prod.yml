spring:
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:kg}?currentSchema=kg&ApplicationName=${spring.application.name}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
  elasticsearch:
    rest:
      uris: ${ES_URIS}
      username: ${ES_USERNAME}
      password: ${ES_PASSWORD}

ctg-cache:
  maxTotal: 1000
  maxIdle: 200
  minIdle: 10
  maxWaitMillis: 3000
  period: 3000
  monitorTimeout: 200
  enabled: true
  expireTime: 36000
  ins-config:
    - name: PORTAL-CENTER
      password: ${REDIS_PASSWORD}
      cluster-nodes: ${REDIS_CLUSTER_NODES}
  db-config:
    - name: PORTAL_CATCH_LOGIN
      ins-name: PORTAL-CENTER
      database: 1

logging:
  level:
    root: warn
    com.ffcs.oss.kg: info

#查重处理 核心配置
similarity:
  # 相似度阈值
  threshold: 0.8
  # 提前终止阈值
  early-stop-threshold: 0.95
  # 是否启用提前终止
  enable-early-stop: true
  # 是否启用文件查重
  enabled: true
  # 相似度计算方法：SIMHASH, COSINE, COMBINED
  method: COSINE
  # 批处理大小
  batch-size: 50
  # 比较线程池并行度
  compare-parallelism: 16
  
  # 队列缓冲配置
  queue:
    # 生产环境启用队列模式
    enabled: true
    # 队列触发阈值（生产环境增大阈值减少触发频率）
    trigger-size: 20
    # 队列超时时间（分钟，生产环境允许更长等待时间）
    timeout-minutes: 5
    # 队列检查间隔（秒，生产环境降低检查频率）
    check-interval-cron: "*/30 * * * * ?"
    
  # 定时任务配置
  schedule:
    # 生产环境默认禁用定时任务，需要手动启用
    enabled: false
    # 定时任务cron表达式（生产环境每小时执行一次）
    cron: "0 0 * * * ?"
    # 查询多少小时前的数据（生产环境6小时，更保守）
    hours-ago: 6
    # 每次处理的批次大小（生产环境小批次，确保稳定性）
    batch-size: 10
    # 最大重试次数（生产环境更保守的重试策略）
    max-retry-count: 2
    
  # 线程池配置
  thread:
    # 批量处理线程池配置
    batch-core-size: 8
    batch-max-size: 16
    batch-queue-capacity: 100
    # 比较处理线程池配置
    compare-core-size: 32
    compare-max-size: 64
    compare-queue-capacity: 500
    # 线程存活时间(秒)
    keep-alive-seconds: 300 