# 文件相似度定时任务功能说明

## 功能概述

为了解决大批量文件查重时可能出现的服务崩溃、内存溢出等问题，新增了定时任务功能，专门处理长时间卡在"查重中"状态的数据。

## 主要特性

### 1. 智能查重恢复
- 定时查询指定时间前仍在查重中的数据（`check_repeat_result = -1`）
- 自动重新提交查重任务
- 避免数据长期停留在查重中状态

### 2. 性能优化设计
- **分批处理**：每次只处理少量数据（默认15条），避免系统负载过高
- **并发控制**：使用信号量限制同时处理的文件数量，防止内存溢出
- **超时保护**：设置任务超时时间，避免长时间阻塞
- **批次间休息**：批次之间有间隔，降低系统压力
- **轻量级处理**：专门的轻量级查重方法，优化内存使用

### 3. 灵活配置
- 支持开启/关闭定时任务
- 可配置查询时间范围
- 可调整每次处理的数据量
- 支持手动触发执行

## 配置说明

### 基本配置

```yaml
similarity:
  schedule:
    # 是否启用定时任务
    enabled: true
    # 查询多少小时前的数据（默认3小时）
    hours-ago: 3
    # 每次处理的批次大小（默认15条）
    batch-size: 15
```

### 环境配置建议

#### 开发环境
```yaml
similarity:
  schedule:
    enabled: true
    hours-ago: 1      # 开发环境1小时后重试
    batch-size: 5     # 小批次，便于测试
```

#### 测试环境
```yaml
similarity:
  schedule:
    enabled: true
    hours-ago: 2      # 测试环境2小时后重试
    batch-size: 10
```

#### 生产环境
```yaml
similarity:
  schedule:
    enabled: false    # 生产环境默认关闭，需要手动开启
    hours-ago: 6      # 生产环境6小时后重试，更保守
    batch-size: 10    # 小批次，确保稳定性
```

## 使用方法

### 1. 启用定时任务

在配置文件中设置：
```yaml
similarity:
  schedule:
    enabled: true
```

### 2. 定时执行
定时任务会在每小时的0分和30分自动执行（每半小时一次）。

### 3. 手动触发

可以通过API接口手动触发：

```http
POST /api/similarity/process-stuck-checking
Content-Type: application/x-www-form-urlencoded

hoursAgo=3&batchSize=15
```

参数说明：
- `hoursAgo`：查询多少小时前的数据（可选，默认3）
- `batchSize`：每批处理数量（可选，默认15）

### 4. 监控和日志

查看应用日志，关键日志信息：
```
=== 开始执行文件相似度定时任务 ===
找到5条需要重新处理的查重数据: [1001, 1002, 1003, 1004, 1005]
将任务分为1个批次处理
第1批次处理成功
查重中断数据处理完成，总数: 5, 成功: 5, 失败: 0, 批次数: 1
=== 文件相似度定时任务执行完成 ===
```

## 性能优化策略

### 1. 内存控制
- 使用信号量限制并发数量
- 分批处理，避免一次性加载大量数据
- 及时释放资源

### 2. 系统负载控制
- 批次间休息，避免连续高负载
- 超时控制，防止任务无限等待
- 专用线程池，隔离定时任务和正常业务

### 3. 数据库优化
- 使用索引查询，提高查询效率
- 分页查询，避免一次性返回大量数据
- 批量更新，减少数据库连接次数

## SQL查询说明

定时任务执行的SQL查询：
```sql
SELECT kg_knowledge_bases_id
FROM kg_knowledge_bases_d
WHERE is_deleted = '0'
AND check_repeat_result = -1
AND created_time <= #{beforeTime}::timestamp
ORDER BY created_time ASC
LIMIT #{limit}
```

这个查询会找到：
- 未删除的数据（`is_deleted = '0'`）
- 仍在查重中的数据（`check_repeat_result = -1`）
- 指定时间前创建的数据（避免处理刚提交的查重任务）

## 故障排查

### 1. 定时任务不执行
检查项：
- 确认 `similarity.schedule.enabled=true`
- 确认应用启动类有 `@EnableScheduling` 注解
- 查看应用日志是否有异常

### 2. 处理效果不佳
优化建议：
- 调小 `batch-size`，减少每次处理数量
- 增大 `hours-ago`，避免频繁重试
- 检查线程池配置是否合理

### 3. 系统负载过高
调优方法：
- 降低并发数量
- 增加批次间休息时间
- 调整线程池参数

## 监控指标

建议监控以下指标：
- 定时任务执行频率和耗时
- 查重中状态数据的数量趋势
- 系统内存和CPU使用率
- 数据库连接池状态

## 注意事项

1. **生产环境使用**：建议先在测试环境验证，生产环境从小批次开始
2. **资源监控**：密切关注内存使用情况，及时调整参数
3. **数据备份**：重要环境建议先备份数据
4. **日志保留**：保留足够的日志便于问题分析
5. **参数调优**：根据实际数据量和服务器性能调整参数

## 相关API接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/similarity/process-stuck-checking` | POST | 手动处理查重中断数据 |
| `/api/similarity/batchCheck` | POST | 原有的批量查重接口 |
| `/api/similarity/clear` | POST | 清除查重缓存 |
| `/api/similarity/list` | GET | 获取相似文件列表 |

## 更新日志

- **v1.0.0**：初始版本，支持定时处理查重中断数据
- 新增轻量级查重处理方法
- 新增手动触发接口
- 新增详细的性能优化和监控 