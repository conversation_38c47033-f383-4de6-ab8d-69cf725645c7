# 队列优化功能快速部署指南

## 🚀 5分钟快速部署

### 1. 启用队列功能
在 `application-dev.yml` 中确认配置：
```yaml
similarity:
  queue:
    enabled: true          # 启用队列模式
    trigger-size: 10       # 10个文件触发
    timeout-minutes: 3     # 3分钟超时触发
```

### 2. 重启服务
```bash
# 重启kg-similarity-service服务
# 确保@EnableScheduling注解已添加到启动类
```

### 3. 验证功能
```bash
# 1. 查看队列状态
curl -X GET "http://localhost:8022/api/similarity/queue/status"

# 2. 测试批量上传（通过kg-system-web或直接调用）
curl -X POST "http://localhost:8022/api/similarity/batchCheck" \
     -H "Content-Type: application/json" \
     -d "[1001, 1002, 1003]"

# 3. 观察日志
tail -f logs/application.log | grep "队列"
```

## 🔧 关键配置说明

### 基础配置
```yaml
similarity:
  queue:
    enabled: true              # 必须启用
    trigger-size: 10          # 数量触发阈值
    timeout-minutes: 3        # 时间触发阈值
    check-interval-seconds: 30 # 检查间隔
```

### 性能调优（根据服务器配置调整）
```yaml
similarity:
  thread:
    compare-core-size: 16     # 根据CPU核数
    compare-max-size: 32      # 最大并发数
  queue:
    trigger-size: 20          # 生产环境建议更大
```

## 📊 监控要点

### 1. 关键日志
```
✅ 正常：已添加3个待查重ID到队列，当前队列大小: 8
✅ 触发：队列大小达到触发阈值(10)，开始处理  
✅ 成功：批量查重任务提交成功，数量: 10
❌ 异常：队列处理异常 (需要关注)
```

### 2. API监控
```bash
# 定期检查队列状态
curl -s "http://localhost:8022/api/similarity/queue/status" | jq .
```

### 3. 性能指标
- 队列当前大小：< 50 (正常)
- 处理成功率：> 95%
- 内存使用：相比优化前应明显降低

## 🚨 常见问题

### Q1: 队列不工作
```bash
# 检查配置
grep -A 5 "similarity.queue" application*.yml

# 检查服务是否启动定时任务
curl -X GET "http://localhost:8022/api/similarity/queue/status"
```

### Q2: 处理速度慢
```yaml
# 调整配置
similarity:
  queue:
    trigger-size: 5          # 减小触发阈值
    timeout-minutes: 1       # 缩短超时时间
```

### Q3: 内存还是高
```yaml
# 更保守的配置
similarity:
  thread:
    compare-core-size: 8     # 减少并发
    compare-max-size: 16
  queue:
    trigger-size: 15         # 增大批次但减少频率
```

## ✅ 验证清单

- [ ] `similarity.queue.enabled=true` 已配置
- [ ] 服务正常启动，无启动异常
- [ ] `POST /api/similarity/batchCheck` 返回"已加入队列"
- [ ] `GET /api/similarity/queue/status` 正常返回状态
- [ ] 日志中能看到队列处理信息
- [ ] 多次调用batchCheck不会导致服务崩溃
- [ ] 查重功能正常，后续业务流程正常

## 📞 应急处理

### 紧急禁用队列
```yaml
similarity:
  queue:
    enabled: false    # 禁用队列，恢复原有模式
```

### 清空队列
```bash
curl -X POST "http://localhost:8022/api/similarity/queue/clear"
```

### 手动触发处理
```bash
curl -X POST "http://localhost:8022/api/similarity/queue/trigger"
```

---

🎉 **部署完成！现在可以放心处理大批量文件查重了！**

有问题请查看详细文档：
- `队列缓冲机制使用说明.md` - 详细使用指南
- `队列优化实现总结.md` - 技术实现细节 