# 文件相似度定时任务功能开发总结

## 需求背景

项目中存在大量文件（约10000+）需要进行查重处理，现有的批量查重方法在处理大批量数据时容易出现：
- 服务直接卡死
- 内存溢出
- 查重任务中断后状态无法恢复
- 使用余弦算法导致的性能问题

## 解决方案

开发了一个智能的定时任务系统，专门处理查重中断的数据，具备以下特性：

### 1. 核心功能
- **定时恢复**：每半小时自动查询并处理卡在查重中状态的数据
- **性能优化**：采用分批处理、并发控制、超时保护等策略
- **手动触发**：提供API接口支持手动执行
- **灵活配置**：支持开关控制和参数调整

### 2. 性能优化策略
- **小批次处理**：每次最多处理15-20条数据
- **并发限制**：使用信号量控制同时处理的文件数量
- **批次间休息**：避免连续高负载
- **超时保护**：防止任务无限等待
- **轻量级算法**：专门的轻量级查重方法

## 代码变更详情

### 1. 数据访问层 (kg-data)

#### 1.1 Mapper接口扩展
**文件**：`ModelKnowledgeBasesDMapper.java`
```java
// 新增方法
List<Long> selectStuckCheckingIds(LocalDateTime beforeTime, Integer limit);
List<KnowledgeBasesEntity> selectStuckCheckingEntities(LocalDateTime beforeTime, Integer limit);
```

#### 1.2 SQL查询实现
**文件**：`ModelKnowledgeBasesDMapper.xml`
```xml
<!-- 查询卡住的查重数据 -->
<select id="selectStuckCheckingIds" resultType="java.lang.Long">
    SELECT kg_knowledge_bases_id
    FROM kg_knowledge_bases_d
    WHERE is_deleted = '0'
    AND check_repeat_result = -1
    AND created_time <= #{beforeTime}::timestamp
    ORDER BY created_time ASC
    LIMIT #{limit}
</select>
```

### 2. 服务层 (kg-similarity-service)

#### 2.1 Service接口扩展
**文件**：`FileSimilarityService.java`
```java
// 新增方法
ServiceResp<Object> processStuckCheckingData(Integer hoursAgo, Integer batchSize);
CompletableFuture<Void> lightweightBatchCheckSimilarityAsync(List<Long> kgKnowledgeBasesIds);
```

#### 2.2 Service实现
**文件**：`FileSimilarityServiceImpl.java`
- 实现了定时任务的核心处理逻辑
- 新增轻量级查重方法，优化内存使用
- 添加完善的异常处理和日志记录

#### 2.3 定时任务类
**文件**：`FileSimilarityScheduleTask.java`
```java
@Scheduled(cron = "0 0,30 * * * ?")  // 每半小时执行
public void processStuckCheckingDataTask()
```

#### 2.4 Controller接口
**文件**：`FileSimilarityController.java`
```java
@PostMapping("/process-stuck-checking")
public ServiceResp<Object> processStuckCheckingData(Integer hoursAgo, Integer batchSize)
```

### 3. 配置文件

#### 3.1 开发环境配置
**文件**：`application-dev.yml`
```yaml
similarity:
  schedule:
    enabled: true
    hours-ago: 3
    batch-size: 15
```

#### 3.2 生产环境配置
**文件**：`application-prod.yml`
```yaml
similarity:
  schedule:
    enabled: false  # 生产环境默认关闭
    hours-ago: 6
    batch-size: 10
```

### 4. 应用配置
**文件**：`KgSimilarityServiceApplication.java`
- 新增 `@EnableScheduling` 注解启用定时任务功能

### 5. 测试代码
**文件**：`FileSimilarityScheduleTaskTest.java`
- 完整的单元测试覆盖
- 测试正常执行、禁用状态、异常处理等场景

## 技术要点

### 1. 性能优化技术
```java
// 信号量控制并发
Semaphore semaphore = new Semaphore(Math.min(4, batch.size()));

// 超时控制
future.get(30, TimeUnit.SECONDS);

// 批次间休息
Thread.sleep(2000);
```

### 2. 异常处理
- 全方位的try-catch覆盖
- 详细的日志记录
- 优雅的降级处理

### 3. 配置化设计
- 支持开关控制
- 灵活的参数配置
- 环境差异化配置

## 部署和使用

### 1. 配置启用
在对应环境的配置文件中设置：
```yaml
similarity:
  schedule:
    enabled: true
```

### 2. 监控方式
通过应用日志监控执行情况：
```
=== 开始执行文件相似度定时任务 ===
找到5条需要重新处理的查重数据
查重中断数据处理完成，总数: 5, 成功: 5, 失败: 0
```

### 3. 手动触发
```bash
curl -X POST "http://localhost:8022/api/similarity/process-stuck-checking" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "hoursAgo=3&batchSize=15"
```

## 文件清单

### 新增文件
- `FileSimilarityScheduleTask.java` - 定时任务类
- `FileSimilarityScheduleTaskTest.java` - 测试类
- `application-prod.yml` - 生产环境配置
- `定时任务说明.md` - 功能说明文档
- `新功能开发总结.md` - 本文档

### 修改文件
- `ModelKnowledgeBasesDMapper.java` - 新增查询方法
- `ModelKnowledgeBasesDMapper.xml` - 新增SQL查询
- `FileSimilarityService.java` - 新增接口方法
- `FileSimilarityServiceImpl.java` - 实现新功能
- `FileSimilarityController.java` - 新增API接口
- `KgSimilarityServiceApplication.java` - 启用定时任务
- `application-dev.yml` - 添加定时任务配置

## 后续优化建议

1. **监控指标**：接入监控系统，跟踪定时任务执行情况
2. **参数自适应**：根据系统负载自动调整批次大小
3. **告警机制**：异常情况及时通知
4. **性能分析**：定期分析执行效果，持续优化
5. **扩展功能**：支持按优先级处理、支持暂停/恢复等

## 风险控制

1. **资源限制**：严格控制并发数和批次大小
2. **超时保护**：所有异步操作都设置超时
3. **异常处理**：完善的异常捕获和恢复机制
4. **配置控制**：支持随时开关定时任务
5. **日志监控**：详细的执行日志便于问题排查

## 总结

本次开发成功解决了大批量文件查重的性能问题，通过定时任务自动恢复中断的查重操作，显著提升了系统的稳定性和可用性。代码设计充分考虑了性能优化、异常处理和可配置性，为后续的功能扩展奠定了良好基础。 