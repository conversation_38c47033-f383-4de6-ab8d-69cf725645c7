# 文件相似度队列缓冲机制使用说明

## 功能概述

为了解决多用户同时上传文件导致的批量查重性能问题，新增了队列缓冲机制。该机制能够：

- **避免并发冲突**：多个请求不再直接触发查重，而是加入队列缓冲
- **智能批量处理**：达到阈值或超时后自动触发批量查重
- **资源控制**：严格控制并发数量，防止内存溢出和服务崩溃
- **业务逻辑保持**：完全保留原有的查重业务功能，只是优化了触发方式

## 工作原理

### 1. 请求接收阶段
```
用户上传文件 → kg-system-web → Feign调用 → batchCheck接口 → 加入队列
```

### 2. 队列管理阶段
```
队列缓冲 → 条件检查 → 触发处理 → 批量查重 → 状态更新
```

### 3. 触发条件
- **数量触发**：队列中文件数量达到配置阈值（默认10个）
- **时间触发**：距离上次新增文件超过配置时间（默认3分钟）
- **手动触发**：通过API接口强制触发

## 配置说明

### 基本配置
```yaml
similarity:
  queue:
    enabled: true              # 是否启用队列模式
    trigger-size: 10          # 队列触发阈值
    timeout-minutes: 3        # 队列超时时间（分钟）
    check-interval-seconds: 30 # 队列检查间隔（秒）
```

### 环境配置建议

#### 开发环境
```yaml
similarity:
  queue:
    enabled: true
    trigger-size: 5           # 小阈值，便于测试
    timeout-minutes: 1        # 短超时，快速响应
    check-interval-seconds: 15 # 频繁检查
```

#### 测试环境
```yaml
similarity:
  queue:
    enabled: true
    trigger-size: 8
    timeout-minutes: 2
    check-interval-seconds: 20
```

#### 生产环境
```yaml
similarity:
  queue:
    enabled: true
    trigger-size: 20          # 大阈值，减少触发频率
    timeout-minutes: 5        # 长超时，允许更多积累
    check-interval-seconds: 60 # 降低检查频率
```

## API接口说明

### 1. 原有接口（已优化）

#### 批量查重接口
```http
POST /api/similarity/batchCheck
Content-Type: application/json

[1001, 1002, 1003, 1004, 1005]
```

**响应示例**：
```json
{
  "success": true,
  "message": "批量文件相似度检查请求已加入队列，系统将自动处理",
  "data": null
}
```

### 2. 新增队列管理接口

#### 查看队列状态
```http
GET /api/similarity/queue/status
```

**响应示例**：
```json
{
  "success": true,
  "message": "获取队列状态成功",
  "data": {
    "enabled": true,
    "currentQueueSize": 15,
    "triggerSize": 10,
    "timeoutMinutes": 3,
    "isProcessing": false,
    "lastUpdateTime": "2024-12-30T14:30:15",
    "totalReceived": 150,
    "totalProcessed": 135,
    "batchCount": 12,
    "minutesSinceLastUpdate": 2
  }
}
```

#### 手动触发队列处理
```http
POST /api/similarity/queue/trigger
```

**响应示例**：
```json
{
  "success": true,
  "message": "手动触发队列处理成功，处理数量: 8",
  "data": 8
}
```

#### 清空队列（紧急情况）
```http
POST /api/similarity/queue/clear
```

**响应示例**：
```json
{
  "success": true,
  "message": "队列清空成功，清空数量: 12",
  "data": 12
}
```

## 使用场景

### 场景1：正常业务流程
1. 用户通过kg-system-web上传文件
2. 系统调用batchCheck接口，文件ID加入队列
3. 当队列达到10个文件时，自动触发批量查重
4. 查重完成后，状态正常更新，后续流程正常进行

### 场景2：低峰期处理
1. 用户零星上传了5个文件
2. 由于未达到数量阈值，文件在队列中等待
3. 3分钟后触发超时机制，自动处理这5个文件

### 场景3：高峰期处理
1. 多个用户同时上传，短时间内积累了50个文件
2. 每达到10个就触发一次处理，分5批处理
3. 每批之间有间隔，避免系统负载过高

### 场景4：紧急处理
1. 管理员发现队列中有待处理文件
2. 调用手动触发接口立即处理
3. 或在紧急情况下清空队列重新开始

## 监控和运维

### 1. 日志监控
查看应用日志，关注以下关键信息：

```
# 队列状态
已添加5个待查重ID到队列，当前队列大小: 15

# 触发处理
队列大小达到触发阈值(10)，开始处理
队列超时(3 分钟)，当前队列大小: 8，开始处理

# 处理过程
开始批量处理查重队列，触发原因: 队列大小达到阈值
本批次处理数量: 10，累计接收: 150，累计处理批次: 12
开始执行批量查重，数量: 10，原因: 队列大小达到阈值
批量查重任务提交成功，数量: 10，原因: 队列大小达到阈值
```

### 2. 性能监控
建议监控以下指标：
- 队列当前大小
- 队列处理频率
- 单批次处理时间
- 系统内存使用率
- 数据库连接池状态

### 3. 告警设置
建议设置以下告警：
- 队列大小持续增长（可能处理能力不足）
- 处理失败率过高
- 系统内存使用率过高
- 长时间无法触发处理

## 故障排查

### 1. 队列不处理
**现象**：队列中有文件但不触发处理

**排查步骤**：
1. 检查配置：`similarity.queue.enabled=true`
2. 查看日志是否有异常
3. 检查定时任务是否正常运行
4. 手动触发测试：`POST /api/similarity/queue/trigger`

### 2. 处理速度慢
**现象**：队列积压严重，处理跟不上

**优化方案**：
1. 调大触发阈值：减少小批次处理
2. 调小超时时间：更及时处理
3. 调整线程池参数：增加并发处理能力
4. 检查数据库性能：优化查询

### 3. 内存使用过高
**现象**：系统内存持续增长

**处理方法**：
1. 调小批次大小：减少单次处理数量
2. 增加批次间休息时间
3. 检查是否有内存泄漏
4. 重启服务释放内存

### 4. 队列丢失数据
**现象**：文件加入队列后查重状态一直没更新

**排查方法**：
1. 检查队列状态：`GET /api/similarity/queue/status`
2. 查看处理日志是否有异常
3. 检查数据库查重状态字段
4. 必要时清空队列重新处理

## 性能优化建议

### 1. 参数调优
根据实际情况调整参数：
```yaml
# 高并发场景
similarity:
  queue:
    trigger-size: 30          # 增大批次
    timeout-minutes: 2        # 缩短超时
    
# 低并发场景  
similarity:
  queue:
    trigger-size: 5           # 减小批次
    timeout-minutes: 5        # 延长超时
```

### 2. 资源配置
```yaml
similarity:
  thread:
    compare-core-size: 16     # 根据CPU核数调整
    compare-max-size: 32      # 控制最大并发
    compare-queue-capacity: 200 # 控制队列容量
```

### 3. 监控优化
- 定期分析处理时间趋势
- 根据业务峰谷调整参数
- 建立性能基线和告警阈值

## 回退方案

如果队列机制出现问题，可以通过以下方式回退：

### 1. 禁用队列模式
```yaml
similarity:
  queue:
    enabled: false
```
设置后重启服务，将恢复到原有的直接处理模式。

### 2. 清空队列
```http
POST /api/similarity/queue/clear
```
清空当前队列中的所有待处理文件。

### 3. 手动处理
如果需要手动处理特定文件：
```sql
-- 查询查重中的文件
SELECT kg_knowledge_bases_id FROM kg_knowledge_bases_d 
WHERE check_repeat_result = '-1' AND is_deleted = '0';

-- 手动调用定时任务接口
POST /api/similarity/process-stuck-checking
```

## 注意事项

1. **生产环境部署**：建议先在测试环境充分测试
2. **参数调整**：根据实际负载情况逐步调整参数
3. **监控告警**：部署后建立完善的监控体系
4. **备份恢复**：重要操作前做好数据备份
5. **渐进升级**：可以先在部分实例启用队列模式进行灰度测试

## 联系支持

如遇问题，请提供以下信息：
- 错误日志
- 队列状态信息
- 系统配置参数
- 复现步骤

---

**文档版本**：v1.0  
**更新时间**：2024年12月  
**适用版本**：kg-similarity-service v1.0+ 