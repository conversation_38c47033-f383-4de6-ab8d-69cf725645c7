# 知识库集团上报定时任务功能说明

## 概述

将原先的手动上报服务改造为定时任务，支持动态配置执行时间、处理数量和开关控制。同时优化了文件格式支持，改为从字典表动态获取。

## 主要修改

### 1. 新增定时任务类
- **文件**: `kg-system-srv/src/main/java/com/ffcs/oss/kg/task/KnowledgeReportTask.java`
- **功能**: 
  - 支持通过配置开关控制任务启停
  - 支持配置cron表达式控制执行时间
  - 支持配置单次最大处理数量

### 2. 动态文件格式支持
- **修改**: `KnowledgeReportService.java`
- **新增方法**:
  - `getSupportedFileFormats()`: 从字典表动态获取支持的文件格式
  - `getDefaultFileFormats()`: 提供默认文件格式作为兜底策略
- **字典表查询**: `SELECT code_value FROM kg_dictionary_c WHERE code_type='document_format_enable'`

### 3. 数据库查询优化
- **新增Mapper方法**: `selectPendingReportKnowledgeBasesWithFormats()`
- **新增XML查询**: 支持动态文件格式过滤的SQL查询
- **查询逻辑**: 使用 `document_format IN (格式列表)` 替代硬编码的正则表达式

## 配置说明

### 配置文件示例
```yaml
knowledge:
  report:
    task:
      # 启用定时任务
      enabled: true
      # 执行时间：每天凌晨2点
      cron: "0 0 2 * * ?"
      # 每次最多处理1000条
      max-count: 1000
```

### 关键配置项

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `knowledge.report.task.enabled` | 是否启用定时任务 | false | true/false |
| `knowledge.report.task.cron` | 定时执行表达式 | "0 0 2 * * ?" | "0 0 3 * * ?" |
| `knowledge.report.task.max-count` | 单次最大处理数量 | 1000 | 500 |

### 常用Cron表达式
- 每天凌晨2点：`0 0 2 * * ?`
- 每天凌晨2点30分：`0 30 2 * * ?`
- 每周一凌晨2点：`0 0 2 ? * MON`
- 每月1号凌晨2点：`0 0 2 1 * ?`
- 每小时执行：`0 0 * * * ?`
- 每30分钟执行：`0 */30 * * * ?`

## 数据库配置

### 字典表配置
需要在 `kg_dictionary_c` 表中配置支持的文件格式：

```sql
-- 插入支持的文件格式
INSERT INTO kg_dictionary_c (code_type, code_value, code_name, order_no) VALUES
('document_format_enable', 'pdf', 'PDF文档', 1),
('document_format_enable', 'doc', 'Word文档', 2),
('document_format_enable', 'docx', 'Word文档', 3),
('document_format_enable', 'ppt', 'PowerPoint演示文稿', 4),
('document_format_enable', 'pptx', 'PowerPoint演示文稿', 5),
('document_format_enable', 'html', 'HTML网页', 6),
('document_format_enable', 'xls', 'Excel表格', 7),
('document_format_enable', 'xlsx', 'Excel表格', 8);
```

## 使用方式

### 1. 启用定时任务
在配置文件中设置：
```yaml
knowledge.report.task.enabled: true
```

### 2. 禁用定时任务
在配置文件中设置：
```yaml
knowledge.report.task.enabled: false
```

### 3. 修改执行时间
```yaml
knowledge.report.task.cron: "0 0 3 * * ?"  # 改为凌晨3点执行
```

### 4. 调整处理数量
```yaml
knowledge.report.task.max-count: 500  # 每次最多处理500条
```

## 日志监控

### 任务执行日志
```
2024-12-12 02:00:00 INFO  开始执行定时知识库集团上报任务，最大处理数量：1000
2024-12-12 02:00:01 INFO  从字典表获取到支持的文件格式: [pdf, doc, docx, ppt, pptx, html, xls, xlsx]
2024-12-12 02:00:02 INFO  查询到 856 条待上报的知识库数据
2024-12-12 02:05:30 INFO  定时知识库集团上报任务执行完成 - 批次号: REPORT_20241212020000_A1B2C3, 总数: 856, 成功: 840, 失败: 16
```

### 日志级别配置
```yaml
logging:
  level:
    com.ffcs.oss.kg.task.KnowledgeReportTask: INFO
    com.ffcs.oss.kg.service.KnowledgeReportService: INFO
```

## 优势

1. **配置灵活**: 支持动态配置执行时间、处理数量和开关
2. **格式动态**: 文件格式支持从字典表动态获取，无需修改代码
3. **性能优化**: 支持限制单次处理数量，避免大批量数据影响性能
4. **容错机制**: 提供默认文件格式作为兜底策略
5. **日志完善**: 提供详细的执行日志便于监控和排查
6. **向下兼容**: 保留原有Controller接口，支持手动触发

## 注意事项

1. 确保字典表中配置了正确的文件格式数据
2. 根据实际业务量调整 `max-count` 配置，避免单次处理过多数据
3. 监控定时任务执行日志，及时发现和处理异常
4. 生产环境建议设置合理的执行时间，避免业务高峰期执行 