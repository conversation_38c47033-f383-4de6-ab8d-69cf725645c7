package com.ffcs.oss.kg.controller;

import com.alibaba.fastjson.JSONObject;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.model.vo.api.GraphDataImportDTO;
import com.ffcs.oss.kg.data.model.vo.api.ImportResult;
import com.ffcs.oss.kg.service.FaultGraphService;
import com.ffcs.oss.kg.service.GraphDataImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 故障图谱数据处理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Api(tags = "故障图谱数据处理")
@RestController
@RequestMapping("/api/faultGraph")
@Slf4j
public class FaultGraphController {

    @Autowired
    private FaultGraphService faultGraphService;
    @Autowired
    private GraphDataImportService graphDataImportService;
    private static final String FULL_CATEGORY_NAME = "故障图谱分类";
    private static final String GRAPH_SPACE_CODE = "fault_graph";
    private static final String TENANT_CODE = "fault_graph_code";
    private static final String TG_NAME = "新故障图谱";
    /**
     * 读取CSV文件并构建故障图谱数据
     * 
     * @return 构建好的图谱数据
     */
    @ApiOperation("读取CSV文件并构建故障图谱数据")
    @PostMapping("/buildFromCsv")
    public ServiceResp<GraphDataImportDTO> buildFromCsv() {
        try {
            log.info("开始读取CSV文件并构建故障图谱数据");
            GraphDataImportDTO result = faultGraphService.buildGraphDataFromCsv();
            log.info("成功构建故障图谱数据，实体数量: {}, 关系数量: {},分类名称:{},图谱名称:{},租户编码:{},图空间编码:{}",
                    result.getEntityList() != null ? result.getEntityList().size() : 0,
                    result.getRsList() != null ? result.getRsList().size() : 0,result.getFullCategoryName()
                    ,result.getTgName(),result.getTenantCode(),result.getGraphSpaceCode());

            result.setFullCategoryName(FULL_CATEGORY_NAME);
            result.setGraphSpaceCode(GRAPH_SPACE_CODE);
            result.setTenantCode(TENANT_CODE);
            result.setTgName(TG_NAME);

            log.info("修改之后的图谱相关信息:分类名称:{},图谱名称:{},租户编码:{},图空间编码:{}",result.getFullCategoryName()
                    ,result.getTgName(),result.getTenantCode(),result.getGraphSpaceCode());
            ImportResult importResult = graphDataImportService.processBatchImport(result);
            log.info("构建资源图谱结果为:{}", JSONObject.toJSONString(importResult));
            return ServiceResp.success(result);
        } catch (Exception e) {
            log.error("构建故障图谱数据失败", e);
            return ServiceResp.fail("构建故障图谱数据失败: " + e.getMessage());
        }
    }
}
