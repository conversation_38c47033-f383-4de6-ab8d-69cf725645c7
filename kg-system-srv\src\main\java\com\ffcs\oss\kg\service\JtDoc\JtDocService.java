package com.ffcs.oss.kg.service.JtDoc;

import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.config.properties.KnowledgeDetailConfig;
import com.ffcs.oss.kg.dfs.service.JtCtdfsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.IOException;

@Service
public class JtDocService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JtCtdfsService jtCtdfsService;

    @Autowired
    private KnowledgeDetailConfig knowledgeDetailConfig;

    /**
     * 文件上传到集团文档系统 - 直接使用InputStream，支持并发
     *
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @param kgName 重命名后的文件名
     * @return 文件在集团文档系统中的路径
     * @throws KnowledgeGraphException 上传过程中的异常
     */
    public String uploadjtFile(InputStream inputStream, String fileName, String kgName) throws KnowledgeGraphException {
        String threadName = Thread.currentThread().getName();
        log.info("开始上传文件到集团文档系统 - 线程: {}, 文件名: {}, 重命名为: {}", 
                threadName, fileName, kgName);

        // 参数校验
        if (inputStream == null) {
            String msg = "上传文件流为空 - 线程: " + threadName;
            log.error(msg);
            throw new KnowledgeGraphException(msg);
        }

        if (StringUtils.isBlank(fileName)) {
            String msg = "文件名为空 - 线程: " + threadName;
            log.error(msg);
            throw new KnowledgeGraphException(msg);
        }

        if (StringUtils.isBlank(kgName)) {
            String msg = "目标文件名为空 - 线程: " + threadName;
            log.error(msg);
            throw new KnowledgeGraphException(msg);
        }

        try {
            String fileDirectory = knowledgeDetailConfig.getJt().getEopFilePath();
            log.info("准备上传文件 - 线程: {}, 目录: {}, 文件名: {} -> {}", 
                    threadName, fileDirectory, fileName, kgName);
            
            // 直接使用InputStream进行上传
            String filePath = jtCtdfsService.uploadJtFile(fileDirectory, fileName, inputStream, kgName);
            
            if (StringUtils.isNotEmpty(filePath)) {
                log.info("文件上传成功 - 线程: {}, 路径: {}", threadName, filePath);
                return filePath;
            } else {
                String msg = "文件上传失败 - 返回路径为空 - 线程: " + threadName;
                log.error(msg);
                throw new KnowledgeGraphException(msg);
            }
        } catch (KnowledgeGraphException e) {
            // 重新抛出业务异常
            String msg = "上传文件到集团文档系统业务异常 - 线程: " + threadName + ", 错误: " + e.getMessage();
            log.error(msg, e);
            throw new KnowledgeGraphException(msg, e);
        } catch (Exception e) {
            String msg = "上传文件到集团文档系统系统异常 - 线程: " + threadName + ", 错误: " + e.getMessage();
            log.error(msg, e);
            throw new KnowledgeGraphException(msg, e);
        }
        // 注意：不在这里关闭InputStream，由调用方负责InputStream的生命周期管理
    }
}
