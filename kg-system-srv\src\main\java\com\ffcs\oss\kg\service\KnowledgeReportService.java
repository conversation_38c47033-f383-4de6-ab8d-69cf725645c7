package com.ffcs.oss.kg.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ffcs.oss.kg.common.core.constant.ReportStatusConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.util.RestTemplateUtil;
import com.ffcs.oss.kg.config.properties.KnowledgeDetailConfig;
import com.ffcs.oss.kg.data.enums.CaseStateEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.AuditEnum;
import com.ffcs.oss.kg.data.rd.entity.DictionaryEntity;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesReportLog;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.data.rd.mapper.DictionaryMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.KgBasesReportLogMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.ModelKnowledgeBasesDMapper;
import com.ffcs.oss.kg.dfs.service.CtdfsService;
import com.ffcs.oss.kg.service.JtDoc.JtDocService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import java.util.Arrays;

/**
 * 知识库上报服务
 * 优化版本，包含完善的日志记录和错误处理
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class KnowledgeReportService {

    private final ModelKnowledgeBasesDMapper knowledgeBasesDMapper;
    private final KgBasesReportLogMapper reportLogMapper;
    private final ObjectMapper objectMapper;
    private final KnowledgeDetailConfig knowledgeDetailConfig;
    private final CtdfsService ctdfsService;
    private final JtDocService jtDocService;
    private final DictionaryMapper dictionaryMapper;

    // 上报状态常量
    public static final String STATUS_REPORTED = "11633876"; // 已上报集团
    public static final String STATUS_OFFLINE = "11633877";  // 集团已下线
    public static final String STATUS_DELETED = "11633878";  // 集团已删除
    public static final String STATUS_NOT_REPORTED = "11634023"; // 未上报集团

    // 操作状态常量
    public static final String OPER_SUCCESS = "1"; // 成功
    public static final String OPER_FAILED = "0";  // 失败

    // 批量处理大小
    private static final int BATCH_SIZE = 100;

    /**
     * 执行知识库集团上报任务
     *
     * @param maxCount 最大处理数量，null表示处理全部
     * @return 上报结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public ReportResult executeKnowledgeReportTask(Integer maxCount) {
        String batchNo = generateBatchNo();
        log.info("开始执行知识库集团上报任务，批次号: {}, 最大处理数量: {}", batchNo, maxCount);

        ReportResult result = new ReportResult();
        result.setBatchNo(batchNo);

        try {
            // 1. 获取支持的文件格式
            Set<String> supportedFormats = getSupportedFileFormats();
            log.info("当前支持的文件格式：{}", supportedFormats);
            
            // 2. 查询待上报的知识库数据（使用动态文件格式）
            List<KnowledgeBasesEntity> pendingList = knowledgeBasesDMapper.selectPendingReportKnowledgeBasesWithFormats(
                    maxCount, new ArrayList<>(supportedFormats));
            
            if (CollectionUtils.isEmpty(pendingList)) {
                log.info("没有待上报的知识库数据");
                result.setMessage("没有待上报的知识库数据");
                return result;
            }

            result.setTotal(pendingList.size());
            log.info("查询到 {} 条待上报的知识库数据", pendingList.size());

            // 2. 分批处理上报
            int successCount = 0;
            int failedCount = 0;

            for (int i = 0; i < pendingList.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, pendingList.size());
                List<KnowledgeBasesEntity> batchList = pendingList.subList(i, endIndex);
                
                log.info("处理第 {} 批数据，数量: {}", (i / BATCH_SIZE + 1), batchList.size());
                
                // 处理当前批次
                BatchResult batchResult = processBatch(batchList, batchNo);
                successCount += batchResult.getSuccessCount();
                failedCount += batchResult.getFailedCount();
            }

            result.setSuccessCount(successCount);
            result.setFailedCount(failedCount);
            result.setMessage("上报任务执行完成");

            log.info("知识库集团上报任务执行完成 - 批次号: {}, 总数: {}, 成功: {}, 失败: {}", 
                    batchNo, result.getTotal(), result.getSuccessCount(), result.getFailedCount());

            return result;

        } catch (Exception e) {
            log.error("知识库集团上报任务执行异常 - 批次号: {}", batchNo, e);
            result.setMessage("上报任务执行异常: " + e.getMessage());
            throw new KnowledgeGraphException("上报任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个批次的上报 - 串行处理版本（生产环境稳定版）
     */
    private BatchResult processBatch(List<KnowledgeBasesEntity> batchList, String batchNo) {
        BatchResult result = new BatchResult();
        Date currentTime = new Date();

        List<Long> successIds = new ArrayList<>();
        List<Long> failedIds = new ArrayList<>();
        Map<Long, String> failureReasons = new HashMap<>(); // 存储失败ID和对应的失败原因

        log.info("开始串行批量处理 - 批次: {}, 总数: {}", batchNo, batchList.size());

        for (KnowledgeBasesEntity entity : batchList) {
            String failReason = null;
            try {
                log.info("开始处理知识库上报 - 批次: {}, ID: {}, 名称: {}", 
                        batchNo, entity.getKgKnowledgeBasesId(), entity.getKnowledgeName());
                
                // 调用上报逻辑
                ReportDetailResult reportResult = performDetailedReport(entity);
                
                if (reportResult.isSuccess()) {
                    successIds.add(entity.getKgKnowledgeBasesId());
                    // 记录成功日志
                    recordReportLog(entity.getKgKnowledgeBasesId(), batchNo, "BATCH_REPORT", 
                                   STATUS_REPORTED, getCurrentUser(), currentTime, OPER_SUCCESS, reportResult.getMessage());
                    log.info("知识库上报成功 - ID: {}, 名称: {}, 详情: {}", 
                            entity.getKgKnowledgeBasesId(), entity.getKnowledgeName(), reportResult.getMessage());
                } else {
                    Long failedId = entity.getKgKnowledgeBasesId();
                    failedIds.add(failedId);
                    failReason = reportResult.getErrorMessage();
                    // 存储失败原因，与ID关联
                    failureReasons.put(failedId, StringUtils.isNotBlank(failReason) ? failReason : "未知错误");
                    
                    // 记录失败日志，包含详细错误信息
                    recordReportLog(failedId, batchNo, "BATCH_REPORT",
                            ReportStatusConstant.REPORT_FAILED, getCurrentUser(), currentTime, OPER_FAILED, failReason);
                    log.error("知识库上报失败 - ID: {}, 名称: {}, 错误详情: {}", 
                            entity.getKgKnowledgeBasesId(), entity.getKnowledgeName(), failReason);
                }

            } catch (KnowledgeGraphException e) {
                // 业务异常
                Long failedId = entity.getKgKnowledgeBasesId();
                failedIds.add(failedId);
                failReason = "业务异常: " + e.getMessage();
                failureReasons.put(failedId, failReason);
                recordReportLog(failedId, batchNo, "BATCH_REPORT",
                        ReportStatusConstant.REPORT_FAILED, getCurrentUser(), currentTime, OPER_FAILED, failReason);
                log.error("知识库上报业务异常 - ID: {}, 名称: {}, 错误详情: {}", 
                        entity.getKgKnowledgeBasesId(), entity.getKnowledgeName(), e.getMessage(), e);
                
            } catch (Exception e) {
                // 系统异常
                Long failedId = entity.getKgKnowledgeBasesId();
                failedIds.add(failedId);
                failReason = "系统异常: " + e.getMessage();
                failureReasons.put(failedId, failReason);
                recordReportLog(failedId, batchNo, "BATCH_REPORT",
                        ReportStatusConstant.REPORT_FAILED, getCurrentUser(), currentTime, OPER_FAILED, failReason);
                log.error("知识库上报系统异常 - ID: {}, 名称: {}, 错误详情: {}", 
                        entity.getKgKnowledgeBasesId(), entity.getKnowledgeName(), e.getMessage(), e);
            }
        }

        // 批量更新数据库状态
        updateBatchStatusSync(successIds, failedIds, failureReasons, batchNo, currentTime);
        
        result.setSuccessCount(successIds.size());
        result.setFailedCount(failedIds.size());
        
        log.info("串行批量处理完成 - 批次: {}, 总数: {}, 成功: {}, 失败: {}", 
                batchNo, batchList.size(), successIds.size(), failedIds.size());
        
        return result;
    }
    
    /**
     * 同步批量更新数据库状态
     */
    private void updateBatchStatusSync(List<Long> successIds, List<Long> failedIds, Map<Long, String> failureReasons, String batchNo, Date currentTime) {
        try {
            // 批量更新成功的记录
            if (!successIds.isEmpty()) {
                try {
                    knowledgeBasesDMapper.batchUpdateReportStatus(successIds, STATUS_REPORTED, 
                            currentTime, batchNo, "1", currentTime, "上报成功");
                    log.info("批量更新成功状态完成 - 批次: {}, 数量: {}", batchNo, successIds.size());
                } catch (Exception e) {
                    log.error("批量更新成功状态异常 - 批次: {}, 数量: {}", batchNo, successIds.size(), e);
                }
            }
            
            // 更新失败的记录，使用具体的失败原因
            if (!failedIds.isEmpty()) {
                try {
                    // 由于每个失败记录可能有不同的失败原因，需要逐个更新
                    int updatedCount = 0;
                    for (Long failedId : failedIds) {
                        try {
                            String failureReason = failureReasons.getOrDefault(failedId, "上报失败");
                            // 限制失败原因长度，避免数据库字段长度限制问题
                            if (failureReason.length() > 500) {
                                failureReason = failureReason.substring(0, 500) + "...";
                            }
                            
                            knowledgeBasesDMapper.batchUpdateReportStatus(Arrays.asList(failedId), 
                                    ReportStatusConstant.REPORT_FAILED, currentTime, batchNo, "0", 
                                    currentTime, failureReason);
                            updatedCount++;
                        } catch (Exception e) {
                            log.error("更新单个失败记录异常 - 批次: {}, ID: {}, 失败原因: {}", 
                                    batchNo, failedId, failureReasons.get(failedId), e);
                        }
                    }
                    log.info("批量更新失败状态完成 - 批次: {}, 总失败数量: {}, 成功更新数量: {}", 
                            batchNo, failedIds.size(), updatedCount);
                } catch (Exception e) {
                    log.error("批量更新失败状态异常 - 批次: {}, 数量: {}", batchNo, failedIds.size(), e);
                }
            }
        } catch (Exception e) {
            log.error("批量更新状态总体异常 - 批次: {}", batchNo, e);
        }
    }
    

    
    /**
     * 获取支持的文件格式列表（从字典表动态获取）
     */
    private Set<String> getSupportedFileFormats() {
        try {
            // 使用MyBatis-Plus的查询条件查询字典表
            List<DictionaryEntity> formatEntities = dictionaryMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<DictionaryEntity>()
                            .eq(DictionaryEntity::getCodeType, "document_format_enable")
            );
            
            if (formatEntities == null || formatEntities.isEmpty()) {
                log.warn("字典表中未找到document_format_enable配置，使用默认格式");
                return getDefaultFileFormats();
            }
            
            Set<String> supportedFormats = new HashSet<>();
            for (DictionaryEntity entity : formatEntities) {
                if (StringUtils.isNotBlank(entity.getCodeValue())) {
                    supportedFormats.add(entity.getCodeValue().toLowerCase());
                }
            }
            
            log.info("从字典表获取到支持的文件格式: {}", supportedFormats);
            return supportedFormats.isEmpty() ? getDefaultFileFormats() : supportedFormats;
            
        } catch (Exception e) {
            log.error("从字典表获取文件格式失败，使用默认格式", e);
            return getDefaultFileFormats();
        }
    }
    
    /**
     * 获取默认文件格式（兜底策略）
     */
    private Set<String> getDefaultFileFormats() {
        return new HashSet<>(Arrays.asList(
                "pdf", "doc", "docx", "ppt", "pptx", "html", "xls", "xlsx"
        ));
    }

    /**
     * 执行详细的上报逻辑，返回详细结果
     */
    private ReportDetailResult performDetailedReport(KnowledgeBasesEntity entity) {
        try {
            // 直接调用单文件上报方法，获取更详细的结果
            return performSingleFileReportWithDetail(entity, getCurrentUser());
        } catch (Exception e) {
            log.error("执行详细上报异常 - 知识库ID: {}", entity.getKgKnowledgeBasesId(), e);
            return ReportDetailResult.failure("上报异常: " + e.getMessage());
        }
    }
    
    /**
     * 执行单个文件的集团上报逻辑（返回详细结果）- 完整实现
     */
    private ReportDetailResult performSingleFileReportWithDetail(KnowledgeBasesEntity knowledgeBases, String operUserId) {
        // 从字典表动态获取允许的文件类型集合
        Set<String> allowedFileTypes = getSupportedFileFormats();
        
        try {
            log.info("开始处理知识库文件上报 - ID: {}, 名称: {}, 操作用户: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), knowledgeBases.getKnowledgeName(), operUserId);
            
            // 1. 文件基本信息获取和校验
            String filePath = knowledgeBases.getKnowledgeFilePath();
            if (StringUtils.isBlank(filePath)) {
                String error = "知识库文件路径为空 - ID: " + knowledgeBases.getKgKnowledgeBasesId();
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            String fileName = getFileNameFromPath(filePath);
            String docType = getFileExtension(fileName).toLowerCase();
            
            log.info("文件信息校验 - 知识库ID: {}, 文件路径: {}, 文件名: {}, 文件类型: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), filePath, fileName, docType);
            
            // 2. 文件类型校验
            if (StringUtils.isBlank(docType)) {
                String error = String.format("无法获取文件类型 - 知识库ID: %s, 文件名: %s", 
                        knowledgeBases.getKgKnowledgeBasesId(), fileName);
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            if (!allowedFileTypes.contains(docType)) {
                String error = String.format("文件类型不支持 - 知识库ID: %s, 文件类型: %s, 允许的文件类型: %s", 
                        knowledgeBases.getKgKnowledgeBasesId(), docType, allowedFileTypes);
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            // 3. 验证知识库基本信息
            if (StringUtils.isBlank(knowledgeBases.getKnowledgeName())) {
                String error = "知识库名称为空 - ID: " + knowledgeBases.getKgKnowledgeBasesId();
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            log.info("知识库信息验证通过 - ID: {}, 准备调用集团接口", knowledgeBases.getKgKnowledgeBasesId());
            
            // 4. 调用集团接口 - 包含文件下载、上传和接口调用的完整流程
            return callJtBatchSaveApiWithDetail(knowledgeBases, docType, operUserId);
            
        } catch (KnowledgeGraphException e) {
            // 业务异常，直接返回错误信息
            String error = String.format("知识库上报业务异常 - 知识库ID: %s, 知识库名称: %s, 错误详情: %s", 
                    knowledgeBases.getKgKnowledgeBasesId(), knowledgeBases.getKnowledgeName(), e.getMessage());
            log.error(error, e);
            return ReportDetailResult.failure(error);
            
        } catch (Exception e) {
            // 系统异常
            String error = String.format("知识库上报系统异常 - 知识库ID: %s, 知识库名称: %s, 错误详情: %s", 
                    knowledgeBases.getKgKnowledgeBasesId(), knowledgeBases.getKnowledgeName(), e.getMessage());
            log.error(error, e);
            return ReportDetailResult.failure(error);
        }
    }
    
    /**
     * 调用集团批量保存接口（返回详细结果）- 实际实现
     */
    private ReportDetailResult callJtBatchSaveApiWithDetail(KnowledgeBasesEntity knowledgeBases, String docType, String operUserId) {
        try {
            log.info("开始调用集团批量保存接口 - 知识库ID: {}, 文件类型: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), docType);
            
            // 1. 下载文件并直接上传到集团系统 - 流式处理，优化内存使用
            String filePath = knowledgeBases.getKnowledgeFilePath();
            String fileName = getFileNameFromPath(filePath);
            String kgName = knowledgeBases.getKnowledgeName() + "." + docType;
            
            String uploadedFilePath = downloadAndUploadFileToJtSystem(filePath, fileName, kgName);
            if (StringUtils.isBlank(uploadedFilePath)) {
                String error = "文件下载并上传到集团系统失败 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId() + ", 文件路径: " + filePath;
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            // 3. 构建集团接口请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-APP-ID", "706e60176b326035c658dfb3b00627c6");
            headers.set("X-APP-KEY", "b67701ddcfbab49ba1df7f5244df07b8");
            
            // 构建文件请求对象
            Object fileReq = buildFileRequest(knowledgeBases, docType, kgName, uploadedFilePath, operUserId);
            if (fileReq == null) {
                String error = "构建集团接口请求对象失败 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId();
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("fileReqList", Arrays.asList(fileReq));
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 4. 获取实际的集团接口URL
            String batchSaveUrl = knowledgeDetailConfig.getJt().getBatchSaveFilesUrl();
            if (StringUtils.isBlank(batchSaveUrl)) {
                String error = "集团接口URL配置为空 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId();
                log.error(error);
                return ReportDetailResult.failure(error);
            }
            
            String requestBodyString = objectMapper.writeValueAsString(requestBody);
            log.info("准备调用集团批量保存接口 - 知识库ID: {}, 请求URL: {}, 请求体大小: {} 字符", 
                    knowledgeBases.getKgKnowledgeBasesId(), batchSaveUrl, requestBodyString.length());
            
            // 5. 发送请求到集团接口
            ResponseEntity<String> response = RestTemplateUtil.exchange(
                    batchSaveUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            
            return handleJtApiResponseWithDetail(response, knowledgeBases);
            
        } catch (KnowledgeGraphException e) {
            // 业务异常已经在上游方法中处理过
            String error = "集团接口调用业务异常 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId() + ", 错误详情: " + e.getMessage();
            log.error(error, e);
            return ReportDetailResult.failure(error);
            
        } catch (JsonProcessingException e) {
            String error = "JSON处理异常 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId() + ", 错误详情: " + e.getMessage();
            log.error(error, e);
            return ReportDetailResult.failure(error);
            
        } catch (Exception e) {
            String error = "调用集团文档接口系统异常 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId() + ", 错误详情: " + e.getMessage();
            log.error(error, e);
            return ReportDetailResult.failure(error);
        }
    }
    
    /**
     * 处理集团接口响应（返回详细结果）- 实际实现
     */
    private ReportDetailResult handleJtApiResponseWithDetail(ResponseEntity<String> response, KnowledgeBasesEntity knowledgeBases) {
        try {
            log.info("开始处理集团接口响应 - 知识库ID: {}, HTTP状态码: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), response.getStatusCode());
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String body = response.getBody();
                log.info("集团接口HTTP调用成功 - 知识库ID: {}, 状态码: {}, 响应体长度: {} 字符", 
                        knowledgeBases.getKgKnowledgeBasesId(), response.getStatusCode(), 
                        body != null ? body.length() : 0);
                
                // 记录完整响应体到日志（用于问题排查）
                if (StringUtils.isNotBlank(body)) {
                    log.debug("集团接口完整响应体 - 知识库ID: {}, 响应内容: {}", 
                            knowledgeBases.getKgKnowledgeBasesId(), body);
                }
                
                // 解析响应体中的业务状态
                if (StringUtils.isNotBlank(body)) {
                    try {
                        // 尝试解析为通用的Map结构
                        @SuppressWarnings("unchecked")
                        Map<String, Object> responseMap = objectMapper.readValue(body, Map.class);
                        
                        Object codeObj = responseMap.get("code");
                        Object msgObj = responseMap.get("msg");
                        Object messageObj = responseMap.get("message");
                        Object dataObj = responseMap.get("data");
                        
                        // 获取响应码
                        Integer responseCode = null;
                        if (codeObj instanceof Number) {
                            responseCode = ((Number) codeObj).intValue();
                        } else if (codeObj instanceof String) {
                            try {
                                responseCode = Integer.parseInt((String) codeObj);
                            } catch (NumberFormatException e) {
                                log.warn("无法解析响应码 - 知识库ID: {}, code值: {}", 
                                        knowledgeBases.getKgKnowledgeBasesId(), codeObj);
                            }
                        }
                        
                        // 获取响应消息
                        String responseMsg = msgObj != null ? msgObj.toString() : 
                                            (messageObj != null ? messageObj.toString() : "无响应消息");
                        
                        log.info("集团接口业务响应 - 知识库ID: {}, 业务代码: {}, 业务消息: {}", 
                                knowledgeBases.getKgKnowledgeBasesId(), responseCode, responseMsg);
                        
                        // 判断业务处理结果
                        if (responseCode != null && responseCode == 200) {
                            String successMsg = String.format("集团接口调用成功 - 业务代码: %d, 消息: %s", responseCode, responseMsg);
                            if (dataObj != null) {
                                successMsg += ", 返回数据: " + dataObj.toString();
                            }
                            return ReportDetailResult.success(successMsg);
                        } else {
                            String errorMsg = String.format("集团接口返回业务错误 - 业务代码: %s, 消息: %s", responseCode, responseMsg);
                            if (dataObj != null) {
                                errorMsg += ", 返回数据: " + dataObj.toString();
                            }
                            return ReportDetailResult.failure(errorMsg);
                        }
                        
                    } catch (JsonProcessingException e) {
                        log.error("解析集团接口JSON响应异常 - 知识库ID: {}, JSON内容: {}", 
                                knowledgeBases.getKgKnowledgeBasesId(), 
                                body.length() > 500 ? body.substring(0, 500) + "..." : body, e);
                        
                        // JSON解析失败时，尝试简单的字符串匹配
                        if (body.toLowerCase().contains("success") || body.contains("\"code\":200") || body.contains("\"code\": 200")) {
                            return ReportDetailResult.success("集团接口调用成功（JSON解析失败，基于内容判断）: " + 
                                    body.substring(0, Math.min(body.length(), 200)));
                        } else {
                            return ReportDetailResult.failure("集团接口响应JSON解析失败，且内容疑似错误: " + 
                                    body.substring(0, Math.min(body.length(), 300)) + ", 解析异常: " + e.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("处理集团接口响应内容异常 - 知识库ID: {}", knowledgeBases.getKgKnowledgeBasesId(), e);
                        return ReportDetailResult.failure("处理集团接口响应异常: " + e.getMessage() + 
                                ", 响应体前100字符: " + body.substring(0, Math.min(body.length(), 100)));
                    }
                } else {
                    String error = "集团接口返回空响应体 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId();
                    log.error(error);
                    return ReportDetailResult.failure(error);
                }
            } else {
                String error = String.format("集团接口HTTP调用失败 - 知识库ID: %s, HTTP状态码: %s, 响应体: %s", 
                        knowledgeBases.getKgKnowledgeBasesId(), response.getStatusCode(), 
                        response.getBody() != null ? 
                                (response.getBody().length() > 300 ? response.getBody().substring(0, 300) + "..." : response.getBody()) 
                                : "null");
                log.error(error);
                return ReportDetailResult.failure(error);
            }
        } catch (Exception e) {
            String error = "处理集团接口响应系统异常 - 知识库ID: " + knowledgeBases.getKgKnowledgeBasesId() + ", 错误详情: " + e.getMessage();
            log.error(error, e);
            return ReportDetailResult.failure(error);
        }
    }
    
    /**
     * 上报详细结果类
     */
    private static class ReportDetailResult {
        private boolean success;
        private String message;
        private String errorMessage;
        
        public static ReportDetailResult success(String message) {
            ReportDetailResult result = new ReportDetailResult();
            result.success = true;
            result.message = message;
            return result;
        }
        
        public static ReportDetailResult failure(String errorMessage) {
            ReportDetailResult result = new ReportDetailResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getErrorMessage() { return errorMessage; }
    }

    /**
     * 从文件路径中提取文件名
     */
    private String getFileNameFromPath(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return "";
        }
        try {
            // 使用简单的字符串操作提取文件名
            int lastIndex = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
            return lastIndex >= 0 ? filePath.substring(lastIndex + 1) : filePath;
        } catch (Exception e) {
            log.error("提取文件名异常 - 文件路径: {}, 错误详情: {}", filePath, e.getMessage(), e);
            return filePath; // 返回原始路径作为fallback
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        try {
            int lastDotIndex = fileName.lastIndexOf('.');
            return lastDotIndex > 0 && lastDotIndex < fileName.length() - 1 ? 
                    fileName.substring(lastDotIndex + 1) : "";
        } catch (Exception e) {
            log.error("获取文件扩展名异常 - 文件名: {}, 错误详情: {}", fileName, e.getMessage(), e);
            return "";
        }
    }
    
    /**
     * 下载文件并直接上传到集团系统 - 并发安全版本
     * 使用同步下载方式避免连接冲突
     */
    private String downloadAndUploadFileToJtSystem(String filePath, String fileName, String kgName) {
        String threadName = Thread.currentThread().getName();
        log.info("开始下载并上传文件 - 线程: {}, 文件路径: {}, 文件名: {}, 知识库名称: {}", 
                threadName, filePath, fileName, kgName);
        
        InputStream uploadInputStream = null;
        try {
            // 1. 同步下载文件到字节数组，避免并发连接问题
            byte[] fileData = downloadFileToByteArray(filePath, threadName);
            if (fileData == null || fileData.length == 0) {
                String error = "文件下载失败或文件为空 - 线程: " + threadName + ", 文件路径: " + filePath;
                log.error(error);
                throw new KnowledgeGraphException(error);
            }
            
            log.info("文件下载成功 - 线程: {}, 文件路径: {}, 文件大小: {} bytes, 准备上传到集团系统", 
                    threadName, filePath, fileData.length);
            
            // 2. 使用字节数组创建InputStream进行上传
            uploadInputStream = new java.io.ByteArrayInputStream(fileData);
            String uploadedFilePath = jtDocService.uploadjtFile(uploadInputStream, fileName, kgName);
            
            if (StringUtils.isBlank(uploadedFilePath)) {
                String error = "集团文档上传服务返回空路径 - 线程: " + threadName + ", 文件名: " + fileName;
                log.error(error);
                throw new KnowledgeGraphException(error);
            }
            
            log.info("文件上传集团系统成功 - 线程: {}, 文件名: {}, 文件大小: {} bytes, 集团文件路径: {}", 
                    threadName, fileName, fileData.length, uploadedFilePath);
            return uploadedFilePath;
            
        } catch (KnowledgeGraphException e) {
            // 业务异常直接重新抛出
            String error = String.format("文件上传业务异常 - 线程: %s, 文件路径: %s, 文件名: %s, 错误详情: %s", 
                    threadName, filePath, fileName, e.getMessage());
            log.error(error, e);
            throw new KnowledgeGraphException(error, e);
            
        } catch (Exception e) {
            String error = String.format("文件上传系统异常 - 线程: %s, 文件路径: %s, 文件名: %s, 错误详情: %s", 
                    threadName, filePath, fileName, e.getMessage());
            log.error(error, e);
            throw new KnowledgeGraphException(error, e);
        } finally {
            // 3. 关闭InputStream（ByteArrayInputStream关闭是无害的）
            if (uploadInputStream != null) {
                try {
                    uploadInputStream.close();
                    log.debug("上传输入流已关闭 - 线程: {}, 文件路径: {}", threadName, filePath);
                } catch (IOException e) {
                    log.warn("关闭上传输入流异常 - 线程: {}, 文件路径: {}, 错误详情: {}", 
                            threadName, filePath, e.getMessage());
                }
            }
        }
    }
    
    /**
     * 同步下载文件到字节数组，避免并发连接问题
     */
    private byte[] downloadFileToByteArray(String filePath, String threadName) {
        // 使用同步块确保下载操作的线程安全
        synchronized (ctdfsService) {
            InputStream downloadStream = null;
            try {
                log.debug("开始同步下载文件 - 线程: {}, 文件路径: {}", threadName, filePath);
                
                // 使用原始的downloadStream方法，但立即读取到内存
                downloadStream = ctdfsService.downloadStream(filePath);
                if (downloadStream == null) {
                    log.error("下载文件流为null - 线程: {}, 文件路径: {}", threadName, filePath);
                    return null;
                }
                
                // 将流完全读取到字节数组
                byte[] fileData = IOUtils.toByteArray(downloadStream);
                log.debug("文件下载到内存完成 - 线程: {}, 文件路径: {}, 大小: {} bytes", 
                        threadName, filePath, fileData.length);
                
                return fileData;
                
            } catch (IOException e) {
                log.error("下载文件到字节数组IO异常 - 线程: {}, 文件路径: {}", threadName, filePath, e);
                return null;
            } catch (Exception e) {
                log.error("下载文件到字节数组系统异常 - 线程: {}, 文件路径: {}", threadName, filePath, e);
                return null;
            } finally {
                // 确保下载流被关闭
                if (downloadStream != null) {
                    try {
                        downloadStream.close();
                        log.debug("下载流已关闭 - 线程: {}, 文件路径: {}", threadName, filePath);
                    } catch (IOException e) {
                        log.warn("关闭下载流异常 - 线程: {}, 文件路径: {}", threadName, filePath, e);
                    }
                }
            }
        }
    }
    
    /**
     * 构建集团接口请求对象
     */
    private Object buildFileRequest(KnowledgeBasesEntity knowledgeBases, String docType, 
                                  String kgName, String uploadedFilePath, String operUserId) {
        try {
            // TODO: 创建实际的FileReq对象
            Map<String, Object> fileReq = new HashMap<>();
            fileReq.put("knName", knowledgeBases.getKnowledgeName());
            
            // TODO: 根据实际配置获取基础路径
            String stpFilePath = knowledgeDetailConfig.getJt().getBaseFilePath() + kgName;
            log.info("batchSaveFiles ftpPath入参：{}", stpFilePath);
            fileReq.put("fileFtpPath", stpFilePath);
            fileReq.put("fileType", docType);
            fileReq.put("isWdd", false);
            
            // 各种标签转换 - TODO: 实现convertIdsToCodeValues方法
            fileReq.put("sceneLabels", convertIdsToCodeValues(knowledgeBases.getApplicationScene()));
            fileReq.put("professionalLabels", convertIdsToCodeValues(knowledgeBases.getMajor()));
            fileReq.put("processLabels", convertIdsToCodeValues(knowledgeBases.getFlowScene()));
            fileReq.put("docOrigins", convertIdsToCodeValues(knowledgeBases.getKnowledgeOrigin()));
            
            // 公开范围和有效期 - TODO: 集成字典查询
            fileReq.put("openScope", getDictionaryCodeValue(knowledgeBases.getPublicity()));
            fileReq.put("periodValidity", getDictionaryCodeValue(knowledgeBases.getPeriodValidity()));
            
            // TODO: 根据实际配置获取区域编码
            fileReq.put("regionCode", knowledgeDetailConfig.getJt().getRegionCode());

            if(StringUtils.isNotBlank(knowledgeBases.getAuthor())){
                fileReq.put("author", knowledgeBases.getAuthor());
            }else {
                fileReq.put("author", knowledgeDetailConfig.getJt().getJtAuthor());
            }
            fileReq.put("authorName", StringUtils.isNotBlank(knowledgeBases.getAuthor()) ? 
                    knowledgeBases.getAuthor() : "默认作者"); // TODO: 从配置获取默认作者
            fileReq.put("createBy", knowledgeBases.getCreatedUserName());
            fileReq.put("docTypes", convertIdsToCodeValues(knowledgeBases.getDocumentType()));
            fileReq.put("fileSource", "province");
            
            log.debug("构建集团文档请求对象 - 知识库ID: {}, 请求详情: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), objectMapper.writeValueAsString(fileReq));
            
            return fileReq;
            
        } catch (Exception e) {
            log.error("构建集团接口请求对象异常 - 知识库ID: {}, 错误详情: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 调用集团批量保存接口
     */
    private boolean callJtBatchSaveApi(List<Object> fileReqs, KnowledgeBasesEntity knowledgeBases) {
        try {
            if (CollectionUtils.isEmpty(fileReqs)) {
                log.error("没有有效的文件请求对象 - 知识库ID: {}", knowledgeBases.getKgKnowledgeBasesId());
                return false;
            }
            
            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-APP-ID", "706e60176b326035c658dfb3b00627c6");
            headers.set("X-APP-KEY", "b67701ddcfbab49ba1df7f5244df07b8");
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("fileReqList", fileReqs);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // TODO: 从配置获取实际的集团接口URL
            // String batchSaveUrl = knowledgeBaseConfig.getJt().getBatchSaveFilesUrl();
            String batchSaveUrl = "http://mock-jt-api.com/batchSaveFiles"; // 临时模拟URL
            
            String requestBodyString = objectMapper.writeValueAsString(requestBody);
            log.info("准备调用集团批量保存接口 - 知识库ID: {}, 请求URL: {}, 请求体大小: {} 字符", 
                    knowledgeBases.getKgKnowledgeBasesId(), batchSaveUrl, requestBodyString.length());
            
            // 发送请求
            ResponseEntity<String> response = RestTemplateUtil.exchange(
                    batchSaveUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            
            return handleJtApiResponse(response, knowledgeBases);
            
        } catch (JsonProcessingException e) {
            log.error("JSON处理异常 - 知识库ID: {}, 错误详情: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("调用集团文档接口异常 - 知识库ID: {}, 错误详情: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 处理集团接口响应
     */
    private boolean handleJtApiResponse(ResponseEntity<String> response, KnowledgeBasesEntity knowledgeBases) {
        try {
            if (response.getStatusCode().is2xxSuccessful()) {
                String body = response.getBody();
                log.info("集团接口调用成功 - 知识库ID: {}, 状态码: {}, 响应体: {}", 
                        knowledgeBases.getKgKnowledgeBasesId(), response.getStatusCode(), body);
                
                // TODO: 解析实际的ApiResponseVm对象
                // ApiResponseVm apiResponse = objectMapper.readValue(body, ApiResponseVm.class);
                // if (apiResponse.getCode() == 200) {
                //     log.info("集团接口返回成功 - 知识库ID: {}, 消息: {}", 
                //             knowledgeBases.getKgKnowledgeBasesId(), apiResponse.getMsg());
                //     return true;
                // } else {
                //     log.error("集团接口返回错误 - 知识库ID: {}, 代码: {}, 消息: {}", 
                //             knowledgeBases.getKgKnowledgeBasesId(), apiResponse.getCode(), apiResponse.getMsg());
                //     return false;
                // }
                
                // 临时模拟响应处理
                if (StringUtils.isNotBlank(body) && body.contains("\"code\":200")) {
                    log.info("集团接口返回成功 - 知识库ID: {}", knowledgeBases.getKgKnowledgeBasesId());
                    return true;
                } else {
                    log.error("集团接口返回错误 - 知识库ID: {}, 响应体: {}", 
                            knowledgeBases.getKgKnowledgeBasesId(), body);
                    return false;
                }
            } else {
                log.error("集团接口调用失败 - 知识库ID: {}, 状态码: {}, 响应体: {}", 
                        knowledgeBases.getKgKnowledgeBasesId(), response.getStatusCode(), response.getBody());
                return false;
            }
        } catch (Exception e) {
            log.error("处理集团接口响应异常 - 知识库ID: {}, 错误详情: {}", 
                    knowledgeBases.getKgKnowledgeBasesId(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 将ID字符串转换为代码值列表 - 实际实现
     */
    private List<String> convertIdsToCodeValues(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ArrayList<>();
        }
        try {
            // 分割字符串并查询每个ID对应的代码值
            String[] idArray = ids.split(",");
            List<String> codeValues = new ArrayList<>();
            
            for (String id : idArray) {
                String trimmedId = id.trim();
                if (StringUtils.isNotBlank(trimmedId)) {
                    String codeValue = getDictionaryCodeValue(trimmedId);
                    if (StringUtils.isNotBlank(codeValue)) {
                        codeValues.add(codeValue);
                    } else {
                        log.warn("字典ID转换失败，使用原始ID - ID: {}", trimmedId);
                        codeValues.add(trimmedId); // 如果转换失败，使用原始ID
                    }
                }
            }
            
            log.debug("ID字符串转换完成 - 原始IDs: {}, 转换后代码值: {}", ids, codeValues);
            return codeValues;
            
        } catch (Exception e) {
            log.error("转换ID字符串为代码值异常 - IDs: {}, 错误详情: {}", ids, e.getMessage(), e);
            // 异常时返回分割后的原始ID列表
            return Arrays.asList(ids.split(","));
        }
    }
    
    /**
     * 获取字典代码值 - 实际实现
     */
    private String getDictionaryCodeValue(String dictionaryId) {
        if (StringUtils.isBlank(dictionaryId)) {
            return "";
        }
        try {
            log.debug("开始查询字典代码值 - 字典ID: {}", dictionaryId);
            
            // 实际的字典查询
            Long id = Long.parseLong(dictionaryId);
            DictionaryEntity dictionaryEntity = dictionaryMapper.selectById(id);
            
            if (dictionaryEntity != null) {
                String codeValue = dictionaryEntity.getCodeValue();
                log.debug("字典查询成功 - 字典ID: {}, 代码值: {}", dictionaryId, codeValue);
                return StringUtils.isNotBlank(codeValue) ? codeValue : "";
            } else {
                log.warn("字典记录不存在 - 字典ID: {}", dictionaryId);
                return "";
            }
            
        } catch (NumberFormatException e) {
            log.error("字典ID格式错误 - 字典ID: {}, 错误详情: {}", dictionaryId, e.getMessage());
            return "";
        } catch (Exception e) {
            log.error("查询字典代码值异常 - 字典ID: {}, 错误详情: {}", dictionaryId, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 记录上报日志
     */
    private void recordReportLog(Long kgKnowledgeBasesId, String batchNo, String operType, 
                               String reportStatus, String operUser, Date operTime, 
                               String operStatus, String failReason) {
        try {
            KgBasesReportLog reportLog = new KgBasesReportLog();
            reportLog.setKgKnowledgeBasesId(kgKnowledgeBasesId);
            reportLog.setBatchNo(batchNo);
            reportLog.setOperType(operType);
            reportLog.setReportStatus(reportStatus);
            reportLog.setOperUser(operUser);
            reportLog.setOperTime(operTime);
            reportLog.setOperStatus(operStatus);
            reportLog.setFailReason(failReason);
            reportLog.setCreatedTime(operTime);
            reportLog.setUpdatedTime(operTime);

            reportLogMapper.insert(reportLog);
        } catch (Exception e) {
            log.error("记录上报日志失败 - 知识库ID: {}, 批次号: {}", kgKnowledgeBasesId, batchNo, e);
        }
    }

    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "REPORT_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) 
               + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 获取当前用户 - 实际实现
     */
    private String getCurrentUser() {
        try {
            // 从安全上下文获取当前用户
            // 根据实际项目的用户获取方式调整
            String currentUser = System.getProperty("user.name", "SYSTEM"); // 临时fallback
            
            // TODO: 集成实际的用户获取逻辑，例如：
            // String currentUser = PtSecurityUtils.getCurrentUser();
            // 或者从Spring Security Context中获取
            // Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            // String currentUser = authentication != null ? authentication.getName() : "SYSTEM";
            
            log.debug("获取当前用户成功: {}", currentUser);
            return StringUtils.isNotBlank(currentUser) ? currentUser : "SYSTEM";
            
        } catch (Exception e) {
            log.warn("获取当前用户异常，使用默认用户: {}", e.getMessage());
            return "SYSTEM";
        }
    }

    /**
     * 查询上报统计信息
     */
    public Map<String, Object> getReportStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            Long successCount = reportLogMapper.countByOperStatus(OPER_SUCCESS);
            Long failedCount = reportLogMapper.countByOperStatus(OPER_FAILED);
            Long totalCount = successCount + failedCount;

            statistics.put("totalCount", totalCount);
            statistics.put("successCount", successCount);
            statistics.put("failedCount", failedCount);
            statistics.put("successRate", totalCount > 0 ? (double) successCount / totalCount : 0.0);

            // 查询最近的上报日志
            List<KgBasesReportLog> recentLogs = reportLogMapper.selectRecent(10);
            statistics.put("recentLogs", recentLogs);

        } catch (Exception e) {
            log.error("查询上报统计信息失败", e);
        }

        return statistics;
    }

    /**
     * 上报结果统计类
     */
    public static class ReportResult {
        private String batchNo;
        private int total;
        private int successCount;
        private int failedCount;
        private String message;

        // Getters and Setters
        public String getBatchNo() { return batchNo; }
        public void setBatchNo(String batchNo) { this.batchNo = batchNo; }
        public int getTotal() { return total; }
        public void setTotal(int total) { this.total = total; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 批次处理结果类
     */
    private static class BatchResult {
        private int successCount;
        private int failedCount;

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
    }

    /**
     * 批量上报知识库到集团
     * 
     * @param knowledgeBaseIds 知识库ID列表
     * @return 批量上报结果
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchReportResult batchReportKnowledgeBases(List<Long> knowledgeBaseIds) {
        String batchNo = generateBatchNo();
        log.info("开始批量上报知识库 - 批次号: {}, 数量: {}, ID列表: {}", batchNo, knowledgeBaseIds.size(), knowledgeBaseIds);
        
        BatchReportResult result = new BatchReportResult();
        result.setBatchNo(batchNo);
        result.setTotalCount(knowledgeBaseIds.size());
        
        Date currentTime = new Date();
        List<Long> successIds = new ArrayList<>();
        List<Long> failedIds = new ArrayList<>();
        Map<Long, String> failureReasons = new HashMap<>();
        
        for (Long knowledgeBaseId : knowledgeBaseIds) {
            try {
                log.info("开始处理单个知识库上报 - 批次: {}, 知识库ID: {}", batchNo, knowledgeBaseId);
                
                // 查询知识库信息
                KnowledgeBasesEntity knowledgeBase = knowledgeBasesDMapper.selectById(knowledgeBaseId);
                if (knowledgeBase == null) {
                    String error = "知识库不存在";
                    failedIds.add(knowledgeBaseId);
                    failureReasons.put(knowledgeBaseId, error);
                    recordReportLog(knowledgeBaseId, batchNo, "BATCH_REPORT", ReportStatusConstant.REPORT_FAILED,
                                   getCurrentUser(), currentTime, OPER_FAILED, error);
                    log.warn("知识库不存在 - 知识库ID: {}", knowledgeBaseId);
                    continue;
                }
                
                // 检查上报条件（格式、查重率、审核状态）
                String checkResult = checkReportConditions(knowledgeBase);
                if (checkResult != null) {
                    failedIds.add(knowledgeBaseId);
                    failureReasons.put(knowledgeBaseId, checkResult);
                    recordReportLog(knowledgeBaseId, batchNo, "BATCH_REPORT", ReportStatusConstant.REPORT_FAILED,
                                   getCurrentUser(), currentTime, OPER_FAILED, checkResult);
                    log.warn("知识库不满足上报条件 - 知识库ID: {}, 原因: {}", knowledgeBaseId, checkResult);
                    continue;
                }
                
                // 执行上报
                ReportDetailResult reportResult = performDetailedReport(knowledgeBase);
                
                if (reportResult.isSuccess()) {
                    successIds.add(knowledgeBaseId);
                    // 更新上报状态为已上报 (使用批量更新方法)
                    knowledgeBasesDMapper.batchUpdateReportStatus(Arrays.asList(knowledgeBaseId), STATUS_REPORTED,
                            currentTime, batchNo, "1", currentTime, "上报成功");
                    recordReportLog(knowledgeBaseId, batchNo, "BATCH_REPORT", STATUS_REPORTED, 
                                   getCurrentUser(), currentTime, OPER_SUCCESS, reportResult.getMessage());
                    log.info("知识库上报成功 - 知识库ID: {}, 消息: {}", knowledgeBaseId, reportResult.getMessage());
                } else {
                    failedIds.add(knowledgeBaseId);
                    failureReasons.put(knowledgeBaseId, reportResult.getErrorMessage());
                    // 更新上报状态为上报失败 (使用批量更新方法)
                    knowledgeBasesDMapper.batchUpdateReportStatus(Arrays.asList(knowledgeBaseId), ReportStatusConstant.REPORT_FAILED,
                            currentTime, batchNo, "0", currentTime, reportResult.getErrorMessage());
                    recordReportLog(knowledgeBaseId, batchNo, "BATCH_REPORT", ReportStatusConstant.REPORT_FAILED,
                                   getCurrentUser(), currentTime, OPER_FAILED, reportResult.getErrorMessage());
                    log.error("知识库上报失败 - 知识库ID: {}, 错误: {}", knowledgeBaseId, reportResult.getErrorMessage());
                }
                
            } catch (Exception e) {
                String error = "系统异常: " + e.getMessage();
                failedIds.add(knowledgeBaseId);
                failureReasons.put(knowledgeBaseId, error);
                recordReportLog(knowledgeBaseId, batchNo, "BATCH_REPORT", ReportStatusConstant.REPORT_FAILED,
                               getCurrentUser(), currentTime, OPER_FAILED, error);
                log.error("知识库上报系统异常 - 知识库ID: {}", knowledgeBaseId, e);
            }
        }
        
        result.setSuccessCount(successIds.size());
        result.setFailCount(failedIds.size());
        result.setSuccessIds(successIds);
        result.setFailedIds(failedIds);
        result.setFailureReasons(failureReasons);
        
        log.info("批量上报知识库完成 - 批次号: {}, 总数: {}, 成功: {}, 失败: {}", 
                batchNo, result.getTotalCount(), result.getSuccessCount(), result.getFailCount());
        
        return result;
    }
    
    /**
     * 单个知识库上报到集团
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 单个上报结果
     */
    @Transactional(rollbackFor = Exception.class)
    public SingleReportResult singleReportKnowledgeBase(Long knowledgeBaseId) {
        log.info("开始单个知识库上报 - 知识库ID: {}", knowledgeBaseId);
        
        SingleReportResult result = new SingleReportResult();
        result.setKnowledgeBaseId(knowledgeBaseId);
        result.setReportTime(new Date());
        
        try {
            // 查询知识库信息
            KnowledgeBasesEntity knowledgeBase = knowledgeBasesDMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null) {
                result.setSuccess(false);
                result.setMessage("知识库不存在");
                log.warn("知识库不存在 - 知识库ID: {}", knowledgeBaseId);
                return result;
            }
            
            // 检查上报条件
            String checkResult = checkReportConditions(knowledgeBase);
            if (checkResult != null) {
                result.setSuccess(false);
                result.setMessage(checkResult);
                log.warn("知识库不满足上报条件 - 知识库ID: {}, 原因: {}", knowledgeBaseId, checkResult);
                return result;
            }
            
            // 执行上报
            ReportDetailResult reportResult = performDetailedReport(knowledgeBase);
            
            if (reportResult.isSuccess()) {
                result.setSuccess(true);
                result.setMessage(reportResult.getMessage());
                // 更新上报状态 (使用批量更新方法)
                knowledgeBasesDMapper.batchUpdateReportStatus(Arrays.asList(knowledgeBaseId), STATUS_REPORTED,
                        result.getReportTime(), generateBatchNo(), "1", result.getReportTime(), "上报成功");
                log.info("知识库上报成功 - 知识库ID: {}, 消息: {}", knowledgeBaseId, reportResult.getMessage());
            } else {
                result.setSuccess(false);
                result.setMessage(reportResult.getErrorMessage());
                // 更新上报状态为失败 (使用批量更新方法)
                knowledgeBasesDMapper.batchUpdateReportStatus(Arrays.asList(knowledgeBaseId), ReportStatusConstant.REPORT_FAILED,
                        result.getReportTime(), generateBatchNo(), "0", result.getReportTime(), reportResult.getErrorMessage());
                log.error("知识库上报失败 - 知识库ID: {}, 错误: {}", knowledgeBaseId, reportResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("系统异常: " + e.getMessage());
            log.error("知识库上报系统异常 - 知识库ID: {}", knowledgeBaseId, e);
        }
        
        return result;
    }
    
    /**
     * 检查知识库是否满足上报条件
     * 
     * @param knowledgeBase 知识库实体
     * @return 不满足条件时返回原因，满足条件时返回null
     */
    private String checkReportConditions(KnowledgeBasesEntity knowledgeBase) {
        try {
            Long knowledgeBaseId = knowledgeBase.getKgKnowledgeBasesId();
            
            // 1. 检查审核状态是否通过 总体状态正常并且 审核状态通过
            String state = knowledgeBase.getState();
            if (!(CaseStateEnum.isTrueReportState(state) && knowledgeBase.getAuditStatus().equals(AuditEnum.PASS.getAuditStatus()))) { // 审核通过状态
                return "审核状态未通过，当前状态: " + state;
            }
            // 判断上报状态 已经上报的就不用了
            if(ReportStatusConstant.isReported(knowledgeBase.getReportStatus())){
                return "上报状态未通过，当前状态: " + knowledgeBase.getReportStatus();
            }

            
            // 2. 检查文档格式是否符合要求
            Set<String> supportedFormats = getSupportedFileFormats();
            String documentFormat = knowledgeBase.getDocumentFormat();
            if (StringUtils.isBlank(documentFormat) || !supportedFormats.contains(documentFormat.toLowerCase())) {
                return "文档格式不符合上报要求，当前格式: " + documentFormat + "，支持的格式: " + supportedFormats;
            }
            
            // 3. 检查查重率是否合格
            java.math.BigDecimal checkRepeatResult = knowledgeBase.getCheckRepeatResult();
            if (checkRepeatResult == null) {
                return "查重率为空，请等待查重完成";
            }
            
            // 查重率为-1表示正在查重中
            if (checkRepeatResult.compareTo(java.math.BigDecimal.valueOf(-1)) == 0) {
                return "正在查重中，请等待查重完成";
            }
            
            // 查重率不能超过0.9（90%）
            if (checkRepeatResult.compareTo(java.math.BigDecimal.valueOf(0.9)) > 0) {
                return "查重率超过限制，当前查重率: " + checkRepeatResult + "，限制: 0.9";
            }
            
            // 4. 检查是否已经上报过（避免重复上报）
            String reportStatus = knowledgeBase.getReportStatus();
            if (STATUS_REPORTED.equals(reportStatus) || STATUS_OFFLINE.equals(reportStatus) || STATUS_DELETED.equals(reportStatus)) {
                return "该知识库已经上报过，当前上报状态: " + reportStatus;
            }
            
            log.info("知识库满足上报条件 - 知识库ID: {}, 审核状态: {}, 文档格式: {}, 查重率: {}", 
                    knowledgeBaseId, state, documentFormat, checkRepeatResult);
            
            return null; // 所有条件都满足
            
        } catch (Exception e) {
            log.error("检查上报条件异常 - 知识库ID: {}", knowledgeBase.getKgKnowledgeBasesId(), e);
            return "检查上报条件异常: " + e.getMessage();
        }
    }

    /**
     * 批量上报结果类
     */
    public static class BatchReportResult {
        private String batchNo;
        private int totalCount;
        private int successCount;
        private int failCount;
        private List<Long> successIds = new ArrayList<>();
        private List<Long> failedIds = new ArrayList<>();
        private Map<Long, String> failureReasons = new HashMap<>();

        // Getters and Setters
        public String getBatchNo() { return batchNo; }
        public void setBatchNo(String batchNo) { this.batchNo = batchNo; }
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailCount() { return failCount; }
        public void setFailCount(int failCount) { this.failCount = failCount; }
        public List<Long> getSuccessIds() { return successIds; }
        public void setSuccessIds(List<Long> successIds) { this.successIds = successIds; }
        public List<Long> getFailedIds() { return failedIds; }
        public void setFailedIds(List<Long> failedIds) { this.failedIds = failedIds; }
        public Map<Long, String> getFailureReasons() { return failureReasons; }
        public void setFailureReasons(Map<Long, String> failureReasons) { this.failureReasons = failureReasons; }
    }

    /**
     * 单个上报结果类
     */
    public static class SingleReportResult {
        private Long knowledgeBaseId;
        private boolean success;
        private String message;
        private Date reportTime;

        // Getters and Setters
        public Long getKnowledgeBaseId() { return knowledgeBaseId; }
        public void setKnowledgeBaseId(Long knowledgeBaseId) { this.knowledgeBaseId = knowledgeBaseId; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Date getReportTime() { return reportTime; }
        public void setReportTime(Date reportTime) { this.reportTime = reportTime; }
    }
} 