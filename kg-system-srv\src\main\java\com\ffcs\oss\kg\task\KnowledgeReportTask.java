package com.ffcs.oss.kg.task;

import com.ffcs.oss.kg.config.properties.ReportConfig;
import com.ffcs.oss.kg.service.KnowledgeReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库集团上报定时任务
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/api/reportTask")
@RequiredArgsConstructor
@EnableScheduling
//@ConditionalOnProperty(name = "knowledge.report.task.enabled", havingValue = "true")
public class KnowledgeReportTask {

    private final KnowledgeReportService knowledgeReportService;

    private final ReportConfig reportConfig;


    /**
     * todo 定时执行 只需要sftp的
     * 定时执行知识库集团上报任务
     * 使用配置的cron表达式执行
     */
    @Scheduled(cron = "${knowledge.report.task.cron:0 0/1 * * * ?}")
    @PostMapping("/executeTask")
    public void executeReportTask() {
        if (!reportConfig.getEnabled()) {
            log.info("知识库集团上报任务已关闭");
            return;
        }
        log.info("开始执行定时知识库集团上报任务，最大处理数量：{}", reportConfig.getMaxCount());

        try {
            KnowledgeReportService.ReportResult result = knowledgeReportService.executeKnowledgeReportTask(reportConfig.getMaxCount());

            log.info("定时知识库集团上报任务执行完成 - 批次号: {}, 总数: {}, 成功: {}, 失败: {}",
                    result.getBatchNo(), result.getTotal(), result.getSuccessCount(), result.getFailedCount());

        } catch (Exception e) {
            log.error("定时知识库集团上报任务执行异常", e);
        }
    }
} 