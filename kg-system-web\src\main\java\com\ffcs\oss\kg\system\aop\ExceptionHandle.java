package com.ffcs.oss.kg.system.aop;


import com.ffcs.oss.kg.common.core.exception.KnowledgeBasesException;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

@RestControllerAdvice
public class ExceptionHandle {

    private final static Logger logger = LoggerFactory.getLogger(ExceptionHandle.class);

    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ServiceResp<Void> exceptionHandler(MethodArgumentNotValidException e) {
        logger.error(e.getMessage(), e);
        // todo 不用返回太详细的信息 先用errormessage
        String errorMessage = e.getBindingResult().getFieldError().getDefaultMessage();
        return ServiceResp.fail(errorMessage);
    }

    @ResponseBody
    @ExceptionHandler(KnowledgeGraphException.class)
    public ServiceResp<Void> exceptionHandler(KnowledgeGraphException e) {
        logger.error(e.getMessage(), e);
        return ServiceResp.fail(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(KnowledgeBasesException.class)
    public ServiceResp<Void> exceptionHandler(KnowledgeBasesException e) {
        logger.error(e.getMessage(), e);
        return ServiceResp.fail(e.getMessage());
    }


    @ResponseBody
    @ExceptionHandler(NoHandlerFoundException.class)
    public ServiceResp<Void> handlerNoFoundException(NoHandlerFoundException e) {
        logger.error(e.getMessage(), e);
        return ServiceResp.fail("路径不存在，请检查路径是否正确");
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public ServiceResp<Void> handleException(Exception e) {
        logger.error(e.getMessage(), e);
        return ServiceResp.fail("系统繁忙,请稍后再试");
    }
}
