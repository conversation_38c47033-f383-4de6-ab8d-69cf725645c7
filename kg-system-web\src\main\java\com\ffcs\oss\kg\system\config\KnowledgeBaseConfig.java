package com.ffcs.oss.kg.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 知识库相关配置统一管理
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "kbs")
@RefreshScope
public class KnowledgeBaseConfig {

    /**
     * 基础信息
     */
    private BaseInfo baseInfo = new BaseInfo();



    /**
     * 即时通相关配置
     */
    private JtConfig jt = new JtConfig();
    
    /**
     * RAG Web相关配置
     */
    private RagWebConfig ragweb = new RagWebConfig();
    
    /**
     * RAG Flow相关配置
     */
    private RagFlowConfig ragflow = new RagFlowConfig();

    
    /**
     * 企微文档相关配置
     */
    private QwdConfig qwd = new QwdConfig();

    /**
     * 问答思维链的配置
     */
    private QaCotConfig qaCot = new QaCotConfig();
    
    /**
     * 相似度检查配置
     */
    private SimilarityConfig similarity = new SimilarityConfig();

    @Data
    public static class BaseInfo {

        private String indexName;

    }
    
    /**
     * 即时通相关配置
     */
    @Data
    public static class JtConfig {
        /**
         * 即时通作者
         */
        private String jtAuthor;
        
        /**
         * 获取3C树URL
         */
        private String get3CTreeUrl;
        
        /**
         * 更新文档URL
         */
        private String updDocUrl;
        
        /**
         * 基础文件路径
         */
        private String baseFilePath;
        
        /**
         * 批量保存文件URL
         */
        private String batchSaveFilesUrl;
        
        /**
         * 区域代码
         */
        private String regionCode;

        /**
         * eop文件路径
         */
        private String eopFilePath;
    }
    
    /**
     * RAG Web相关配置
     */
    @Data
    public static class RagWebConfig {
        /**
         * RAG Web URL
         */
        private String url;
        
        /**
         * 是否启用RAG Web
         */
        private boolean enabled = false;
    }
    
    /**
     * RAG Flow相关配置
     */
    @Data
    public static class RagFlowConfig {
        /**
         * 是否启用RAG Flow
         */
        private boolean enabled = false;
        
        /**
         * API配置
         */
        private ApiConfig api = new ApiConfig();
        
        /**
         * API配置
         */
        @Data
        public static class ApiConfig {
            /**
             * API URL
             */
            private String url;
            
            /**
             * API Key
             */
            private String key;
        }
    }
    
    /**
     * 企微文档相关配置
     */
    @Data
    public static class QwdConfig {
        /**
         * 分类ID
         */
        private String categoryId;

        /**
         * 默认的知识分类
         */
        private String questionClassifyId = "11641184";

        /**
         * 默认的生命周期
         */
        private String lifeCycleId = "11638661";
    }

    /**
     * 问答思维链
     */
    @Data
    public static class QaCotConfig {
        /**
         * 分类ID
         */
        private String categoryId = "73";
    }
    
    /**
     * 相似度检查配置
     */
    @Data
    public static class SimilarityConfig {
        /**
         * 是否启用相似度检查
         */
        private boolean check = false;
    }
} 