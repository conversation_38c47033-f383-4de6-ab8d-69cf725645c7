package com.ffcs.oss.kg.system.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.model.evt.common.IdRequest;
import com.ffcs.oss.kg.data.model.evt.hidedanger.SaveKgBasesAuditPersonDEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.*;
import com.ffcs.oss.kg.data.rd.entity.DocumentEntity;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.*;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.SpacePageInfoEvt;
import com.ffcs.oss.kg.system.annotation.*;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.evt.knowledgeBases.*;
import com.ffcs.oss.kg.system.evt.rawCase.*;
import com.ffcs.oss.kg.system.service.knowledgeBase.KnowledgeBasesService;
import com.ffcs.oss.kg.system.vm.dictionary.DictionaryTreeVm;
import com.ffcs.oss.kg.system.vm.dictionary.DictionaryVm;
import com.ffcs.oss.kg.system.vm.knowledgeBases.KnowledgeBasesInfoVm;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Api(tags = "知识库")
@RestController
@Slf4j
@RequestMapping("api/knowledgeBases")
public class KnowledgeBasesController {

    @Autowired
    private KnowledgeBasesService knowledgeBasesService;

    @ApiOperation("查询知识库")
    @PostMapping("/getKnowledgeBases")
    @DataPermission(DataPermissionConstant.BASES)
    public ServiceResp getKnowledgeBases(@RequestBody GetBasesEvt evt) {
        return ServiceResp.success(knowledgeBasesService.getKnowledgeBases(evt));
    }

    @ApiOperation("添加知识文档")
    @PostMapping("/addKnowledgeFile")
    public ServiceResp<Boolean> addKnowledgeFile(HttpServletRequest request, @RequestParam(value = "multipartFiles") MultipartFile[] multipartFiles) throws IOException, KnowledgeGraphException {
        boolean flag = knowledgeBasesService.addKnowledgeFile(request, multipartFiles);
        return ServiceResp.success(flag);
    }

    @ApiOperation("删除知识文档")
    @PostMapping("/deleteFileDocument")
    public ServiceResp<Boolean> deleteFileDocument(@RequestBody DeletDocEvt evt) {
        boolean flag = knowledgeBasesService.deleteFileDocument(evt);
        return ServiceResp.success(flag);
    }

    @ApiOperation("下载知识文档")
    @PostMapping("/downLoadFileDocument")
    public ServiceResp<Boolean> downLoadFileDocument(HttpServletResponse response, @RequestBody DownLoadDocEvt evt) throws IOException {
        boolean flag = knowledgeBasesService.downLoadFileDocument(response, evt);
        return ServiceResp.success(flag);
    }

    @ApiOperation("增加知识/修改知识")
    @PostMapping("/addOrUpdateKnowledgeBases")
    public ServiceResp<KnowledgeBasesEntity> addOrUpdateKnowledgeBases(@RequestBody InsertKnowledgeBasesEvt insertKnowledgeBasesEvt) throws Exception {
        KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesService.addOrUpdateKnowledgeBases(insertKnowledgeBasesEvt);
        return ServiceResp.success(knowledgeBasesEntity);
    }

    @ApiOperation("批量导入知识")
    @PostMapping("/batchAddKnowledgeBases")
    public ServiceResp<List<Map<String, Object>>> batchAddKnowledgeBases(@RequestBody List<InsertKnowledgeBasesEvt> insertKnowledgeBasesEvts) throws Exception {
        List<Map<String, Object>> knowledgeBasesEntitys = knowledgeBasesService.batchAddKnowledgeBases(insertKnowledgeBasesEvts);
        return ServiceResp.success(knowledgeBasesEntitys);
    }

    @ApiOperation("查看知识详情")
    @PostMapping("/getKnowledgeBasesInfo")
    public ServiceResp<KnowledgeBasesInfoVm> getKnowledgeBasesInfo(HttpServletRequest request, @RequestBody InsertKnowledgeBasesEvt evt) throws JsonProcessingException, KnowledgeGraphException {
        KnowledgeBasesInfoVm knowledgeBasesInfoVm = knowledgeBasesService.getKnowledgeBasesInfo(request, evt);
        return ServiceResp.success(knowledgeBasesInfoVm);
    }

    @ApiOperation("根据人员查询知识")
    @PostMapping("/getBasesByAuthor")
    public ServiceResp<PageInfo<List<KnowledgeBasesEntity>>> getBasesByAuthor(@RequestBody AuthorEvt evt) {
        PageInfo<List<KnowledgeBasesEntity>> pageInfo = knowledgeBasesService.getBasesByAuthor(evt);
        return ServiceResp.success(pageInfo);
    }

    @ApiOperation("批量删除知识")
    @PostMapping("/deleteKgbsByIds")
    public ServiceResp<Boolean> deleteKgbsByIds(@RequestBody KgBsIdsEvt evt) throws KnowledgeGraphException {
        boolean flag = knowledgeBasesService.deleteKgbsByIds(evt);
        return ServiceResp.success(flag);
    }

    @ApiOperation("查询文件信息")
    @PostMapping("/getDocumentsByKgBasesId")
    public ServiceResp<List<DocumentEntity>> getDocumentsByKgBasesId(@RequestBody GetBasesDocumentsEvt evt) {
        List<DocumentEntity> documents = knowledgeBasesService.getDocumentsByKgBaseId(evt);
        return ServiceResp.success(documents);
    }

    @ApiOperation("查看背景图片及功能概述")
    @PostMapping("/searchKnowledgeBackGroundAndInfo")
    public ServiceResp searchKnowledgeBackGroundAndInfo() {
        return knowledgeBasesService.searchKnowledgeBackGroundAndInfo();
    }

    @ApiOperation("查看应用成效")
    @PostMapping("/searchKnowledgeApplicationEffective")
    public ServiceResp searchKnowledgeApplicationEffective() {
        return knowledgeBasesService.searchKnowledgeApplicationEffective();
    }

    @ApiOperation("查看本地网排名及分布")
    @PostMapping("/queryLocalNetworkRank")
    public ServiceResp queryLocalNetworkRank() {
        return knowledgeBasesService.queryLocalNetworkRank();
    }

    @ApiOperation("查看专业排名及分布")
    @PostMapping("/queryMajorRank")
    public ServiceResp queryMajorRank() {
        return knowledgeBasesService.queryMajorRank();
    }

    @ApiOperation("置顶知识")
    @PostMapping("/topUpBases")
    public ServiceResp topUpBases(@RequestBody List<KnowledgeBasesEntity> evt) {
        return knowledgeBasesService.topUpBases(evt);
    }

    @ApiOperation("取消置顶知识")
    @PostMapping("/cancelTopUpBases")
    public ServiceResp cancelTopUpBases(@RequestBody List<KnowledgeBasesEntity> evt) {
        return knowledgeBasesService.cancelTopUpBases(evt);
    }

    @ApiOperation("知识模板下载")
    @PostMapping("/downLoadBaseManualExcel")
    public void downLoadBaseManualExcel(HttpServletResponse response) throws IOException {
        knowledgeBasesService.downLoadBaseManualExcel(response);
    }

    @ApiOperation("知识模板下载")
    @PostMapping("/downLoadBasesManualDocx")
    public void downLoadBasesManualDocx(HttpServletResponse response) throws IOException {
        knowledgeBasesService.downLoadBasesManualDocx(response);
    }

    @ApiOperation("全文检索")
    @PostMapping("/getBasesOnFullText")
    @DataPermission("知识库")
    public ServiceResp<PageInfo<KnowledgeBasesEntity>> getBasesOnFullText(@RequestBody GetBasesEvt evt) {
        PageInfo<KnowledgeBasesEntity> pageInfo = knowledgeBasesService.getBasesOnFullText(evt);
        return ServiceResp.success(pageInfo);
    }

    @ApiOperation("查看知识详情图片")
    @PostMapping("/getBasesImageInfo")
    public ServiceResp<List<String>> getBasesImageInfo(@RequestBody InsertKnowledgeBasesEvt evt) throws IOException {
        List<String> basesImageInfoList = knowledgeBasesService.getBasesImageInfo(evt);
        return ServiceResp.success(basesImageInfoList);
    }

    @ApiOperation("插入知识详情图片")
    @PostMapping("/insertBasesImages")
    public ServiceResp<String> insertBasesImages(@RequestParam(value = "file") MultipartFile file) throws IOException, KnowledgeGraphException {
        String filePath = knowledgeBasesService.insertBasesImages(file);
        return ServiceResp.success(filePath);
    }

    @ApiOperation("删除附件")
    @PostMapping("/deleteDoc")
    public ServiceResp<Boolean> deleteDoc(@RequestBody DocEvt evt) {
        boolean flag = knowledgeBasesService.deleteDoc(evt);
        return ServiceResp.success(flag);
    }

    @ApiOperation("知识预览")
    @PostMapping("/lookForBases")
    public ServiceResp<String> lookForBases(@RequestBody LookForBasesEvt evt) throws Exception {
        String path = knowledgeBasesService.lookForBases(evt);
        return ServiceResp.success(path);
    }

    @ApiOperation("批量上线")
    @PostMapping("/submitKnowledgeBases")
    public ServiceResp submitKnowledgeBases(@RequestBody BatchUpdateBasesStateEvt evt) throws Exception {
        if (CollectionUtil.isEmpty(evt.getKgKnowledgeBasesIds()) && StringUtils.isNotBlank(evt.getState())) {
            return ServiceResp.fail("需要修改的数组和状态不能为空");
        }
        return knowledgeBasesService.submitKnowledgeBases(evt);
    }

    @ApiOperation("批量上报")
    @PostMapping("/reportKnowledgeBases")
    public ServiceResp reportKnowledgeBases(@RequestBody BatchUpdateBasesStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getKgKnowledgeBasesIds()) && StringUtils.isNotBlank(evt.getState())) {
            return ServiceResp.fail("需要修改的数组和状态不能为空");
        }
        return knowledgeBasesService.reportKnowledgeBases(evt);
    }

    @ApiOperation("批量下线")
    @PostMapping("/offerLineKnowledgeBases")
    public ServiceResp offerLineKnowledgeBases(@RequestBody BatchUpdateBasesStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getKgKnowledgeBasesIds()) && StringUtils.isNotBlank(evt.getState())) {
            return ServiceResp.fail("需要修改的数组和状态不能为空");
        }
        return knowledgeBasesService.offerLineKnowledgeBases(evt);
    }

    @ApiOperation("知识库导出word或pdf")
    @GetMapping("/exportBasesWordOrPdf")
    public void exportBasesWordOrPdf(@RequestParam Long kgBasesId, @RequestParam String fileType, HttpServletResponse response) throws IOException {
        knowledgeBasesService.exportBasesWordOrPdf(kgBasesId, fileType, response);
    }

    @ApiOperation("通用下载文件接口")
    @PostMapping("/commonDownLoadFile")
    public boolean commonDownLoadFile(HttpServletResponse response, @RequestBody CommonDownloadFileEvt evt) throws IOException {
        return knowledgeBasesService.commonDownLoadFile(response, evt);
    }

    @ApiOperation("知识库新增或修改审核配置")
    @PostMapping("/addOrUpdateBasesAuditDimensionConfig")
    public ServiceResp addOrUpdateBasesAuditDimensionConfig(@RequestBody List<KgBasesAuditDimensionConfigD> evt) throws IOException {
        return knowledgeBasesService.addOrUpdateBasesAuditDimensionConfig(evt);
    }

    @ApiOperation("查询知识库审核配置")
    @PostMapping("/getBasesAuditDimensionConfig")
    public ServiceResp getBasesAuditDimensionConfig() throws IOException {
        return knowledgeBasesService.getBasesAuditDimensionConfig();
    }

    @ApiOperation("删除知识库审核配置")
    @PostMapping("/deleteBasesAuditDimensionConfig")
    public ServiceResp deleteBasesAuditDimensionConfig(@RequestBody KgBasesAuditDimensionConfigD evt) throws IOException {
        if (evt.getKgBasesAuditDimensionId() == null) {
            return ServiceResp.fail("待删除的知识库审核配置id不能为空");
        }
        return knowledgeBasesService.deleteBasesAuditDimensionConfig(evt);
    }

    /**
     * 实际审核通过 和 审核不通过的接口
     * @param evt
     * @return
     */
    @ApiOperation("填写或修改评审结论")
    @PostMapping("/addOrUpdateBasesReviewConclusion")
    public ServiceResp addOrUpdateBasesReviewConclusion(@RequestBody BasesReviewConclusionEvt evt) {
        return knowledgeBasesService.addOrUpdateBasesReviewConclusion(evt);
    }

    @ApiOperation("查看评审结论")
    @PostMapping("/getBasesReviewConclusion")
    public ServiceResp getBasesReviewConclusion(@RequestBody BasesReviewConclusionEvt evt) {
        if (evt.getKgKnowledgeBasesId() == null) {
            return ServiceResp.fail("知识id不能为空");
        }
        return knowledgeBasesService.getBasesReviewConclusion(evt);
    }

    @ApiOperation("新增或修改知识库审核人")
    @PostMapping("/addOrUpdateBasesAuditPerson")
    public ServiceResp addOrUpdateBasesAuditPerson(@RequestBody SaveKgBasesAuditPersonDEvt evt) {
        if (evt == null) {
            return ServiceResp.fail("知识库审核人不能为空");
        }
        return knowledgeBasesService.addOrUpdateBasesAuditPerson(evt);
    }

    @ApiOperation("查询知识库审核人")
    @PostMapping("/getBasesAuditPerson")
    public ServiceResp getBasesAuditPerson(@RequestBody KgBasesAuditPersonDEvt evt) {
        evt.setCountTotal(true);
        return ServiceResp.success("操作成功", knowledgeBasesService.getBasesAuditPerson(evt));
    }

    @ApiOperation("根据名称查询知识库审核人")
    @PostMapping("/getBasesAuditPersonByName")
    public ServiceResp getBasesAuditPersonByName(@RequestBody KgBasesAuditPersonDEvt evt) {
        return ServiceResp.success("操作成功", knowledgeBasesService.getBasesAuditPersonByName(evt));
    }

    @ApiOperation("批量删除知识库审核人")
    @PostMapping("/batchDeleteBasesAuditPerson")
    public ServiceResp batchDeleteBasesAuditPerson(@RequestBody List<KgBasesAuditPersonD> evt) {
        return knowledgeBasesService.batchDeleteBasesAuditPerson(evt);
    }

    @ApiOperation("批量设置为区域管理员")
    @PostMapping("/batchSetRegionAdmin")
    public ServiceResp batchSetRegionAdmin(@RequestBody List<KgBasesAuditPersonD> evt) {
        return knowledgeBasesService.batchSetRegionAdmin(evt);
    }

    @ApiOperation("批量取消区域管理员")
    @PostMapping("/batchCancelRegionAdmin")
    public ServiceResp batchCancelRegionAdmin(@RequestBody List<KgBasesAuditPersonD> evt) {
        return knowledgeBasesService.batchCancelRegionAdmin(evt);
    }

    @ApiOperation("修改审核人分配规则配置")
    @PostMapping("/updateAuditorAllocationRuleConfig")
    public ServiceResp updateAuditorAllocationRuleConfig(@RequestBody KgAuditorAllocationRuleConfigD evt) {
        return knowledgeBasesService.updateAuditorAllocationRuleConfig(evt);
    }

    @ApiOperation("获取待审核数和历史审核数")
    @PostMapping("/getWaitingAuditAndHistoryAuditNums")
    public ServiceResp getWaitingAuditAndHistoryAuditNums(@RequestBody KnowledgeBasesEntity evt) {
        return knowledgeBasesService.getWaitingAuditAndHistoryAuditNums(evt);
    }

    @ApiOperation("获取审核人分配规则配置")
    @PostMapping("/getAuditorAllocationRuleConfig")
    public ServiceResp getAuditorAllocationRuleConfig() {
        return knowledgeBasesService.getAuditorAllocationRuleConfig();
    }

    @ApiOperation("查询知识库审核人手机号码")
    @PostMapping("/getBasesAuditPersonTelPhone")
    public ServiceResp getBasesAuditPersonTelPhone(@RequestBody KgBasesAuditPersonD evt) {
        return knowledgeBasesService.getBasesAuditPersonTelPhone(evt);
    }

    @ApiOperation("查询该知识相关的TOP5")
    @PostMapping("/getBasesRelatedTopFive")
    public ServiceResp getBasesRelatedTopFive(@RequestBody KnowledgeBasesEntity evt) {
        return knowledgeBasesService.getBasesRelatedTopFive(evt);
    }

    @ApiOperation("根据codeType获取对应的字典列表")
    @PostMapping("/getAllBasesDictionary")
    public ServiceResp<Map<String, List<DictionaryVm>>> getAllBasesDictionary(@RequestBody List<String> codeTypeList) {
        return ServiceResp.success(knowledgeBasesService.getAllBasesDictionary(codeTypeList));
    }

    @ApiOperation("知识库解析")
    @PostMapping("/basesAnalysis")
    public ServiceResp<KnowledgeBasesEntity> importOperationsManualExcel(@RequestParam("file") MultipartFile file) throws Exception {
        KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesService.importBasesManualExcel(file);
        return ServiceResp.success(knowledgeBasesEntity);
    }

    @ApiOperation("批量知识库上传")
    @PostMapping("/batchUploadBases")
    public ServiceResp<List<KnowledgeBasesEntity>> batchUploadBases(@RequestParam("file") MultipartFile[] files) throws Exception {
        List<KnowledgeBasesEntity> knowledgeBasesEntitys = knowledgeBasesService.batchUploadBases(files);
        return ServiceResp.success(knowledgeBasesEntitys);
    }

    @PostMapping("/getAvailableBasesSpaces")
    @ApiOperation("获取可选知识空间列表")
    public ServiceResp<PageInfo<KgBasesSpaceC>> getAvailableBasesSpaces(@RequestBody SpacePageInfoEvt evt) {
        try {
            return ServiceResp.success(knowledgeBasesService.getAvailableBasesSpaces(evt));
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @PostMapping("/getAllBasesSpaces")
    @ApiOperation("获取可选知识空间列表")
    public ServiceResp<List<KgBasesSpaceC>> getAllBasesSpaces() {
        try {
            return ServiceResp.success(knowledgeBasesService.getAllBasesSpaces());
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }



    @ApiOperation("插入知识空间图片")
    @PostMapping("/insertSpacesImages")
    public ServiceResp<String> insertSpacesImages(@RequestParam(value = "file") MultipartFile file) throws IOException, KnowledgeGraphException {
        String filePath = knowledgeBasesService.insertBasesImages(file);
        return ServiceResp.success(filePath);
    }

    @ApiOperation("上传文件到rag")
    @PostMapping("/uploadFileToRag")
    public ServiceResp<UploadApiResponse> uploadFileToRag(@RequestParam("filePath") String filePath, @RequestParam("ragFlowId") String ragFlowId) {
        UploadApiResponse response = knowledgeBasesService.uploadFileToRag(filePath, ragFlowId);
        return ServiceResp.success(response);
    }





    @PostMapping("/detail")
    @ApiOperation("获取知识空间详情")
    public ServiceResp getBasesSpaceDetail(@RequestBody IdRequest request) {
        try {
            return ServiceResp.success(knowledgeBasesService.getBasesSpaceById(request.getId()));
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @PostMapping("/getAllBasesTreeDictionary")
    @ApiOperation("获取树形字典值")
    public ServiceResp<Map<String, List<DictionaryTreeVm>>> getAllBasesTreeDictionary(@RequestBody List<String> codeTypeList) {
        return ServiceResp.success(knowledgeBasesService.getAllBasesTreeDictionary(codeTypeList));
    }

    @PostMapping("/getLabels")
    @ApiOperation("获取所有标签")
    public ServiceResp<List<KgBasesSpaceLabelD>> getLabels() {
        try {
            return ServiceResp.success(knowledgeBasesService.getLabels());
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @PostMapping("/createLabel")
    @ApiOperation("创建知识标签")
    public ServiceResp<String> createLabel(@Valid @RequestBody KgBasesSpaceLabelD evt) {
        try {
            knowledgeBasesService.createLabel(evt);
            return ServiceResp.success("新增成功");
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @PostMapping("/updateLabel")
    @ApiOperation("更新知识标签")
    public ServiceResp<String> updateLabel(@Valid @RequestBody KgBasesSpaceLabelD evt) {
        try {
            knowledgeBasesService.updateLabel(evt);
            return ServiceResp.success("修改成功");
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @PostMapping("/deleteLabels")
    @ApiOperation("删除知识标签")
    public ServiceResp<String> deleteLabels(@RequestBody IdRequest request) {
        try {
            knowledgeBasesService.deleteLabels(request.getIds());
            return ServiceResp.success("删除成功");
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @PostMapping("/getSpacesNums")
    @ApiOperation("获取知识文件数量")
    public ServiceResp<List<Map<String, String>>> getSpacesNums() {
        try {
            List<Map<String, String>> nums = knowledgeBasesService.getSpacesNums();
            return ServiceResp.success(nums);
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        }
    }

    @ApiOperation("查询知识空间")
    @PostMapping("/getKnowledgeBasess")
    @DataPermission(DataPermissionConstant.BASES)
    public ServiceResp getKnowledgeBasess(@RequestBody GetBasesEvt evt) {
        if (evt.isFromStaff() && CollectionUtils.isEmpty(evt.getMtPlatStaffId()) && Objects.nonNull(evt.getStaffType()) && evt.getStaffType() == 0) {
            return ServiceResp.success(new PageInfo<>(Collections.emptyList()));
        }
        return ServiceResp.success(knowledgeBasesService.getKnowledgeBasess(evt));
    }

    @ApiOperation("知识查重")
    @PostMapping("/basesCheckRepeat")
    public ServiceResp<String> basesCheckRepeat(@RequestBody BasesCheckRepeatEvt evt) {
        String r = knowledgeBasesService.basesCheckRepeat(evt);
        return ServiceResp.success("查重成功", r);
    }

    @ApiOperation("文件查重并存储相似文件")
    @PostMapping("/checkFileRepeat")
    public ServiceResp<String> checkFileRepeat(@RequestBody KgKnowledgeBasesIdEvt evt) {

        knowledgeBasesService.checkFileRepeatAndSaveToRedis(evt.getKgKnowledgeBasesId(), true);
        return ServiceResp.success("查重完成");
    }

    @ApiOperation("查询文件查重相似度列表")
    @PostMapping("/getFileSimilarityList")
    public ServiceResp<List<SimilarFileInfo>> getFileSimilarityList(@RequestBody KgKnowledgeBasesIdEvt evt) {
        List<SimilarFileInfo> similarList = knowledgeBasesService.getFileSimilarityListFromRedis(evt.getKgKnowledgeBasesId());
        return ServiceResp.success(similarList);
    }

    @ApiOperation(value = "下载问答对Excel模板", notes = "用于下载问答对系统的Excel导入模板")
    @PostMapping("/downloadTemplate")
    public HttpServletResponse downLoadOperationsManualExcel(HttpServletResponse response) {
        Resource resource = new ClassPathResource("excel/template/问答对通用模板.xlsx");
        if (resource.exists()) {
            try {
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/vnd.ms-excel");
                String fileName = resource.getFilename();
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + java.net.URLEncoder.encode(fileName, "UTF-8"));
                response.flushBuffer();
                BufferedInputStream bis = new BufferedInputStream(resource.getInputStream());
                OutputStream os = response.getOutputStream();
                byte[] buffer = new byte[1024];
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
                if (bis != null) {
                    bis.close();
                }
                os.close(); // 关闭输出流
                return response;
            } catch (Exception e) {
                log.error("导出问答对通用模板文件异常", e);
            }
        }
        return null;
    }

    @ApiOperation(value = "下载模板导入模板")
    @GetMapping("/downLoadQATemplate")
    public HttpServletResponse downLoadQATemplate(HttpServletResponse response) {
        return knowledgeBasesService.downLoadCommonFile(response,"excel/template/问答对通用模板.xlsx","问答对通用模板");
    }

    @ApiOperation("问答对导入到知识库")
    @PostMapping("/importqwkbs")
    public ServiceResp<KnowledgeBasesEntity> importqwkbs(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException {
        try {
            KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesService.importqwkbs(request,file);
            if (knowledgeBasesEntity != null) {
                return ServiceResp.success(knowledgeBasesEntity);
            } else {
                return ServiceResp.fail("问答对导入失败，请检查数据是否符合要求");
            }
        } catch (Exception e) {
            return ServiceResp.fail("问答对导入异常: " + e.getMessage());
        }
    }

    @ApiOperation("问答对导入到知识库")
    @PostMapping("/importqwkbss")
    public ServiceResp<KnowledgeBasesEntity> importqwkbss(@RequestBody InsertKnowledgeBasesEvt insertKnowledgeBasesEvt) throws IOException {
        try {
            KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesService.importqwkbss(insertKnowledgeBasesEvt);
            if (knowledgeBasesEntity != null) {
                return ServiceResp.success(knowledgeBasesEntity.getImportError(), knowledgeBasesEntity);
            } else {
                return ServiceResp.fail("问答对导入失败，请检查数据是否符合要求");
            }
        } catch (Exception e) {
            return ServiceResp.fail("问答对导入异常: " + e.getMessage());
        }
    }
    @ApiOperation("下载并解析知识文档内容")
    @PostMapping("/analyzeFileContent")
    public ServiceResp<String> analyzeFileContent(@RequestBody DownLoadDocEvt evt) {
        try {
            String content = knowledgeBasesService.downloadAndAnalyzeFileContent(evt);
            return ServiceResp.success(content);
        } catch (KnowledgeGraphException e) {
            return ServiceResp.fail(e.getMessage());
        } catch (Exception e) {
            return ServiceResp.fail("系统异常: " + e.getMessage());
        }
    }

    /**
     * 修改知识库名称
     *
     * @param evt 修改知识库名称请求参数
     * @return 操作结果
     */
    @PostMapping("/updateName")
    @ApiOperation("修改知识库名称")
    public ServiceResp updateKnowledgeBaseName(@RequestBody @Valid UpdateKnowledgeBaseNameEvt evt) {
        log.info("修改知识库名称，参数：{}", evt);
        return knowledgeBasesService.updateKnowledgeBaseName(evt);
    }
}
