package com.ffcs.oss.kg.system.controller;

import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeBasesException;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.common.core.util.StringUtil;
import com.ffcs.oss.kg.data.enums.CaseStateEnum;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.KgBasesAuditPersonDEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.KnowledgeBasesVm;
import com.ffcs.oss.kg.data.model.vm.bases.KgBasesAuditPersonDVm;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.ProfessionalTermEntity;
import com.ffcs.oss.kg.system.annotation.DataPermission;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.evt.professionalTerm.*;
import com.ffcs.oss.kg.system.service.professionalTerm.ProfessionalTermService;
import com.ffcs.oss.kg.system.vm.professionalTerm.ImportResultVm;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.ffcs.oss.kg.data.model.evt.knowledgeBases.BasesReviewConclusionEvt;
import com.ffcs.oss.kg.data.model.evt.professionalTerm.ProfessionalTermReviewConclusionEvt;

/**
 * 专业词汇管理控制器
 */
@RestController
@RequestMapping("/professional-term")
@Api(tags = "专业词汇管理")
@Slf4j
public class ProfessionalTermController {

    @Autowired
    private ProfessionalTermService professionalTermService;

    /**
     * 分页查询专业词汇
     *
     * @param evt 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation("分页查询专业词汇")
    @DataPermission(DataPermissionConstant.PROFESSIONAL_TERM)
    public ServiceResp<PageInfo<ProfessionalTermEntity>> page(@RequestBody ProfessionalTermQueryEvt evt) {
        try {
            PageInfo<ProfessionalTermEntity> page = professionalTermService.page(evt);
            return ServiceResp.success(page);
        } catch (Exception e) {
            log.error("分页查询专业词汇失败", e);
            return ServiceResp.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 全文检索专业词汇
     *
     * @param evt 查询条件
     * @return 分页结果
     */
    @PostMapping("/search")
    @ApiOperation("全文检索专业词汇")
    public ServiceResp<PageInfo<ProfessionalTermEntity>> search(@RequestBody ProfessionalTermQueryEvt evt) {
        try {
            if (StringUtils.isBlank(evt.getKeyword())) {
                return ServiceResp.fail("请输入检索关键词");
            }
            PageInfo<ProfessionalTermEntity> page = professionalTermService.fullTextSearch(evt);
            return ServiceResp.success(page);
        } catch (Exception e) {
            log.error("全文检索专业词汇失败", e);
            return ServiceResp.fail("检索失败：" + e.getMessage());
        }
    }

    /**
     * 查询专业词汇详情
     *
     * @param id 专业词汇ID
     * @return 专业词汇详情
     */
    @GetMapping("/{id}")
    @ApiOperation("查询专业词汇详情")
    public ServiceResp<ProfessionalTermEntity> detail(@PathVariable Long id) {
        try {
            ProfessionalTermEntity term = professionalTermService.getById(id);
            if (term == null) {
                return ServiceResp.fail("专业词汇不存在");
            }
            return ServiceResp.success(term);
        } catch (Exception e) {
            log.error("查询专业词汇详情失败", e);
            return ServiceResp.fail("查询详情失败：" + e.getMessage());
        }
    }

    /**
     * 保存或更新专业词汇
     *
     * @param evt 专业词汇数据
     * @return 结果
     */
    @PostMapping("/save")
    @ApiOperation("保存或更新专业词汇")
    public ServiceResp<Boolean> save(@Valid @RequestBody ProfessionalTermEvt evt) {
        try {
            boolean result = professionalTermService.saveOrUpdate(evt);
            return ServiceResp.success(result);
        } catch (KnowledgeGraphException e) {
            log.error("保存专业词汇失败", e);
            return ServiceResp.fail(e.getMessage());
        }
    }

    /**
     * 批量删除专业词汇
     *
     * @param evt 删除参数
     * @return 结果
     */
    @PostMapping("/delete")
    @ApiOperation("批量删除专业词汇")
    public ServiceResp<Boolean> delete(@RequestBody ProfessionalTermDeleteEvt evt) {
        try {
            boolean result = professionalTermService.batchDelete(evt);
            return ServiceResp.success(result);
        } catch (KnowledgeGraphException e) {
            log.error("删除专业词汇失败", e);
            return ServiceResp.fail(e.getMessage());
        }
    }

    /**
     * 导入专业词汇
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    @ApiOperation("导入专业词汇")
    public ServiceResp<ImportResultVm> importTerms(@RequestParam("file") MultipartFile file) {
        try {
            ImportResultVm result = professionalTermService.importTerms(file);
            return ServiceResp.success(result);
        } catch (IOException | KnowledgeGraphException e) {
            log.error("导入专业词汇失败", e);
            return ServiceResp.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 下载专业词汇导入模板
     *
     * @param response HTTP响应
     */
    @GetMapping("/download-template")
    @ApiOperation("下载专业词汇导入模板")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            professionalTermService.downloadTemplate(response);
        } catch (IOException e) {
            log.error("下载专业词汇导入模板失败", e);
        }
    }

    /**
     * 同步专业词汇数据到ES
     *
     * @return 同步结果
     */
    @PostMapping("/sync-to-es")
    @ApiOperation("同步专业词汇数据到ES")
    public ServiceResp syncToEs() {
        try {
            ServiceResp result = professionalTermService.syncProfessionalTermToEs();
            return result;
        } catch (Exception e) {
            log.error("同步专业词汇数据到ES失败", e);
            return ServiceResp.fail("同步失败：" + e.getMessage());
        }
    }

    /**
     * 批量提交专业词汇
     *
     * @param ids 专业词汇ID列表
     * @return 结果
     */
    @PostMapping("/batch-submit")
    @ApiOperation("批量提交专业词汇")
    public ServiceResp batchSubmit(@RequestBody List<Long> ids) {
        try {
            return professionalTermService.batchSubmitTerms(ids);
        } catch (Exception e) {
            log.error("批量提交专业词汇失败", e);
            return ServiceResp.fail("提交失败：" + e.getMessage());
        }
    }
    
    /**
     * 填写或修改专业词汇评审结论
     *
     * @param evt 评审结论事件
     * @return 结果
     */
    @PostMapping("/review-conclusion")
    @ApiOperation("填写或修改专业词汇评审结论")
    public ServiceResp reviewConclusion(@RequestBody ProfessionalTermReviewConclusionEvt evt) {
        try {
            if (evt.getProfessionalTermId() == null) {
                return ServiceResp.fail("专业词汇ID不能为空");
            }
            return professionalTermService.addOrUpdateTermReviewConclusion(evt);
        } catch (Exception e) {
            log.error("填写或修改专业词汇评审结论失败", e);
            return ServiceResp.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取专业词汇评审结论
     *
     * @param professionalTermId 专业词汇ID
     * @return 评审结论
     */
    @PostMapping("/get-review-conclusion")
    @ApiOperation("获取专业词汇评审结论")
    public ServiceResp getReviewConclusion(@RequestParam Long professionalTermId) {
        try {
            if (professionalTermId == null) {
                return ServiceResp.fail("专业词汇ID不能为空");
            }
            return professionalTermService.getTermReviewConclusion(professionalTermId);
        } catch (Exception e) {
            log.error("获取专业词汇评审结论失败", e);
            return ServiceResp.fail("获取评审结论失败：" + e.getMessage());
        }
    }

    /**
     * 导出专业词汇到Excel
     *
     * @param evt 导出请求参数
     * @param response HTTP响应对象
     */
    @PostMapping("/export")
    @ApiOperation("导出专业词汇到Excel")
    public void exportTerms(@RequestBody ExportProfessionalTermEvt evt, HttpServletResponse response) {
        try {
            log.info("导出专业词汇，参数：{}", evt);
            professionalTermService.exportTermsToExcel(evt, response);
        } catch (Exception e) {
            log.error("导出专业词汇失败", e);
            throw new KnowledgeBasesException("导出专业词汇失败：" + e.getMessage());
        }
    }

    /**
     * 批量修改专业词汇上报状态
     *
     * @param evt 批量修改上报状态请求参数
     * @return 操作结果
     */
    @PostMapping("/batch-update-report-status")
    @ApiOperation("批量修改专业词汇上报状态")
    public ServiceResp batchUpdateReportStatus(@RequestBody BatchUpdateTermReportStatusEvt evt) {
        log.info("批量修改专业词汇上报状态，参数：{}", evt);
        return professionalTermService.batchUpdateReportStatus(evt);
    }
}