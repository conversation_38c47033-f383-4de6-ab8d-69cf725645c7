package com.ffcs.oss.kg.system.controller.knowledgeBases;

import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.BatchUpdateReportStatusEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.BatchUpdateCotReportStatusEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.GetQaCotsEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.ExportQaCotsEvt;
import com.ffcs.oss.kg.data.model.vo.DashboardStatisticsVO;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.system.events.cot.QaCotIdsEvt;
import com.ffcs.oss.kg.system.events.cot.QaCotInfoEvt;
import com.ffcs.oss.kg.system.events.qa.BatchUpdateQaStateEvt;
import com.ffcs.oss.kg.system.evt.knowledgeBases.InsertKnowledgeBasesEvt;
import com.ffcs.oss.kg.system.service.knowledgeBase.QaCotService;
import com.ffcs.oss.kg.system.vm.cot.QaCotInfoVm;
import com.ffcs.oss.kg.system.vm.cot.QaCotVm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * 问答思维链控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/qa-cot")
@Api(tags = "问答思维链管理接口")
public class QaCotController {

    @Autowired
    private QaCotService qaCotService;

    @ApiOperation("查询问答COT列表")
    @PostMapping("/getQaCots")
    public ServiceResp<QaCotVm> getQaCots(@RequestBody GetQaCotsEvt evt) {
        QaCotVm qaCotVm = qaCotService.getQaCots(evt);
        return ServiceResp.success(qaCotVm);
    }

    @ApiOperation("查询问答COT详情")
    @PostMapping("/getQaCotInfo")
    public ServiceResp<QaCotInfoVm> getQaCotInfo(@RequestBody QaCotInfoEvt evt) {
        QaCotInfoVm qaCotInfoVm = qaCotService.getQaCotInfo(evt);
        return ServiceResp.success(qaCotInfoVm);
    }

    @ApiOperation("添加或更新问答COT")
    @PostMapping("/addOrUpdateQaCot")
    public ServiceResp addOrUpdateQaCot(@RequestBody KgQaCotD evt) {
        return qaCotService.addOrUpdateQaCot(evt);
    }

    @ApiOperation("批量添加问答COT")
    @PostMapping("/addBatchQaCot")
    public ServiceResp addBatchQaCot(@RequestBody List<KgQaCotD> dataList) {
        return qaCotService.addBatchQaCot(dataList);
    }

    @ApiOperation("删除问答COT")
    @PostMapping("/deleteQaCots")
    public ServiceResp deleteQaCots(@RequestBody QaCotIdsEvt evt) {
        return qaCotService.deleteQaCots(evt);
    }

    @ApiOperation("提交问答思维链审核")
    @PostMapping("/submitQaCots")
    public ServiceResp submitQaCots(@RequestBody BatchUpdateQaStateEvt evt) {
        return qaCotService.submitQaCots(evt);
    }

    @ApiOperation("审核通过以及 驳回问答COT")
    @PostMapping("/approveQaCots")
    public ServiceResp approveQaCots(@RequestBody BatchUpdateQaStateEvt evt) {
        return qaCotService.approveQaCots(evt);
    }

    @ApiOperation("驳回问答COT")
    @PostMapping("/rejectQaCots")
    public ServiceResp rejectQaCots(@RequestBody BatchUpdateQaStateEvt evt) {
        return qaCotService.rejectQaCots(evt);
    }

    @ApiOperation("上下线问答COT")
    @PostMapping("/offlineQaCots")
    public ServiceResp offlineQaCots(@RequestBody BatchUpdateQaStateEvt evt) {
        return qaCotService.offlineQaCots(evt);
    }

    @ApiOperation("导出问答COT到Excel")
    @PostMapping("/exportQaCots")
    public void exportQaCots(@RequestBody ExportQaCotsEvt evt, HttpServletResponse response) {
        log.info("导出问答思维链，参数：{}", evt);
        qaCotService.exportQaCotsToExcel(evt, response);
    }

    @ApiOperation("批量修改问答COT上报状态")
    @PostMapping("/batchUpdateReportStatus")
    public ServiceResp batchUpdateReportStatus(@RequestBody BatchUpdateCotReportStatusEvt evt) {
        log.info("批量修改问答思维链上报状态，参数：{}", evt);
        return qaCotService.batchUpdateReportStatus(evt);
    }

    @ApiOperation("问答思维链导入到知识库")
    @PostMapping("/importQaCot")
    public ServiceResp<KnowledgeBasesEntity> importQaCot(@RequestBody InsertKnowledgeBasesEvt insertKnowledgeBasesEvt) throws IOException {
        try {
            KnowledgeBasesEntity knowledgeBasesEntity = qaCotService.importQaCot(insertKnowledgeBasesEvt);
            if (knowledgeBasesEntity != null) {
                return ServiceResp.success(knowledgeBasesEntity.getImportError(), knowledgeBasesEntity);
            } else {
                return ServiceResp.fail("问答思维链导入失败，请检查数据是否符合要求");
            }
        } catch (Exception e) {
            return ServiceResp.fail("问答思维链导入异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "下载问答思维链Excel模板", notes = "用于下载问答思维链系统的Excel导入模板")
    @PostMapping("/downloadTemplate")
    public HttpServletResponse downLoadOperationsManualExcel(HttpServletResponse response) {
        Resource resource = new ClassPathResource("excel/template/问答思维链通用模板.xlsx");
        if (resource.exists()) {
            try {
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/vnd.ms-excel");
                String fileName = resource.getFilename();
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + java.net.URLEncoder.encode(fileName, "UTF-8"));
                response.flushBuffer();
                BufferedInputStream bis = new BufferedInputStream(resource.getInputStream());
                OutputStream os = response.getOutputStream();
                byte[] buffer = new byte[1024];
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
                if (bis != null) {
                    bis.close();
                }
                os.close(); // 关闭输出流
                return response;
            } catch (Exception e) {
                log.error("导出问答思维链通用模板文件异常", e);
            }
        }
        return null;
    }

    @ApiOperation("问答思维链首页接口")
    @PostMapping("/dashboard")
    public ServiceResp<DashboardStatisticsVO> getDashboardStatistics() {
        DashboardStatisticsVO statistics = qaCotService.getDashboardStatistics();
        return ServiceResp.success(statistics);
    }

}