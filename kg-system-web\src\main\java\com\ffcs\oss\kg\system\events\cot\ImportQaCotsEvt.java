package com.ffcs.oss.kg.system.events.cot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 导入问答对的事件
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "导入问答COT的事件")
public class ImportQaCotsEvt {

    @ApiModelProperty(value = "关联的知识库ID")
    private Long kgKnowledgeBasesId;

    @ApiModelProperty(value = "知识空间ID")
    private Long kgBasesSpaceCId;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件名称")
    private String fileName;
} 