package com.ffcs.oss.kg.system.events.cot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 问答对ID列表事件
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "问答COTID列表事件")
public class QaCotIdsEvt {

    @ApiModelProperty(value = "问答otID列表")
    private List<Long> ids;

    @ApiModelProperty(value = "删除状态（1：删除）")
    private String delStatus;

    @ApiModelProperty(value = "操作用户ID")
    private String operUserId;
} 