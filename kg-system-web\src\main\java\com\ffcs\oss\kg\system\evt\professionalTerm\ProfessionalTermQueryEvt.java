package com.ffcs.oss.kg.system.evt.professionalTerm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ffcs.oss.kg.common.core.mvc.PageableEvt;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ffcs.oss.kg.data.rd.entity.common.PermissionEvt;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 专业词汇查询请求参数
 */
@Data
public class ProfessionalTermQueryEvt extends PermissionEvt {

    /**
     * 术语/缩略语/专业名词
     */
    @ApiModelProperty(name = "术语/缩略语/专业名词")
    private String term;

    /**
     * 中文
     */
    @ApiModelProperty(name = "中文")
    private String chinese;

    /**
     * 英文
     */
    @ApiModelProperty(name = "英文")
    private String english;

    /**
     * 审批状态
     */
    @ApiModelProperty(name = "审批状态")
    private String auditStatus;

    /**
     * 创建时间-开始
     */
    @ApiModelProperty(name = "创建时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    /**
     * 创建时间-结束
     */
    @ApiModelProperty(name = "创建时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    private String createdUserName;

    /**
     * 专业领域
     */
    @ApiModelProperty(name = "专业领域")
    private String major;

    /**
     * 应用场景
     */
    @ApiModelProperty(name = "应用场景")
    private String applicationScene;
    
    /**
     * 全文检索关键词
     */
    @ApiModelProperty(name = "全文检索关键词")
    private String keyword;
    
    /**
     * 知识库类型，固定为3-专业词汇
     */
    @ApiModelProperty(name = "知识库类型", hidden = true)
    private String basesType = "3";

    @ApiModelProperty("当前登录人")
    private String beforeOneUser;

    @ApiModelProperty("当前登陆人的同组用户")
    private List<String> perUserNames;
    @ApiModelProperty(name = "当前登录人的类型")
    private String type;

    @ApiModelProperty("当前登陆人的区域数组")
    private List<String> currentUserList;

    @ApiModelProperty(name = "区域", notes = "门户-区域")
    private List<Integer> region = new ArrayList<>();

    /**
     * 状态
     */
    @ApiModelProperty(name = "状态")
    private List<String> state;
    /**
     *
     * 全文检索
     */
    @ApiModelProperty(name = "全文检索")
    private String fullText;

    /**
     * 查询权限对应的专业
     */
    @ApiModelProperty(name = "查询权限对应的专业")
    private List<String> stateMajor;

    /**
     * 查询权限对应的区域
     */
    @ApiModelProperty(name = "查询权限对应的区域")
    private List<Integer> stateRegion;

    private List<String> majorList;

    /**
     * 上报状态列表
     */
    @ApiModelProperty(value = "上报状态", notes = "0-未上报，1-已上报，2-上报失败")
    private List<String> reportStatusList;

    @ApiModelProperty(value = "导出状态", notes = "0-未导出，1-已导出")
    private List<String> exportStatusList;
}