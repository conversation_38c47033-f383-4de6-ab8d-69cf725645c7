package com.ffcs.oss.kg.system.service.graph.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ffcs.oss.common.core.utils.bean.BeanUtils;
import com.ffcs.oss.common.utils.FormatUtil;
import com.ffcs.oss.kg.biz.nebula.GraphQuery;
import com.ffcs.oss.kg.biz.service.graph.InstancePathConfigService;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.nebula.model.NgEdge;
import com.ffcs.oss.kg.common.nebula.model.NgPath;
import com.ffcs.oss.kg.common.nebula.model.NgVertex;
import com.ffcs.oss.kg.common.nebula.model.PathQueryResult;
import com.ffcs.oss.kg.data.converter.graph.AttributeConverter;
import com.ffcs.oss.kg.data.enums.AttributeRelType;
import com.ffcs.oss.kg.data.enums.FiledInitEnum;
import com.ffcs.oss.kg.data.model.evt.graph.EntityTemplateMapping;
import com.ffcs.oss.kg.data.model.evt.graph.RelationTemplateMapping;
import com.ffcs.oss.kg.data.model.vo.graph.*;
import com.ffcs.oss.kg.data.rd.entity.graph.*;
import com.ffcs.oss.kg.data.rd.entity.thematicGraph.ThematicGraphEntity;
import com.ffcs.oss.kg.data.rd.mapper.graph.*;
import com.ffcs.oss.kg.data.model.evt.graph.EntityTypeInsEvt;
import com.ffcs.oss.kg.data.rd.mapper.thematicGraph.ThematicGraphMapper;
import com.ffcs.oss.kg.system.evt.graph.GetSubEvt;
import com.ffcs.oss.kg.system.evt.graph.GraphInsSaveEvt;
import com.ffcs.oss.kg.system.evt.graph.PathQueryEvt;
import com.ffcs.oss.kg.system.evt.graph.SearchEntityInstanceEvt;
import com.ffcs.oss.kg.system.evt.point.GetModelEntityByHierarchyEvt;
import com.ffcs.oss.kg.system.service.graph.ThematicGraphInsService;
import com.ffcs.oss.kg.system.utils.ConstantUtil;
import com.ffcs.oss.kg.system.vm.graph.TempEntityInstanceVm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/3/4
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class ThematicGraphInsServiceImpl implements ThematicGraphInsService {

    /**
     * 最大返回结果数
     */
    @Value("${kg.total:200}")
    private Integer maxResults;

    /**
     * mapper 维护
     */
    private final KgTgModelSelectionMapper modelSelectionMapper;

    private final EntityTypeMapper entityTypeMapper;

    private final KgTgModelSnapshotMapper modelSnapshotMapper;

    private final TempEntityMapper tempEntityMapper;
    private final TempRelationMapper tempRelationMapper;
    private final AttributeMapper attributeMapper;

    private final ThematicGraphMapper thematicGraphMapper;

    private final GraphQuery graphQuery;

    private final EntityTemplateMapper templateMapper;

    private final EntityTypeSelectedAttrMapper selectedAttrMapper;

    private final JdbcTemplate jdbcTemplate;
    
    private final InstancePathConfigService instancePathConfigService;

    @Override
    public List<EntityTypeInsEvt> getModelEntityByHierarchy(GetModelEntityByHierarchyEvt evt) {
        // 1. 基础查询逻辑保持不变
        List<KgTgModelSelection> kgTgModelSelections = modelSelectionMapper.selectList(
                new LambdaQueryWrapper<KgTgModelSelection>()
                        .eq(Objects.isNull(evt.getTgId()), KgTgModelSelection::getBatchNum, evt.getBatchNum()).eq(Objects.nonNull(evt.getTgId()), KgTgModelSelection::getTgId, evt.getTgId())
                        .eq(KgTgModelSelection::getModelType, AttributeRelType.ENTITY.getCode()));

        // 2. 拆分变更集和选择集
        Map<Boolean, List<KgTgModelSelection>> partitioned = kgTgModelSelections.stream().collect(Collectors.partitioningBy(KgTgModelSelection::getIsChange));
        List<KgTgModelSelection> changeList = partitioned.get(true);
        List<KgTgModelSelection> selectCol = partitioned.get(false);

        // 3. 批量处理非变更数据
        List<Long> entityIds = selectCol.stream().map(KgTgModelSelection::getModelId)
                .collect(Collectors.toList());

        List<EntityTypeVO> entityVOs = CollectionUtils.isEmpty(entityIds) ?
                new ArrayList<>() : batchGetEntityTypeVOs(entityIds, evt.getHierarchy());

        // 4. 处理变更数据（保持原有逻辑）
        if (CollectionUtils.isNotEmpty(changeList)) {
            List<Long> snapshotIds = changeList.stream().map(KgTgModelSelection::getModelId).collect(Collectors.toList());
            entityVOs.addAll(processSnapshotData(snapshotIds, evt.getHierarchy()));
        }

        // 5. 转换为最终结果
        return entityVOs.stream().map(this::convertToEntityTypeInsEvt).collect(Collectors.toList());
    }

    private List<EntityTypeVO> processSnapshotData(List<Long> snapshotIds, String hierarchy) {
        List<KgTgModelSnapshot> kgTgModelSnapshots = modelSnapshotMapper.selectBatchIds(snapshotIds);

        return kgTgModelSnapshots.stream().map(snap -> {
            try {
                EntityTypeVO entityTypeVO = ConstantUtil.objectMapper().readValue(snap.getSnapshotData().toString(), EntityTypeVO.class);
                if ((StringUtils.isNotBlank(hierarchy) && entityTypeVO.getSubGraph().equals(hierarchy))
                        || (StringUtils.isBlank(hierarchy) && StringUtils.isBlank(entityTypeVO.getSubGraph()))) {
                    return entityTypeVO;
                } else {
                    return null;
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    // 批量获取实体VO的核心方法
    private List<EntityTypeVO> batchGetEntityTypeVOs(List<Long> entityIds, String hierarchy) {
        // 1. 批量获取基础实体信息
        List<EntityType> entities = entityTypeMapper.selectList(
                new LambdaQueryWrapper<EntityType>()
                        .in(EntityType::getId, entityIds)
                        .eq(StringUtils.isNotBlank(hierarchy), EntityType::getSubGraph, hierarchy));

        // 2. 提前收集需要批量查询的数据
        Set<Long> validEntityIds = entities.stream()
                .map(EntityType::getId)
                .collect(Collectors.toSet());

        // 4. 批量查询所有模板属性关联数据
        Map<Long, List<TemplateAttrSelectionVO>> templateSelectionsMap = batchGetTemplateSelections(validEntityIds);

        // 5. 组装VO对象
        return entities.stream().map(entity -> {
            EntityTypeVO vo = new EntityTypeVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setTemplateSelections(templateSelectionsMap.getOrDefault(entity.getId(), Collections.emptyList()));
            return vo;
        }).collect(Collectors.toList());
    }

    // 批量获取模板选择数据
    private Map<Long, List<TemplateAttrSelectionVO>> batchGetTemplateSelections(Set<Long> entityIds) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return Collections.emptyMap();
        }
        // 1. 批量查询所有模板关联
        List<EntityTypeSelectedAttr> allSelectedAttrs = selectedAttrMapper.selectList(
                new LambdaQueryWrapper<EntityTypeSelectedAttr>().in(EntityTypeSelectedAttr::getEntityTypeId, entityIds).eq(EntityTypeSelectedAttr::getIsDeleted, false));

        // 2. 按实体分组
        Map<Long, List<EntityTypeSelectedAttr>> entityAttrsMap = allSelectedAttrs.stream()
                .collect(Collectors.groupingBy(EntityTypeSelectedAttr::getEntityTypeId));

        // 3. 收集所有需要的属性ID和模板ID
        Set<Long> attrIds = allSelectedAttrs.stream().map(EntityTypeSelectedAttr::getAttributeId).collect(Collectors.toSet());
        Set<Long> templateIds = allSelectedAttrs.stream().map(EntityTypeSelectedAttr::getTemplateId).collect(Collectors.toSet());

        // 4. 批量查询模板和属性
        Map<Long, EntityTemplate> templateMap = templateMapper.selectBatchIds(templateIds).stream().collect(Collectors.toMap(EntityTemplate::getId, Function.identity()));

        Map<Long, Attribute> attributeMap = attributeMapper.selectBatchIds(attrIds).stream().collect(Collectors.toMap(Attribute::getId, Function.identity()));

        // 5. 构建结果结构
        Map<Long, List<TemplateAttrSelectionVO>> result = new HashMap<>();

        entityAttrsMap.forEach((entityId, selectedAttrs) -> {
            // 按模板分组
            Map<Long, List<EntityTypeSelectedAttr>> templateGroup = selectedAttrs.stream().collect(Collectors.groupingBy(EntityTypeSelectedAttr::getTemplateId));

            List<TemplateAttrSelectionVO> selections = templateGroup.entrySet().stream().map(entry -> {
                Long templateId = entry.getKey();
                EntityTemplate template = templateMap.get(templateId);
                if (template == null) return null;

                TemplateAttrSelectionVO vo = new TemplateAttrSelectionVO();
                vo.setTemplateId(templateId);
                vo.setTemplateName(template.getTemplateName());
                vo.setTemplateCode(template.getTemplateCode());

                List<AttributeVO> attributes = entry.getValue().stream()
                        .map(selected -> attributeMap.get(selected.getAttributeId())).filter(Objects::nonNull)
                        .map(AttributeConverter::convertToVO).collect(Collectors.toList());
                vo.setSelectedAttributes(attributes);
                return vo;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            result.put(entityId, selections);
        });

        return result;
    }

    // 转换方法保持不变
    private EntityTypeInsEvt convertToEntityTypeInsEvt(EntityTypeVO vo) {
        EntityTypeInsEvt insEvt = new EntityTypeInsEvt();
        BeanUtils.copyProperties(vo, insEvt);
        List<PropVO> propList = vo.getTemplateSelections().stream()
                .flatMap(te -> te.getSelectedAttributes().stream().map(sel -> createPropVO(te, sel))).collect(Collectors.toList());

        insEvt.setPropList(propList);
        return insEvt;
    }

    private PropVO createPropVO(TemplateAttrSelectionVO te, AttributeVO sel) {
        PropVO prop = new PropVO();
        prop.setPropCode(String.format("%s.%s", te.getTemplateCode(), sel.getAttrCode()));
        prop.setPropName(String.format("%s.%s", te.getTemplateName(), sel.getAttrName()));
        prop.setDataType(sel.getDataType());
        prop.setDefaultValue(sel.getDefaultValue());
        prop.setComment(sel.getRemark());
        prop.setIsVid(sel.getIsVid());
        prop.setIsInit(sel.getIsInit());
        return prop;
    }

    @Override
    public Boolean entityExampleInsert(GraphInsSaveEvt evt) {
        List<TempEntityInstance> tempList = evt.getEntityTypeInsList()
                .stream()
                .map(typeIns -> buildTempEntityInstance(typeIns, evt))
                .collect(Collectors.toList());

        tempEntityMapper.batchInsertOrUpdate(tempList);
        return true;
    }

    @Override
    public NgPath subGraphQuery(GetSubEvt evt) {
        // 1. 获取起点VID
        Object vid = resolveVid(evt.getTempId());

        // 3. 执行子图查询
        NgPath rawPath = graphQuery.querySubGraph(vid, 1, null, null, evt.getGraphSpaceCode());

        /*List<String> rsInsName = rawPath.getEdgeList().stream().filter(fi -> Objects.nonNull(fi.getProps()))
                .map(ver -> FormatUtil.stringParse(ver.getProps().get("name"))).collect(Collectors.toList());*/


        // 2. 获取允许的实体类型和关系类型
        List<EntityTemplateMapping> entities = modelSelectionMapper.selectEntityMappingByShowWithNames(evt.getTgId());
        List<RelationTemplateMapping> relationTemplateMappings = modelSelectionMapper.selectRelationMappings(evt.getTgId(), null);

        // 默认1步

        // 4. 过滤结果并设置显示名称
        return filterAndEnrichPath(rawPath, entities, relationTemplateMappings);
    }

    @Override
    public List<EntityType> getModelEntities(GetModelEntityByHierarchyEvt evt) {
        if (Objects.isNull(evt.getTgId()) && Objects.isNull(evt.getBatchNum())) {
            throw new KnowledgeGraphException("tgId和batchNum不能同时为空");
        }
        
        return entityTypeMapper.selectEntityTypeWithSelection(evt.getTgId(), evt.getBatchNum());
    }


    private Object resolveVid(Long tempId) {
        try {
            // 根据实体ID和模板ID查询实例
            TempEntityInstance instance = tempEntityMapper.selectById(tempId);
            if (instance == null) {
                throw new KnowledgeGraphException("TempEntityInstance not found with id: " + tempId);
            }

            EntityType entityType = entityTypeMapper.selectOne(new LambdaQueryWrapper<EntityType>()
                    .eq(EntityType::getEntityCode, instance.getModelCode())
                    .eq(EntityType::getIsDeleted, false));
            String format = getString(entityType, instance);

            // 执行查询
            Object queriedVid = null;
            try {
                queriedVid = jdbcTemplate.queryForObject(format, Object.class);
            } catch (DataAccessException e) {
                log.error("Error executing query: " + format, e);
                // 查不到先直接返回
                return instance.getVid();
            }

            // 比较查询结果与实例中的vid
            if (queriedVid != null && !queriedVid.equals(instance.getVid())) {
                // 如果不一致，更新实例
                instance.setVid(FormatUtil.stringParse(queriedVid));
                tempEntityMapper.updateById(instance);
                return queriedVid;
            }

            return instance.getVid();
        } catch (Exception e) {
            // 记录错误日志
            log.error("Error resolving VID for event: {}", tempId, e);
            // 根据业务需求决定是返回null还是抛出异常
            // 这里选择返回null，实际可根据需要调整
            return null;
        }
    }

    private static String getString(EntityType entityType, TempEntityInstance instance) {
        if (entityType == null) {
            throw new RuntimeException("EntityType not found for modelCode: " + instance.getModelCode());
        }

        String templateCode = entityType.getTemplateCode().split(",")[0];
        String vidCode = entityType.getVidField().split(",")[0];

        // 查询vid
        String sql = "select %s from %s.%s where " + FiledInitEnum.NAME.getFiledCode() + " = '%s' limit 1";
        return String.format(sql, vidCode, entityType.getGraphSpaceCode(), templateCode, instance.getName());
    }

    private Set<String> getEntityModelCodes(Long tgId) {
        List<TempEntityInstance> entities = tempEntityMapper.selectList(
                new LambdaQueryWrapper<TempEntityInstance>().select(TempEntityInstance::getModelCode).eq(TempEntityInstance::getTgId, tgId)
                        .eq(TempEntityInstance::getDeleted, false).eq(TempEntityInstance::getStatus, 1)

        );
        return entities.stream().map(TempEntityInstance::getModelCode).distinct().collect(Collectors.toSet());
    }

    private Set<String> getRelationModelCodes(Long tgId) {
        List<TempRelationInstance> relations = tempRelationMapper.selectList(
                new LambdaQueryWrapper<TempRelationInstance>().select(TempRelationInstance::getModelCode).eq(TempRelationInstance::getTgId, tgId)
                        .eq(TempRelationInstance::getDeleted, false).eq(TempRelationInstance::getStatus, 1)
        );
        return relations.stream().map(TempRelationInstance::getModelCode).distinct().collect(Collectors.toSet());
    }

    private NgPath filterAndEnrichPath(NgPath path,
                                       List<EntityTemplateMapping> entities,
                                       List<RelationTemplateMapping> relationTemplateMappings) {
        // 过滤顶点
        List<NgVertex> filteredVertices = path.getVertexList().stream()
                .filter(v -> v.getTags() != null &&
                        v.getTags().stream()
                                .anyMatch(tag -> entities.stream()
                                        .map(EntityTemplateMapping::getTemplateCode).collect(Collectors.toSet()).contains(tag.getTagName())))
                .peek(v -> v.getTags().forEach(tag -> {
                    if (tag != null && tag.getTagName() != null) {
                        entities.stream()
                                .filter(etm -> etm.getTemplateCode() != null &&
                                        etm.getTemplateCode().equals(tag.getTagName()))
                        .findFirst()
                                .ifPresent(tem -> {
                                    tag.setEntityName(tem.getEntityName());
                                    tag.setColor(tem.getColor());
                                });
                    }
                }))
                .collect(Collectors.toList());

        // 过滤边
        List<NgEdge> filteredEdges = path.getEdgeList().stream()
                .filter(e -> relationTemplateMappings.stream().map(RelationTemplateMapping::getTemplateCode).collect(Collectors.toSet()).contains(e.getEdgeType()))
                .peek(e -> {
                    e.setRsName(relationTemplateMappings.stream().filter(rtm -> rtm.getTemplateCode().equals(e.getEdgeType())).findFirst().orElse(new RelationTemplateMapping()).getRsName());
                })
                .collect(Collectors.toList());

        NgPath result = new NgPath();
        result.setVertexList(filteredVertices);
        result.setEdgeList(filteredEdges);
        return result;
    }

    // 缓存这些查询以提高性能
    private TempEntityInstance getEntityByModelCode(String code) {
        return null;
    }

    private TempRelationInstance getRelationByModelCode(String code) {
        return null;
    }

    private TempEntityInstance buildTempEntityInstance(EntityTypeInsEvt typeIns, GraphInsSaveEvt evt) {
        TempEntityInstance instance = new TempEntityInstance();
        instance.setTgId(evt.getTgId());
        instance.setBatchId(evt.getBatchNum());
        instance.setModelCode(typeIns.getEntityCode());

        List<PropValueVo> propValues = processPropList(typeIns.getPropList());
        Map<String, String> jsonNodeMap = processPropValues(propValues, instance);

        instance.setInstanceData(ConstantUtil.objectMapper().valueToTree(Collections.singletonList(jsonNodeMap)));
        instance.setStatus(0);
        return instance;
    }

    private List<PropValueVo> processPropList(List<PropVO> props) {
        return props.stream()
                .peek(this::validateRequiredValue)
                .map(prop -> new PropValueVo(prop.getPropCode(), prop.getValue()))
                .collect(Collectors.toList());
    }

    private void validateRequiredValue(PropVO prop) {
        if ((Boolean.TRUE.equals(prop.getIsInit()) || Boolean.TRUE.equals(prop.getIsVid()))
                && (prop.getValue() == null || prop.getValue().trim().isEmpty())) {
            throw new IllegalArgumentException("属性[" + prop.getPropCode() + "]的value不能为空（isInit/isVid为true）");
        }
    }

    private Map<String, String> processPropValues(List<PropValueVo> propValues, TempEntityInstance instance) {
        Map<String, String> jsonNodeMap = new HashMap<>();
        propValues.forEach(node -> {
            String[] parts = node.getPropCode().split("\\.", 2);  // 优化split次数
            String field = parts.length > 1 ? parts[1] : "";

            if (field.equals(FiledInitEnum.NAME.getFiledCode())) {
                instance.setName(node.getPropValue());
            } else if (field.equals(FiledInitEnum.DISPLAY_NAME.getFiledCode())) {
                instance.setDisplayName(node.getPropValue());
            } else {
                jsonNodeMap.put(node.getPropCode(), node.getPropValue());
            }
        });
        return jsonNodeMap;
    }

    @Override
    public List<TempEntityInstanceVm> searchEntityInstances(SearchEntityInstanceEvt evt) {
        log.info("开始根据关键字查询实例，参数：{}", evt);

        if (Objects.isNull(evt.getTgId()) && StringUtils.isBlank(evt.getBatchNum())) {
            throw new KnowledgeGraphException("tgId和batchNum不能同时为空");
        }

        ThematicGraphEntity thematicGraphEntity = thematicGraphMapper.selectById(evt.getTgId());
        if (Objects.isNull(thematicGraphEntity)) {
            throw new KnowledgeGraphException("找不到专题图谱");
        }

        // 构建查询条件
        LambdaQueryWrapper<TempEntityInstance> queryWrapper = new LambdaQueryWrapper<>();

        // 根据图谱ID查询
        if (Objects.nonNull(evt.getTgId())) {
            queryWrapper.eq(TempEntityInstance::getTgId, evt.getTgId());
        }

        // 根据批次号查询
        if (Objects.isNull(evt.getTgId()) && StringUtils.isNotBlank(evt.getBatchNum())) {
            queryWrapper.eq(TempEntityInstance::getBatchId, evt.getBatchNum());
        }

        // 根据关键字查询
        if (StringUtils.isNotBlank(evt.getKeyword())) {
            String keyword = "%" + evt.getKeyword() + "%";
            queryWrapper.and(wrapper ->
                wrapper.like(TempEntityInstance::getName, keyword)
                    .or()
                    .like(TempEntityInstance::getDisplayName, keyword)
            );
        }

        // 只查询未删除的
        queryWrapper.eq(TempEntityInstance::getDeleted, false);

        // 限制返回结果数量
        queryWrapper.last("LIMIT " + maxResults);

        // 执行查询
        List<TempEntityInstance> results = tempEntityMapper.selectList(queryWrapper);

        // 如果没有查询到实例，直接返回空列表
        if (CollectionUtils.isEmpty(results)) {
            log.info("根据关键字查询实例完成，共找到0条记录");
            return new ArrayList<>();
        }

        // 获取所有实例的模型编码
        List<String> modelCodes = results.stream()
                .map(TempEntityInstance::getModelCode)
                .distinct()
                .collect(Collectors.toList());

        // 如果模型编码列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(modelCodes)) {
            log.info("根据关键字查询实例完成，但没有有效的模型编码，共找到0条记录");
            return new ArrayList<>();
        }

        // 构建实体类型查询条件
        LambdaQueryWrapper<EntityType> entityTypeWrapper = new LambdaQueryWrapper<EntityType>()
                .in(EntityType::getEntityCode, modelCodes)
                .eq(EntityType::getGraphSpaceCode, thematicGraphEntity.getGraphSpaceCode())
                .eq(EntityType::getIsDeleted, false);

        // 添加entityName精准查询条件
        if (CollectionUtils.isNotEmpty(evt.getEntityName())) {
            // 检查是否包含"不限定实体类型"
            boolean hasUnlimited = evt.getEntityName().contains("不限定实体类型");
            if (!hasUnlimited) {
                entityTypeWrapper.in(EntityType::getEntityName, evt.getEntityName());
            }
        }

        List<EntityType> entityTypes = entityTypeMapper.selectList(entityTypeWrapper);
        Map<String, EntityType> entityMap = entityTypes.stream().collect(Collectors.toMap(EntityType::getEntityCode, entityType -> entityType));

        log.info("根据关键字查询实例完成，共找到{}条记录", results.size());
        return results.stream()
                .filter(instance -> {
                    // 如果指定了entityName且不包含"不限定实体类型"，需要过滤结果
                    if (CollectionUtils.isNotEmpty(evt.getEntityName())) {
                        boolean hasUnlimited = evt.getEntityName().contains("不限定实体类型");
                        if (!hasUnlimited) {
                            EntityType entityType = entityMap.get(instance.getModelCode());
                            return entityType != null && evt.getEntityName().contains(entityType.getEntityName());
                        }
                    }
                    return entityMap.containsKey(instance.getModelCode());
                })
                .map(instance -> {
                    EntityType entityType = entityMap.get(instance.getModelCode());
                    TempEntityInstanceVm kgSelectionVo = new TempEntityInstanceVm();
                    BeanUtils.copyProperties(instance, kgSelectionVo);
                    kgSelectionVo.setEntityCode(entityType.getEntityCode());
                    kgSelectionVo.setEntityName(entityType.getEntityName());
                    kgSelectionVo.setEntityId(entityType.getId());
                    kgSelectionVo.setColor(entityType.getColor());
                    return kgSelectionVo;
                }).collect(Collectors.toList());
    }
    
    @Override
    public PathQueryResult pathQuery(PathQueryEvt evt) {
        log.info("执行路径查询，参数：{}", evt);

        Object vid = resolveVid(evt.getTempId());
        if(Objects.nonNull(vid)){
            evt.setVid(FormatUtil.stringParse(vid));
        }
        
        // 参数校验
        if (StringUtils.isBlank(evt.getTgId()) || StringUtils.isBlank(evt.getVid()) || 
             StringUtils.isBlank(evt.getCurrentTypeCode())) {
            throw new KnowledgeGraphException("查无数据");
        }
        if (StringUtils.isBlank(evt.getEntryTypeCode())) {
            // 如果入口编码为空 则为入口  设置入口编码为当前编码
            evt.setEntryTypeCode(evt.getCurrentTypeCode());
        }
        
        try {
            // 调用业务层服务执行路径查询
            return instancePathConfigService.executePathQuery(
                    evt.getGraphSpaceCode(),
                    evt.getTgId(), evt.getVid(), evt.getEntryTypeCode(), evt.getCurrentTypeCode());
        } catch (Exception e) {
            log.error("路径查询失败", e);
            throw new KnowledgeGraphException("路径查询失败: " + e.getMessage());
        }
    }

}
