package com.ffcs.oss.kg.system.service.graph.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ffcs.oss.common.core.utils.StringUtils;
import com.ffcs.oss.common.core.utils.bean.BeanUtils;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.model.TemplateData;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.converter.graph.DtPermissionConverter;
import com.ffcs.oss.kg.data.enums.AttributeRelType;
import com.ffcs.oss.kg.data.model.evt.graph.*;
import com.ffcs.oss.kg.data.model.thematicGraph.QueryThematicGraphParamPageModel;
import com.ffcs.oss.kg.data.model.thematicGraph.ThematicGraphPageModel;
import com.ffcs.oss.kg.data.model.vo.graph.EntityTypeVO;
import com.ffcs.oss.kg.data.model.vo.graph.KgSelectionVo;
import com.ffcs.oss.kg.data.model.vo.graph.RelationTypeVO;
import com.ffcs.oss.kg.data.model.vo.graph.TempEntityInstanceVO;
import com.ffcs.oss.kg.data.rd.entity.graph.*;
import com.ffcs.oss.kg.data.rd.entity.thematicGraph.ThematicGraphEntity;
import com.ffcs.oss.kg.data.rd.mapper.graph.*;
import com.ffcs.oss.kg.data.rd.mapper.thematicGraph.GraphCategoryMapper;
import com.ffcs.oss.kg.data.rd.mapper.thematicGraph.ThematicGraphMapper;
import com.ffcs.oss.kg.system.config.CommonConfig;
import com.ffcs.oss.kg.system.config.ConstantConfig;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.evt.graph.QueryThematicGraphTypeParamEvt;
import com.ffcs.oss.kg.system.evt.graph.ThematicGraphSaveEvt;
import com.ffcs.oss.kg.system.evt.point.GetModelEntityByHierarchyEvt;
import com.ffcs.oss.kg.system.evt.thematicGraph.QueryThematicGraphParamPageEvt;
import com.ffcs.oss.kg.system.service.graph.ThematicGraphService;
import com.ffcs.oss.kg.system.service.graph.processor.ExcelProcessorService;
import com.ffcs.oss.kg.system.service.graph.processor.InstanceSyncService;
import com.ffcs.oss.kg.system.vm.graph.ThematicGraphTypeVm;
import com.ffcs.oss.kg.system.vm.thematicGraph.ThematicGraphVm;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.alibaba.fastjson.JSONObject;
/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/2/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class ThematicGraphServiceImpl implements ThematicGraphService {
    @Autowired
    private DtPermissionConverter dtPermissionConverter;
    @Autowired
    private CommonConfig commonConfig;

    private final ConstantConfig constantConfig;
    /**
     * 类型维护
     */
    private final EntityTemplateServiceImpl entityTemplateService;

    private final EntityTypeServiceImpl entityTypeService;

    private final RelationTemplateServiceImpl relationTemplateService;

    private final RelationTypeServiceImpl relationTypeService;

    private final GraphInsServiceImpl graphInsService;

    private final InstanceSyncService instanceSyncService;

    /**
     * mapper 维护
     */
    private final ThematicGraphMapper thematicGraphMapper;
    private final GraphCategoryMapper graphCategoryMapper;
    private final KgTgModelSelectionMapper modelSelectionMapper;

    private final EntityTemplateMapper entityTemplateMapper;

    private final EntityTypeMapper entityTypeMapper;
    private final RelationTypeMapper relationTypeMapper;
    private final RelationTemplateMapper relationTemplateMapper;

    private final InterfacesAndScenesServiceImpl interactionsAndScenesService;

    private final KgTgModelSnapshotMapper modelSnapshotMapper;

    private final ExcelProcessorService exportProcessorService;

    private final TempEntityMapper tempEntityMapper;
    private final TempRelationMapper tempRelationMapper;


    @Override
    public PageInfo<ThematicGraphVm> queryThematicGraphPage(QueryThematicGraphParamPageEvt evt) {
        //标记当前登录人,查询条件中使用
        if (StrUtil.isEmpty(evt.getBeforeOneUser())) {
            evt.setBeforeOneUser(PtSecurityUtils.getUsername());
        }
        QueryThematicGraphParamPageModel model = new QueryThematicGraphParamPageModel();
        BeanUtils.copyProperties(evt, model);
        List<Long> categoryIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(evt.getCategoryName())) {
            categoryIds = graphCategoryMapper.selectChildIdsByCategoryName(model.getCategoryName());
        }
        if (evt.getCategoryId() != null) {
            categoryIds = graphCategoryMapper.selectChildIdsById(model.getCategoryId());
        }
        model.setCategoryIds(categoryIds);

        PageHelper.startPage(model.getPageNo(), model.getPageSize());
        // 根据查询条件 分页查询数据
        List<ThematicGraphPageModel> list = thematicGraphMapper.queryThematicGraphPage(model);
        PageInfo<ThematicGraphPageModel> thematicGraphPageModelPageInfo = new PageInfo<>(list);
        List<ThematicGraphVm> results = list.stream().map(item -> {
            ThematicGraphVm thematicGraphPageVm = new ThematicGraphVm();
            BeanUtils.copyProperties(item, thematicGraphPageVm);
            return thematicGraphPageVm;
        }).collect(Collectors.toList());
        DtPermissionConverter.permissionConvert(results, evt);
        PageInfo<ThematicGraphVm> pageInfo = new PageInfo<>(results);
        pageInfo.setPageNum(thematicGraphPageModelPageInfo.getPageNum());
        pageInfo.setPageSize(thematicGraphPageModelPageInfo.getPageSize());
        pageInfo.setTotal(thematicGraphPageModelPageInfo.getTotal());
        return pageInfo;
    }

    @Override
    public ServiceResp<Long> createTempTg(CreateTempTgEvt evt) {
        if (CollectionUtils.isEmpty(evt.getSelectionList())) {
            return ServiceResp.success(0L);
        }
        // todo  选中的数据可能有新增的 没有id 选不做处理直接由
        evt.setSelectionList(evt.getSelectionList().stream().filter(oj -> Objects.nonNull(oj.getModelId())).collect(Collectors.toList()));

        AttributeRelType attributeRelType = AttributeRelType.codeBy(evt.getModelType());
        if (Objects.isNull(attributeRelType)) {
            throw new KnowledgeGraphException("不支持的类型");
        }
        // 1. 删除打标的临时数据
        try {
            tempDelete(evt);
        } catch (Exception e) {
            throw new KnowledgeGraphException("删除数据失败");
        }
        // 2. 新增的数据 新增到关联表上
        List<KgTgModelSelection> currentList = getCurrentList(evt.getTgId(), evt.getBatchNum(), evt.getModelType(), null);

        List<Long> noChangeList = currentList.stream().filter(fi -> !fi.getIsChange()).map(KgTgModelSelection::getModelId).collect(Collectors.toList());
        List<ChooseTypeEvt> insertList = evt.getSelectionList().stream().filter(oj -> !oj.isDeleted() && !noChangeList.contains(oj.getModelId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<KgTgModelSelection> selectionInsert = insertList.stream().map(ok -> convertKgModelSelection(ok.getModelId(), evt.getBatchNum(), evt.getTgId(), evt.getModelType())).collect(Collectors.toList());
            modelSelectionMapper.insertBatch(selectionInsert);
        }
        // 3.补全数据
        buildPointsAndEdges(evt.getTgId(), evt.getBatchNum(), attributeRelType);

        return ServiceResp.success(0L);
    }


    @Override
    public ServiceResp<Long> deleteTempTg(DeleteTempTgEvt evt) throws KnowledgeGraphException {
        AttributeRelType modelType = AttributeRelType.codeBy(evt.getModelType());
        if (Objects.isNull(modelType)) {
            throw new KnowledgeGraphException("不支持的类型");
        }
        // 0.
        // 0.1. 实体模板和关系模板删除还是不做关联处理 todo
        // 0.2. 实体类型删除 删除对应的 关系类型
        // 0.3. 关系类型删除 删除对应的 实体类型
        List<KgTgModelSelection> currentList = modelSelectionMapper.selectBatchIds(evt.getDeleteSelectionId());
        // 1. 快照数据直接删除
        List<KgTgModelSelection> noChangeList = currentList.stream().filter(oj -> !oj.getIsChange()).collect(Collectors.toList());

        //2. 根据类型不同 补全删除
        List<Long> deleteModelIds = noChangeList.stream().map(KgTgModelSelection::getModelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteModelIds)) {
            LambdaQueryWrapper<KgTgModelSelection> delWrapper = buildDeleteWrapper(deleteModelIds, modelType, evt.getTgId(), evt.getBatchNum());
            if (Objects.nonNull(delWrapper)) {
                modelSelectionMapper.delete(delWrapper);
            }
        }

        // 3. 快照数据删除
        List<Long> snapList = currentList.stream().filter(KgTgModelSelection::getIsChange).map(KgTgModelSelection::getModelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(snapList)) {
            modelSnapshotMapper.deleteBatchIds(snapList);
        }
        // 4.传入的参数删除
        modelSelectionMapper.deleteBatchIds(evt.getDeleteSelectionId());
        return ServiceResp.success("删除成功", 0L);
    }

    private LambdaQueryWrapper<KgTgModelSelection> buildDeleteWrapper(List<Long> deleteModelIds, AttributeRelType modelType, Long tgId, String batchNum) {
        LambdaQueryWrapper<KgTgModelSelection> delWrapper = new LambdaQueryWrapper<>();
        delWrapper.eq(Objects.isNull(tgId), KgTgModelSelection::getBatchNum, batchNum)
                .eq(Objects.nonNull(tgId), KgTgModelSelection::getTgId, tgId);
        switch (modelType) {
            case ENTITY_TEMPLATE:
            case RELATION_TEMPLATE:
                return null;
            case ENTITY:
                List<RelationType> relationTypes = relationTypeMapper.selectRelationsByEntityTypeIdsOr(deleteModelIds);
                List<Long> relList = relationTypes.stream().map(RelationType::getId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(relList)) {
                    return null;
                }
                delWrapper.in(KgTgModelSelection::getModelId, relList).eq(KgTgModelSelection::getModelType, AttributeRelType.RELATION.getCode());

                //todo 模板还要判断其他没有共用 先不删
                /*List<RelationTemplate> relationTemplates = relationTemplateMapper.selectBatchIds(relationTypes.stream().map(RelationType::getTemplateId).filter(Objects::nonNull).collect(Collectors.toList()));
                delWrapper.in(KgTgModelSelection::getModelId, relationTemplates).eq(KgTgModelSelection::getModelType, AttributeRelType.RELATION_TEMPLATE.getCode());*/
                break;
            case RELATION:
                List<RelationType> relationRelation = relationTypeMapper.selectList(
                        new LambdaQueryWrapper<RelationType>()
                                .in(RelationType::getId, deleteModelIds).eq(RelationType::getIsDeleted, false));
                List<RelationTemplate> templates = relationTemplateMapper.selectBatchIds(relationRelation.stream().map(RelationType::getTemplateId).filter(Objects::nonNull).collect(Collectors.toList()));

                List<Long> entityList = relationRelation.stream()
                        .flatMap(item -> Stream.of(item.getAEntityTypeId(), item.getZEntityTypeId())).distinct()
                        .collect(Collectors.toList());
                // todo 补全删除需要判断

                List<KgTgModelSelection> kgTgModelSelections = modelSelectionMapper.selectList(new LambdaQueryWrapper<KgTgModelSelection>()
                        .notIn(KgTgModelSelection::getModelId, deleteModelIds).eq(KgTgModelSelection::getModelType, AttributeRelType.RELATION.getCode()).eq(Objects.isNull(tgId), KgTgModelSelection::getBatchNum, batchNum)
                        .eq(Objects.nonNull(tgId), KgTgModelSelection::getTgId, tgId).eq(KgTgModelSelection::getIsChange, false));
                if (CollectionUtils.isNotEmpty(kgTgModelSelections)) {
                    List<RelationType> sameEntityType = relationTypeMapper.selectList(
                            new LambdaQueryWrapper<RelationType>()
                                    .in(RelationType::getId, kgTgModelSelections.stream().map(KgTgModelSelection::getModelId).collect(Collectors.toList())).eq(RelationType::getIsDeleted, false));
                    List<Long> needEntity = sameEntityType.stream()
                            .flatMap(item -> Stream.of(item.getAEntityTypeId(), item.getZEntityTypeId())).distinct()
                            .collect(Collectors.toList());

                    entityList = entityList.stream().filter(item -> !needEntity.contains(item)).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(entityList)) {
                    return null;
                }

                delWrapper
                        .in(KgTgModelSelection::getModelId, entityList)
                        .eq(KgTgModelSelection::getModelType, AttributeRelType.ENTITY.getCode());
                //todo 模板不删

                /*if (CollectionUtils.isNotEmpty(templates)) {
                    // 当 templates 不为空时，构造 AND (条件1 OR 条件2)
                    List<Long> finalEntityList = entityList;
                    delWrapper.and(wrapper ->
                            wrapper.or(CollectionUtils.isNotEmpty(templates),subWrapper ->
                                            subWrapper.in(KgTgModelSelection::getModelId,
                                                            templates.stream().map(RelationTemplate::getId).collect(Collectors.toList()))
                                                    .eq(KgTgModelSelection::getModelType, AttributeRelType.RELATION_TEMPLATE.getCode())
                                    ).or(subWrapper ->
                                            subWrapper.in(KgTgModelSelection::getModelId, finalEntityList)
                                                    .eq(KgTgModelSelection::getModelType, AttributeRelType.ENTITY.getCode())
                                    )
                    );
                } else {
                    delWrapper
                            .in(KgTgModelSelection::getModelId, entityList)
                            .eq(KgTgModelSelection::getModelType, AttributeRelType.ENTITY.getCode());
                }*/
                break;
            default:
                break;
        }
        return delWrapper;
    }

    /**
     * 同步专题图谱下的 实体模板/类型 关系模板/类型
     *
     * @param tgId
     * @param batchNum
     */
    private void buildPointsAndEdges(Long tgId, String batchNum, AttributeRelType modelType) {
        // 0. 代码规则
        // 1 实体模板 关系模板新增不需要补全数据
        // 2 实体类型 需要补全实体模板 当前已有的实体类型能够构建的 关系类型 以及对应的关系模板
        // 3 关系类型 需要补全对应的 A Z 端的实体类型 以及对应的实体模板

        // 1.查询新增完后 已经有的属性数据
        List<KgTgModelSelection> allList = getCurrentList(tgId, batchNum, null, null);
        List<KgTgModelSelection> currentList = allList.stream().filter(fi -> !fi.getIsChange()).collect(Collectors.toList());
        // 1.1 按类型区分数据
        List<Long> entityTypeList = currentList.stream().filter(item -> AttributeRelType.ENTITY.getCode().equals(item.getModelType())).map(KgTgModelSelection::getModelId).collect(Collectors.toList());
        List<Long> entityTemplateList = currentList.stream().filter(item -> AttributeRelType.ENTITY_TEMPLATE.getCode().equals(item.getModelType())).map(KgTgModelSelection::getModelId).collect(Collectors.toList());
        List<Long> rsTypeList = currentList.stream().filter(item -> AttributeRelType.RELATION.getCode().equals(item.getModelType())).map(KgTgModelSelection::getModelId).collect(Collectors.toList());
        List<Long> rsTemplateList = currentList.stream().filter(item -> AttributeRelType.RELATION_TEMPLATE.getCode().equals(item.getModelType())).map(KgTgModelSelection::getModelId).collect(Collectors.toList());

        List<ChooseSelectionEvt> selectionList = new ArrayList<>();
        switch (modelType) {
            case ENTITY:
                if (CollectionUtils.isNotEmpty(entityTypeList)) {
                    // 2.1 获取实体类型对应的实体模板
                    List<EntityTemplate> entityTemplates = entityTypeMapper.selectTemplatesByEntityTypeIds(entityTypeList);
                    selectionList.addAll(entityTemplates.stream().map(EntityTemplate::getId)
                            .filter(fi -> !entityTemplateList.contains(fi)).map(ma -> new ChooseSelectionEvt(ma, AttributeRelType.ENTITY_TEMPLATE.getCode())).collect(Collectors.toList()));
                    // 2.2 获取当前实体类型能够构建的关系类型/模板
                    // 2.3 获取关系类型对应的关系模板
                    List<RelationType> relationTypes = relationTypeMapper.selectRelationsByEntityTypeIds(entityTypeList);
                    if (CollectionUtils.isNotEmpty(relationTypes)) {
                        selectionList.addAll(relationTypes.stream().map(RelationType::getId)
                                .filter(fi -> !rsTypeList.contains(fi)).map(ma -> new ChooseSelectionEvt(ma, AttributeRelType.RELATION.getCode())).collect(Collectors.toList()));
                        selectionList.addAll(relationTypes.stream().map(RelationType::getTemplateId).distinct()
                                .filter(fi -> !rsTemplateList.contains(fi)).map(ma -> new ChooseSelectionEvt(ma, AttributeRelType.RELATION_TEMPLATE.getCode())).collect(Collectors.toList()));
                    }
                }
                break;
            case RELATION:
                if (CollectionUtils.isNotEmpty(rsTypeList)) {
                    // 3.1 查询现有的关系类型数据
                    List<RelationType> relationTypes = relationTypeMapper.selectList(new LambdaQueryWrapper<RelationType>().in(RelationType::getId, rsTypeList).eq(RelationType::getIsDeleted, false));
                    List<Long> collectEntity = relationTypes.stream().map(fo -> Arrays.asList(fo.getAEntityTypeId(), fo.getZEntityTypeId())).flatMap(List::stream).distinct().filter(io -> !entityTypeList.contains(io)).collect(Collectors.toList());
                    // 3.2 根据查询到的实体类型id 查询实体模板
                    if (CollectionUtils.isNotEmpty(collectEntity)) {
                        selectionList.addAll(collectEntity.stream().map(ma -> new ChooseSelectionEvt(ma, AttributeRelType.ENTITY.getCode())).collect(Collectors.toList()));
                        List<EntityTemplate> entityTemplates = entityTemplateMapper.selectList(new LambdaQueryWrapper<EntityTemplate>().in(EntityTemplate::getId, collectEntity).eq(EntityTemplate::getIsDeleted, false));
                        selectionList.addAll(entityTemplates.stream().map(EntityTemplate::getId)
                                .filter(fi -> !entityTemplateList.contains(fi)).map(ma -> new ChooseSelectionEvt(ma, AttributeRelType.ENTITY_TEMPLATE.getCode())).collect(Collectors.toList()));

                    }
                }
                break;
            default:
                break;
        }

        // 4. 写入临时关系表
        List<KgTgModelSelection> insertList = selectionList.stream().distinct().map(item -> convertKgModelSelection(item.getModelId(), batchNum, tgId, item.getModelType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            modelSelectionMapper.insertBatch(insertList);
        }

    }

    /**
     * 转换数据
     *
     * @param modelId
     * @param batchNum
     * @param tgId
     * @param modelType
     * @return
     */
    private KgTgModelSelection convertKgModelSelection(Long modelId, String batchNum, Long tgId, String modelType) {
        KgTgModelSelection kgTgModelSelection = new KgTgModelSelection();
        kgTgModelSelection.setBatchNum(batchNum);
        kgTgModelSelection.setModelType(modelType);
        kgTgModelSelection.setModelId(modelId);
        kgTgModelSelection.setIsTemp(true);
        kgTgModelSelection.setTgId(tgId);
        kgTgModelSelection.setIsChange(false);
        return kgTgModelSelection;
    }

    private List<KgTgModelSelection> getCurrentList(Long tgId, String batchNum, String modelType, Boolean isChaneGroup) {
        return modelSelectionMapper.selectList(new LambdaQueryWrapper<KgTgModelSelection>().eq(Objects.nonNull(tgId), KgTgModelSelection::getTgId, tgId)
                .eq(Objects.isNull(tgId), KgTgModelSelection::getBatchNum, batchNum)
                .eq(StringUtils.isNotBlank(modelType), KgTgModelSelection::getModelType, modelType)
                .eq(Objects.nonNull(isChaneGroup), KgTgModelSelection::getIsChange, isChaneGroup));
    }

    /**
     * 删除通用
     *
     * @param evt
     */
    private void tempDelete(CreateTempTgEvt evt) throws KnowledgeGraphException {
        //todo 删除时也需要关联删除(否则会被补全)
        List<Long> delList = evt.getSelectionList().stream().filter(ChooseTypeEvt::isDeleted).map(ChooseTypeEvt::getModelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delList)) {
            List<KgTgModelSelection> modelDeleteList = modelSelectionMapper.selectList(new LambdaQueryWrapper<KgTgModelSelection>()
                    .eq(Objects.nonNull(evt.getTgId()), KgTgModelSelection::getTgId, evt.getTgId()).eq(Objects.isNull(evt.getTgId()), KgTgModelSelection::getBatchNum, evt.getBatchNum())
                    .eq(KgTgModelSelection::getModelType, evt.getModelType()).in(KgTgModelSelection::getModelId, delList));

            DeleteTempTgEvt delTg = new DeleteTempTgEvt();
            BeanUtils.copyProperties(evt, delTg);
            delTg.setDeleteSelectionId(modelDeleteList.stream().map(KgTgModelSelection::getSelectionId).collect(Collectors.toList()));
            deleteTempTg(delTg);
        }
    }

    @Override
    public ServiceResp<String> operateCancel(OperateCancelEvt evt) {

        // todo 第一次新建的删除需要 直接把 对应实体模板 关系的给删了 先跳过
        // 1.取消操作直接删除掉对应的关联表数据
        List<KgTgModelSelection> kgTgModelSelections = modelSelectionMapper.selectList(new LambdaQueryWrapper<KgTgModelSelection>().eq(Objects.isNull(evt.getTgId()), KgTgModelSelection::getBatchNum, evt.getBatchNum())
                .eq(Objects.nonNull(evt.getTgId()), KgTgModelSelection::getTgId, evt.getTgId()).eq(KgTgModelSelection::getIsTemp, true));

        if (CollectionUtils.isNotEmpty(kgTgModelSelections)) {
            List<Long> snapList = kgTgModelSelections.stream().filter(KgTgModelSelection::getIsChange).map(KgTgModelSelection::getModelId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(snapList)) {
                modelSnapshotMapper.deleteBatchIds(snapList);
            }
            modelSelectionMapper.deleteBatchIds(kgTgModelSelections.stream().map(KgTgModelSelection::getSelectionId).collect(Collectors.toList()));
        }
        return ServiceResp.success("取消成功");
    }

    @Override
    public boolean checkUnsavedDataByTgId(Long tgId) {
        Integer integer = modelSelectionMapper.selectCount(new LambdaQueryWrapper<KgTgModelSelection>()
                .eq(KgTgModelSelection::getTgId, tgId).eq(KgTgModelSelection::getIsTemp, true));
        return integer > 0;
    }

    @Override
    public boolean deleteUnsavedDataByTgId(Long tgId) {
        // 1.不加载直接删除数据
        modelSelectionMapper.delete(new LambdaQueryWrapper<KgTgModelSelection>().eq(KgTgModelSelection::getTgId, tgId).eq(KgTgModelSelection::getIsTemp, true));
        return true;
    }

    @Override
    public ServiceResp<Long> createThematicGraph(ThematicGraphSaveEvt evt) {
        // 1.校验数据
        // 2.保存基本信息
        Long tgId = saveGraphInfo(evt);
        // 3.模板关联的信息更新状态 为非临时
        dealWithTempData(evt, tgId);
        // 4.同步临时数据到正式表上
        dealWithInstanceData(evt, tgId);
        // 5.接口等模块的修改
        try {
            interactionsAndScenesService.processInterfacesAndScenes(evt.getInterfaceList(), evt.getSceneList(), tgId);
        } catch (Exception e) {
            throw new KnowledgeGraphException("写入接口场景表错误");
        }
        return ServiceResp.success("保存成功", tgId);
    }

    @Override
    public ServiceResp<Long> editThematicGraph(ThematicGraphSaveEvt evt) {
        // 1.校验数据
        // 2.保存基本信息
        Long tgId = saveGraphInfo(evt);
        // 3.模板关联的信息更新状态 为非临时
        dealWithTempData(evt, tgId);
        // 4.同步临时数据到正式表上
        dealWithInstanceData(evt, tgId);
        // 5.接口等模块的修改
        try {
            interactionsAndScenesService.processInterfacesAndScenesForEdit(evt.getInterfaceList(), evt.getSceneList(), tgId);
        } catch (Exception e) {
            throw new KnowledgeGraphException("写入接口场景表错误");
        }
        return ServiceResp.success("保存成功", tgId);
    }

    @Override
    public ServiceResp<Long> stagingThematicGraph(ThematicGraphSaveEvt evt) {
        // 1. 校验数据
        // 2. 保存基本信息
        Long tgId = saveGraphInfo(evt);
        // 3. 接口等模块的修改
        try {
            interactionsAndScenesService.processInterfacesAndScenesForEdit(evt.getInterfaceList(), evt.getSceneList(), tgId);
        } catch (Exception e) {
            throw new KnowledgeGraphException("写入接口场景表错误");
        }
        return ServiceResp.success("保存成功", tgId);
    }

    @Override
    public PageInfo<TempEntityInstanceVO> queryTgInsEntityPage(CommonInsEvt evt) {
        return graphInsService.pageTempEntity(evt);
    }

    private Long saveGraphInfo(ThematicGraphSaveEvt evt) {
        ThematicGraphEntity tgEntity = new ThematicGraphEntity();
        BeanUtils.copyProperties(evt, tgEntity);
        tgEntity.setIsDel(CommonConstant.IS_DELETED);
        // insert 专题图谱表 获取主表id
        if (Objects.nonNull(evt.getTgId())) {
            thematicGraphMapper.updateById(tgEntity);
        } else {
            if (commonConfig.getPerSwitch()) {
                dtPermissionConverter.initPermission(tgEntity, DataPermissionConstant.KNOW, evt.getCategoryId());
            }

            thematicGraphMapper.insert(tgEntity);
        }
        return tgEntity.getTgId();
    }

    private void dealWithInstanceData(ThematicGraphSaveEvt evt, Long tgId) {
        // 查询所有选择的关系
        instanceSyncService.dealWithInstanceData(evt, tgId);
    }


    /**
     * 同步处理关系维护数据
     *
     * @param evt
     * @param tgId
     */
    private void dealWithTempData(ThematicGraphSaveEvt evt, Long tgId) {
        LambdaUpdateWrapper<KgTgModelSelection> updateSet = new LambdaUpdateWrapper<KgTgModelSelection>()
                .set(KgTgModelSelection::getTgId, tgId)
                .set(KgTgModelSelection::getBatchNum, evt.getBatchNum())
                .set(KgTgModelSelection::getIsTemp, false).set(KgTgModelSelection::getIsChange, false);
        if (Objects.isNull(evt.getTgId())) {
            modelSelectionMapper.update(null, updateSet.eq(KgTgModelSelection::getBatchNum, evt.getBatchNum()).eq(KgTgModelSelection::getIsChange, false));
        } else {
            modelSelectionMapper.update(null, updateSet.eq(KgTgModelSelection::getTgId, evt.getTgId()).eq(KgTgModelSelection::getIsChange, false));
        }
        // 处理不同类型下的数据
        List<KgTgModelSelection> currentList = getCurrentList(evt.getTgId(), evt.getBatchNum(), null, true);
        List<KgTgModelSelection> entityTypeList = currentList.stream().filter(item -> AttributeRelType.ENTITY.getCode().equals(item.getModelType())).collect(Collectors.toList());
        List<KgTgModelSelection> entityTemplateList = currentList.stream().filter(item -> AttributeRelType.ENTITY_TEMPLATE.getCode().equals(item.getModelType())).collect(Collectors.toList());
        List<KgTgModelSelection> rsTypeList = currentList.stream().filter(item -> AttributeRelType.RELATION.getCode().equals(item.getModelType())).collect(Collectors.toList());
        List<KgTgModelSelection> rsTemplateList = currentList.stream().filter(item -> AttributeRelType.RELATION_TEMPLATE.getCode().equals(item.getModelType())).collect(Collectors.toList());

        //todo  分别处理新增 编辑数据的操作 后续改为分线程处理
        entityTypeService.dealSyncSource(entityTypeList, tgId, evt.getBatchNum());
        entityTemplateService.dealSyncSource(entityTemplateList, tgId, evt.getBatchNum());
        relationTypeService.dealSyncSource(rsTypeList, tgId, evt.getBatchNum());
        relationTemplateService.dealSyncSource(rsTemplateList, tgId, evt.getBatchNum());

    }

    @Override
    public ThematicGraphTypeVm queryEntityAndRelationship(QueryThematicGraphTypeParamEvt evt) {
        // .1 todo 改成查全量 然后展示 用listTg
        ThematicGraphTypeVm thematicGraphTypeVm = new ThematicGraphTypeVm();
        ServiceResp<PageInfo<EntityTypeVO>> pageEntityList = entityTypeService.listTg(new TgBaseQueryEvt(evt.getTgId(), evt.getBatchNum()));
        ServiceResp<PageInfo<RelationTypeVO>> pageRelList = relationTypeService.listTg(new TgBaseQueryEvt(evt.getTgId(), evt.getBatchNum()));
        if (Objects.nonNull(pageEntityList.getBody()) && pageEntityList.isSuccess()) {
            thematicGraphTypeVm.setTempTgEntityList(pageEntityList.getBody().getList());
        }
        if (Objects.nonNull(pageRelList.getBody()) && pageRelList.isSuccess()) {
            thematicGraphTypeVm.setTempTgRsList(pageRelList.getBody().getList());
        }

        return thematicGraphTypeVm;
    }

    @Override
    public void exportTemplate(CommonGraphEvt evt, HttpServletResponse response) throws Exception {
        ThematicGraphEntity graph = exportProcessorService.validateThematicGraph(evt.getTgId());
        List<TemplateData> templates = new ArrayList<>();

        // 处理实体类型模板
        exportProcessorService.processEntityTemplates(templates, graph.getTgId());

        // 处理关系类型模板
        exportProcessorService.processRelationTemplates(templates, graph.getTgId());

        if (templates.isEmpty()) {
            throw new KnowledgeGraphException("当前专题图谱下没有可导出的类型数据");
        }

        exportProcessorService.exportExcel(response, templates);
    }

    @Override
    public boolean changeSelection(SelectionChangeEvt evt) {
        modelSelectionMapper.update(null, new LambdaUpdateWrapper<KgTgModelSelection>().set(KgTgModelSelection::getIsShow, evt.getIsShow())
                .in(KgTgModelSelection::getSelectionId, evt.getSelectionList()));
        return true;
    }

    @Override
    public List<KgSelectionVo> getChangeSelectionList(CommonGraphEvt evt) {
        log.info("查询参数：{}", evt.toString());
        // 1. 查询所有实体类型的数据
        List<KgTgModelSelection> kgTgModelSelections = modelSelectionMapper.selectList(new LambdaQueryWrapper<KgTgModelSelection>().eq(Objects.isNull(evt.getTgId()), KgTgModelSelection::getBatchNum, evt.getBatchNum())
                .eq(Objects.nonNull(evt.getTgId()), KgTgModelSelection::getTgId, evt.getTgId()).eq(KgTgModelSelection::getIsChange, false).eq(KgTgModelSelection::getModelType, AttributeRelType.ENTITY.getCode()).orderByDesc(KgTgModelSelection::getCreatedTime));
        List<KgTgModelSelection> kgTgModelSelectionsBak=new ArrayList<>();
        kgTgModelSelectionsBak.addAll(kgTgModelSelections);
        // 修改页面都查
        if (CollectionUtils.isEmpty(evt.getTempIds())) {
            if (kgTgModelSelections.stream().noneMatch(KgTgModelSelection::getIsShow)) {
                // 没有展示的默认选择第一个
                kgTgModelSelections = CollectionUtils.isEmpty(kgTgModelSelections) ? new ArrayList<>() : Collections.singletonList(kgTgModelSelections.get(0));
            } else {
                kgTgModelSelections = kgTgModelSelections.stream().filter(KgTgModelSelection::getIsShow).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(kgTgModelSelections)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<EntityType> entityTypeWrapper = new LambdaQueryWrapper<EntityType>();


        // 添加entityName精准查询条件
        if (CollectionUtils.isNotEmpty(evt.getEntityName())) {
            // 检查是否包含"不限定实体类型"
            boolean hasUnlimited = evt.getEntityName().contains("不限定实体类型");
            if (!hasUnlimited) {
                entityTypeWrapper.in(EntityType::getEntityName, evt.getEntityName()).eq(EntityType::getIsDeleted, false);
            }
            entityTypeWrapper.in(EntityType::getId, kgTgModelSelectionsBak.stream().map(KgTgModelSelection::getModelId).collect(Collectors.toList()));
        } else {
            entityTypeWrapper.in(EntityType::getId, kgTgModelSelections.stream().map(KgTgModelSelection::getModelId).collect(Collectors.toList()))
                    .eq(EntityType::getIsDeleted, false);
        }

        List<EntityType> entityTypes = entityTypeMapper.selectList(entityTypeWrapper);

        Map<String, EntityType> entityMap = entityTypes.stream().collect(Collectors.toMap(EntityType::getEntityCode, entityType -> entityType));

        //最后过滤
        LambdaQueryWrapper<TempEntityInstance> instanceWrapper = new LambdaQueryWrapper<TempEntityInstance>()
                .eq(Objects.isNull(evt.getTgId()), TempEntityInstance::getBatchId, evt.getBatchNum())
                .eq(Objects.nonNull(evt.getTgId()), TempEntityInstance::getTgId, evt.getTgId())
                .in(CollectionUtils.isNotEmpty(evt.getTempIds()), TempEntityInstance::getTempId, evt.getTempIds())
                .eq(TempEntityInstance::getDeleted, false);
        if (CollectionUtils.isNotEmpty(entityTypes)) {
            log.error("entityTypes为null,入参为:{}",JSONObject.toJSONString(evt));
            instanceWrapper.in(TempEntityInstance::getModelCode, entityTypes.stream().map(EntityType::getEntityCode).collect(Collectors.toList()));
        }

        // 添加keyword对name和displayName的联合模糊查询
        if (StringUtils.isNotBlank(evt.getKeyword())) {
            instanceWrapper.and(wrapper ->
                    wrapper.like(TempEntityInstance::getName, evt.getKeyword())
                            .or()
                            .like(TempEntityInstance::getDisplayName, evt.getKeyword())
            );
        }

        instanceWrapper.orderByDesc(TempEntityInstance::getUpdatedTime)
                .last(" LIMIT " + constantConfig.getGraphShowNum());

        List<TempEntityInstance> entityInstance = tempEntityMapper.selectList(instanceWrapper);
        log.info("查询实体类型数据数量：{}", entityInstance.size());

        List<KgTgModelSelection> finalKgTgModelSelections = CollectionUtils.isNotEmpty(kgTgModelSelectionsBak)?kgTgModelSelectionsBak:kgTgModelSelections;

        return entityInstance.stream().map(instance -> {
            EntityType entityType = entityMap.get(instance.getModelCode());
            KgSelectionVo kgSelectionVo = new KgSelectionVo();
            kgSelectionVo.setSelectionId(finalKgTgModelSelections.stream().filter(item -> item.getModelId().equals(entityType.getId())).findFirst().orElse(new KgTgModelSelection()).getSelectionId());
            kgSelectionVo.setName(instance.getName());
            kgSelectionVo.setDisplayName(instance.getDisplayName());
            kgSelectionVo.setEntityCode(entityType.getEntityCode());
            kgSelectionVo.setEntityName(entityType.getEntityName());
            kgSelectionVo.setEntityId(entityType.getId());
            kgSelectionVo.setInstanceData(instance.getInstanceData().get(0));
            kgSelectionVo.setTempId(instance.getTempId());
            kgSelectionVo.setColor(entityType.getColor());
            kgSelectionVo.setVid(instance.getVid());
            return kgSelectionVo;
        }).collect(Collectors.toList());
    }


}
