package com.ffcs.oss.kg.system.service.knowledgeBase;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.model.evt.hidedanger.SaveKgBasesAuditPersonDEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.*;
import com.ffcs.oss.kg.data.model.vm.bases.KgBasesAuditPersonDVm;
import com.ffcs.oss.kg.data.rd.entity.DocumentEntity;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.*;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.SpacePageInfoEvt;
import com.ffcs.oss.kg.system.evt.knowledgeBases.*;
import com.ffcs.oss.kg.system.evt.rawCase.*;
import com.ffcs.oss.kg.system.vm.dictionary.DictionaryTreeVm;
import com.ffcs.oss.kg.system.vm.dictionary.DictionaryVm;
import com.ffcs.oss.kg.system.vm.knowledgeBases.KnowledgeBasesInfoVm;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Classname KnowledgeBasesService
 * <AUTHOR>
 */
public interface KnowledgeBasesService {

    KnowledgeBasesVm getKnowledgeBases(GetBasesEvt evt);

    ServiceResp searchKnowledgeBackGroundAndInfo();

    ServiceResp searchKnowledgeApplicationEffective();

    ServiceResp queryMajorRank();

    ServiceResp queryLocalNetworkRank();

    KnowledgeBasesEntity addOrUpdateKnowledgeBases(InsertKnowledgeBasesEvt insertKnowledgeBasesEvt) throws Exception;

    boolean addKnowledgeFile(HttpServletRequest request, MultipartFile[] multipartFiles) throws IOException;

    KnowledgeBasesInfoVm getKnowledgeBasesInfo(HttpServletRequest request, InsertKnowledgeBasesEvt evt);

    boolean deleteKgbsByIds(KgBsIdsEvt evt);

    boolean downLoadFileDocument(HttpServletResponse response, DownLoadDocEvt evt);

    ServiceResp topUpBases(List<KnowledgeBasesEntity> evt);

    ServiceResp cancelTopUpBases(List<KnowledgeBasesEntity> evt);

    PageInfo<List<KnowledgeBasesEntity>> getBasesByAuthor(AuthorEvt evt);

    List<DocumentEntity> getDocumentsByKgBaseId(GetBasesDocumentsEvt evt);

    void downLoadBaseManualExcel(HttpServletResponse response) throws IOException;

    void downLoadBasesManualDocx(HttpServletResponse response) throws IOException;

    PageInfo<KnowledgeBasesEntity> getBasesOnFullText(GetBasesEvt evt);

    List<String> getBasesImageInfo(InsertKnowledgeBasesEvt evt);

    String insertBasesImages(MultipartFile file);

    boolean deleteDoc(DocEvt evt);

    String lookForBases(LookForBasesEvt evt);

    ServiceResp submitKnowledgeBases(BatchUpdateBasesStateEvt evt)throws Exception;

    void exportBasesWordOrPdf(Long kgBasesId, String fileType, HttpServletResponse response);

    boolean commonDownLoadFile(HttpServletResponse response, CommonDownloadFileEvt evt);

    ServiceResp addOrUpdateBasesAuditDimensionConfig(List<KgBasesAuditDimensionConfigD> evt);

    ServiceResp getBasesAuditDimensionConfig();

    ServiceResp deleteBasesAuditDimensionConfig(KgBasesAuditDimensionConfigD evt);

    ServiceResp addOrUpdateBasesReviewConclusion(BasesReviewConclusionEvt evt);

    ServiceResp getBasesReviewConclusion(BasesReviewConclusionEvt evt);

    ServiceResp addOrUpdateBasesAuditPerson(SaveKgBasesAuditPersonDEvt evt);

    QueryPageVm<KgBasesAuditPersonDVm> getBasesAuditPerson(KgBasesAuditPersonDEvt evt);

    KgBasesAuditPersonDVm getBasesAuditPersonByName(KgBasesAuditPersonDEvt evt);

    ServiceResp batchDeleteBasesAuditPerson(List<KgBasesAuditPersonD> evt);

    ServiceResp batchSetRegionAdmin(List<KgBasesAuditPersonD> evt);

    ServiceResp batchCancelRegionAdmin(List<KgBasesAuditPersonD> evt);

    ServiceResp updateAuditorAllocationRuleConfig(KgAuditorAllocationRuleConfigD evt);

    ServiceResp getWaitingAuditAndHistoryAuditNums(KnowledgeBasesEntity evt);

    ServiceResp getAuditorAllocationRuleConfig();

    ServiceResp getBasesAuditPersonTelPhone(KgBasesAuditPersonD evt);

    ServiceResp getBasesRelatedTopFive(KnowledgeBasesEntity evt);

    UploadApiResponse uploadFileToRag(String filePath,String ragFlowId);

    boolean deleteFileDocument(DeletDocEvt evt);

    Map<String, List<DictionaryVm>> getAllBasesDictionary(List<String> codeTypeList);

    KnowledgeBasesEntity importBasesManualExcel(MultipartFile file) throws IOException;

    Object getBasesSpaceById(Long id);

    PageInfo<KgBasesSpaceC> getAvailableBasesSpaces(SpacePageInfoEvt evt);

    Map<String,List<DictionaryTreeVm>> getAllBasesTreeDictionary(List<String> codeTypeList);

    List<KgBasesSpaceLabelD> getLabels();

    void createLabel(KgBasesSpaceLabelD evt);

    void updateLabel(KgBasesSpaceLabelD evt);

    void deleteLabels(List<Long> ids);

    List<Map<String, String>> getSpacesNums();

    List<KgBasesSpaceC> getAllBasesSpaces();

    PageInfo<KgBasesSpaceC> getKnowledgeBasess(GetBasesEvt evt);

    String basesCheckRepeat(BasesCheckRepeatEvt evt);

    ApiResponse ragBuild(DatasetCreateRequest request);

    ApiResponse delRagFlowSpaceFile(DatasetCreateRequest request);

    ServiceResp reportKnowledgeBases(BatchUpdateBasesStateEvt evt);

    ServiceResp offerLineKnowledgeBases(BatchUpdateBasesStateEvt evt);

    List<Map<String, Object>> batchAddKnowledgeBases(List<InsertKnowledgeBasesEvt> insertKnowledgeBasesEvts);

    List<KnowledgeBasesEntity> batchUploadBases(MultipartFile[] files) throws IOException;

    /**
     * 文件查重并存储结果到Redis
     * 该方法会委托给FileSimilarityService进行异步处理
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param fromPage 是否来自页面请求
     */
    void checkFileRepeatAndSaveToRedis(Long kgKnowledgeBasesId,boolean fromPage);

    /**
     * 从Redis获取文件查重结果
     * 该方法会委托给FileSimilarityService获取相似度列表
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @return 相似文件列表
     */
    List<SimilarFileInfo> getFileSimilarityListFromRedis(Long kgKnowledgeBasesId);

    KnowledgeBasesEntity importqwkbs(HttpServletRequest request, MultipartFile file) throws IOException;

    String downloadAndAnalyzeFileContent(DownLoadDocEvt evt);

    KnowledgeBasesEntity importqwkbss(InsertKnowledgeBasesEvt insertKnowledgeBasesEvt);

    HttpServletResponse downLoadCommonFile(HttpServletResponse response, String address, String modelName);

    /**
     * 修改知识库名称
     *
     * @param evt 修改知识库名称请求参数
     * @return 操作结果
     */
    ServiceResp updateKnowledgeBaseName(com.ffcs.oss.kg.data.model.evt.knowledgeBases.UpdateKnowledgeBaseNameEvt evt);
}
