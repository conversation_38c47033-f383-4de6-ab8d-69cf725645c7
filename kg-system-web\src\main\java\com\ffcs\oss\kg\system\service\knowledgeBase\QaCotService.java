package com.ffcs.oss.kg.system.service.knowledgeBase;

import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.BatchUpdateCotReportStatusEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.ExportQaCotsEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.GetQaCotsEvt;
import com.ffcs.oss.kg.data.model.vo.DashboardStatisticsVO;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.system.events.cot.QaCotIdsEvt;
import com.ffcs.oss.kg.system.events.cot.QaCotInfoEvt;
import com.ffcs.oss.kg.system.events.qa.BatchUpdateQaStateEvt;
import com.ffcs.oss.kg.system.evt.knowledgeBases.InsertKnowledgeBasesEvt;
import com.ffcs.oss.kg.system.vm.cot.QaCotInfoVm;
import com.ffcs.oss.kg.system.vm.cot.QaCotVm;

import java.util.List;

/**
 * 问答对服务接口
 * 
 * <AUTHOR>
 */
public interface QaCotService {

    /**
     * 获取问答COT列表
     * 
     * @param evt 获取问答对列表的请求参数
     * @return 问答对列表视图模型
     */
    QaCotVm getQaCots(GetQaCotsEvt evt);

    /**
     * 获取问答COT详情
     * 
     * @param evt 获取问答对详情的请求参数
     * @return 问答对详情视图模型
     */
    QaCotInfoVm getQaCotInfo(QaCotInfoEvt evt);

    /**
     * 添加或更新问答COT
     * 
     * @param evt 添加或更新问答对的请求参数
     * @return 操作结果
     */
    ServiceResp addOrUpdateQaCot(KgQaCotD evt);

    /**
     * 批量导入
     * @param dataList
     * @return
     */
    ServiceResp addBatchQaCot(List<KgQaCotD> dataList);

    /**
     * 删除问答COT
     * 
     * @param evt 删除问答对的请求参数
     * @return 操作结果
     */
    ServiceResp deleteQaCots(QaCotIdsEvt evt);

    /**
     * 提交问答对审核
     * 
     * @param evt 提交问答对审核的请求参数
     * @return 操作结果
     */
    ServiceResp submitQaCots(BatchUpdateQaStateEvt evt);

    /**
     * 审核通过问答COT
     * 
     * @param evt 审核问答对的请求参数
     * @return 操作结果
     */
    ServiceResp approveQaCots(BatchUpdateQaStateEvt evt);

    /**
     * 审核拒绝问答对
     * 
     * @param evt 审核问答对的请求参数
     * @return 操作结果
     */
    ServiceResp rejectQaCots(BatchUpdateQaStateEvt evt);

    /**
     * 下线问答对
     * 
     * @param evt 下线问答对的请求参数
     * @return 操作结果
     */
    ServiceResp offlineQaCots(BatchUpdateQaStateEvt evt);



    /**
     * 检查问答COT重复
     * 
     * @param question 问题内容
     * @return 检查结果，存在重复返回true
     */
    boolean checkQaCotRepeat(String question);

    /**
     * 从知识库导入问答COT
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param filePath           文件路径
     * @param organization       组织机构
     * @param knowledgeOrigin    知识来源
     * @param lifeCycle          生命周期
     * @return 导入的问答对列表
     */
    List<KgQaCotD> importQaCotsFromKnowledgeBase(Long kgKnowledgeBasesId, String filePath, String organization, String knowledgeOrigin, String lifeCycle, String author, Long region, Long kgBasesSpaceCId);

    /**
     * 导出问答对到Excel
     *
     * @param evt 导出请求参数
     * @param response HTTP响应对象
     */
    void exportQaCotsToExcel(ExportQaCotsEvt evt, javax.servlet.http.HttpServletResponse response);

    /**
     * 批量修改问答对上报状态
     *
     * @param evt 批量修改上报状态请求参数
     * @return 操作结果
     */
    ServiceResp batchUpdateReportStatus(BatchUpdateCotReportStatusEvt evt);

    /**
     * 导入问答思维 先导入到知识库
     *
     * @param insertKnowledgeBasesEvt
     * @return
     */
    KnowledgeBasesEntity importQaCot(InsertKnowledgeBasesEvt insertKnowledgeBasesEvt);


    /**
     * 获取问答思维导图统计数据
     * @return
     */
    DashboardStatisticsVO getDashboardStatistics();

} 