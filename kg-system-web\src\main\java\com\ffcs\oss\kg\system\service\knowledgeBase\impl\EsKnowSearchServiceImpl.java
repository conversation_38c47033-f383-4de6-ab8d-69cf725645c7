package com.ffcs.oss.kg.system.service.knowledgeBase.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.data.converter.graph.DtPermissionConverter;
import com.ffcs.oss.kg.data.es.KnowledgeBasesIdx;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.GetBasesEvt;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: ZSX
 * @Description:
 * @Date: 2025/6/12
 */
@Service
@Transactional
@Slf4j
public class EsKnowSearchServiceImpl {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    //===================== 公开方法 =====================//

    public void updateInfo(KnowledgeBasesIdx updateEntity, Long id, String knowledgeBasesIndexName) {
        //todo 审核需要修改es上的状态
        try {
            // 2. 转换为Json（只序列化非null字段）
            String json = elasticsearchRestTemplate.getElasticsearchConverter()
                    .mapObject(updateEntity)
                    .toJson();

            // 3. 构建更新请求
            UpdateQuery updateQuery = UpdateQuery.builder(String.valueOf(id))
                    .withDocument(Document.parse(json))
                    .build();
            log.debug("ES更新请求:{}", updateQuery);
            elasticsearchRestTemplate.update(updateQuery, IndexCoordinates.of(knowledgeBasesIndexName));
            log.info("ES更新成功, ID:{}", updateEntity.getKgKnowledgeBasesId());
        } catch (Exception e) {
            if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                log.error("es新增失败，异常信息：{}", e.getMessage());
            }
        }
    }



    /**
     * 分页查询知识库（返回PageInfo）
     */
    public PageInfo<KnowledgeBasesEntity> getCasesOnFullTextWithPage(GetBasesEvt evt) {
        NativeSearchQuery query = buildQuery(evt, true); // 启用分页
        SearchHits<KnowledgeBasesIdx> hits = executeSearch(query);
        return convertToPageInfo(hits, evt);
    }

    /**
     * 无分页查询知识库（返回List）
     */
    public List<KnowledgeBasesEntity> getCasesOnFullTextWithoutPage(GetBasesEvt evt) {
        NativeSearchQuery query = buildQuery(evt, false); // 禁用分页
        SearchHits<KnowledgeBasesIdx> hits = executeSearch(query);
        return convertToList(hits, evt);
    }

    //===================== 私有方法 =====================//

    /**
     * 执行ES查询
     */
    private SearchHits<KnowledgeBasesIdx> executeSearch(NativeSearchQuery query) {
        return elasticsearchRestTemplate.search(query, KnowledgeBasesIdx.class);
    }

    /**
     * 构建基础查询条件
     */
    private BoolQueryBuilder buildBaseQuery(GetBasesEvt evt) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("auditStatus", CommonConstant.YES))
                .filter(QueryBuilders.termQuery("isDeleted", CommonConstant.NO));

        // 全文检索
        if (StringUtils.isNotEmpty(evt.getFullText())) {
            boolQuery.must(QueryBuilders.matchPhraseQuery("fullText", evt.getFullText()));
        }

        // 权限专业过滤（优先）
        if (CollectionUtils.isNotEmpty(evt.getStateMajor())) {
            boolQuery.filter(QueryBuilders.termsQuery("major", evt.getStateMajor()));
        }
        // 表单专业过滤
        if (CollectionUtils.isNotEmpty(evt.getMajor())) {
            boolQuery.filter(QueryBuilders.termsQuery("major", evt.getMajor()));
        }

        // 权限区域过滤（优先）
        if (CollectionUtils.isNotEmpty(evt.getStateRegion())) {
            boolQuery.filter(QueryBuilders.termsQuery("region", evt.getStateRegion()));
        }
        // 表单区域过滤
        if (CollectionUtils.isNotEmpty(evt.getRegion())) {
            boolQuery.filter(QueryBuilders.termsQuery("region", evt.getRegion()));
        }

        // 其他状态过滤
        if (CollectionUtils.isNotEmpty(evt.getState())) {
            boolQuery.filter(QueryBuilders.termsQuery("state", evt.getState()));
        }

        // 权限附加条件
        DtPermissionConverter.esPermission(evt, boolQuery);
        return boolQuery;
    }

    /**
     * 构建完整查询（动态分页）
     */
    private NativeSearchQuery buildQuery(GetBasesEvt evt, boolean withPagination) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
                .withQuery(buildBaseQuery(evt))
                .withSort(SortBuilders.fieldSort("updatedTime").order(SortOrder.DESC));

        if (withPagination) {
            builder.withPageable(PageRequest.of(evt.getPageNo() - 1, evt.getPageSize()));
        }
        return builder.build();
    }

    /**
     * 转换为分页结果
     */
    private PageInfo<KnowledgeBasesEntity> convertToPageInfo(SearchHits<KnowledgeBasesIdx> hits, GetBasesEvt evt) {
        List<KnowledgeBasesEntity> content = convertToList(hits, evt);
        PageInfo<KnowledgeBasesEntity> pageInfo = new PageInfo<>(content);
        pageInfo.setTotal(hits.getTotalHits());
        return pageInfo;
    }

    /**
     * 转换为列表结果
     */
    private List<KnowledgeBasesEntity> convertToList(SearchHits<KnowledgeBasesIdx> hits, GetBasesEvt evt) {
        List<KnowledgeBasesEntity> entities = hits.getSearchHits().stream()
                .map(hit -> {
                    KnowledgeBasesEntity entity = new KnowledgeBasesEntity();
                    BeanUtil.copyProperties(hit.getContent(), entity);
                    // 处理特殊字段格式
                    if (StringUtils.isNotBlank(entity.getOperativeWord())) {
                        entity.setOperativeWord(entity.getOperativeWord().replace(",", ";"));
                    }
                    return entity;
                })
                .collect(Collectors.toList());

        DtPermissionConverter.permissionConvert(entities, evt);
        return entities;
    }
}
