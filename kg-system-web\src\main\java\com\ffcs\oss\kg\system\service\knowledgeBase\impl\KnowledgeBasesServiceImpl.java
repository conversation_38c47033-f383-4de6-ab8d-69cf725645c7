package com.ffcs.oss.kg.system.service.knowledgeBase.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ffcs.oss.common.core.utils.bean.BeanUtils;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.FormatUtil;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.kg.common.cache.CtgRedisService;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.ExceptionDefinition;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.model.ExcelData;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.common.core.util.*;
import com.ffcs.oss.kg.data.converter.graph.DtPermissionConverter;
import com.ffcs.oss.kg.data.enums.*;
import com.ffcs.oss.kg.data.enums.knowledgeBases.AuditEnum;
import com.ffcs.oss.kg.data.es.KnowledgeBasesIdx;
import com.ffcs.oss.kg.data.model.evt.hidedanger.SaveKgBasesAuditPersonDEvt;
import com.ffcs.oss.kg.system.client.KnowledgeReportClient;
import com.ffcs.oss.kg.system.utils.ReportFormatUtil;
import com.ffcs.oss.kg.common.core.constant.ReportStatusConstant;
import com.ffcs.oss.kg.data.model.KgKnowledgeHomeIndexD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.*;
import com.ffcs.oss.kg.data.model.vm.KgKnowledgeHomeIndexDVm;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ffcs.oss.kg.data.model.vm.bases.KgBasesAuditPersonDVm;
import com.ffcs.oss.kg.data.model.vm.bases.MajorStatisticsVm;
import com.ffcs.oss.kg.data.model.vm.region.QueryRegionListVm;
import com.ffcs.oss.kg.data.rd.entity.*;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.common.CommonCategoryEntity;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.*;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.SpacePageInfoEvt;
import com.ffcs.oss.kg.data.rd.mapper.*;
import com.ffcs.oss.kg.data.rd.mapper.cases.AuditorAllocationRuleConfigMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.*;
import com.ffcs.oss.kg.dfs.entity.FileInfo;
import com.ffcs.oss.kg.dfs.service.CtdfsService;
import com.ffcs.oss.kg.system.client.RagFlowClient;
import com.ffcs.oss.kg.system.client.SmQryClient;
import com.ffcs.oss.kg.system.config.CommonConfig;
import com.ffcs.oss.kg.system.config.KnowledgeBaseConfig;
import com.ffcs.oss.kg.system.config.KnowledgeConstant;
import com.ffcs.oss.kg.system.config.RegionConfig;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.evt.index.SizeInfo;
import com.ffcs.oss.kg.system.evt.index.UnitConverterUtil;
import com.ffcs.oss.kg.system.evt.knowledgeBases.*;
import com.ffcs.oss.kg.system.evt.rawCase.*;
import com.ffcs.oss.kg.system.evt.user.QryUserEvt;
import com.ffcs.oss.kg.system.client.FileSimilarityClient;
import com.ffcs.oss.kg.system.service.DocService;
import com.ffcs.oss.kg.system.service.JtDocService;
import com.ffcs.oss.kg.system.service.deviceCon.KgContiDService;
import com.ffcs.oss.kg.system.service.knowledgeBase.KnowledgeBasesService;
import com.ffcs.oss.kg.system.service.knowledgeBases.KnowledgeSpaceService;
import com.ffcs.oss.kg.system.utils.ReportFormatUtil;
import com.ffcs.oss.kg.system.utils.TextPlagiarismCheckUtil;
import com.ffcs.oss.kg.system.vm.dictionary.DictionaryTreeVm;
import com.ffcs.oss.kg.system.vm.dictionary.DictionaryVm;
import com.ffcs.oss.kg.system.vm.knowledgeBases.ApiResponseVm;
import com.ffcs.oss.kg.system.vm.knowledgeBases.KnowledgeBasesInfoVm;
import com.ffcs.oss.kg.system.vm.user.SimpleUserVm;
import com.ffcs.oss.kg.system.vm.user.UserVm;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import okhttp3.*;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.*;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

// 添加SpringContextHolder导入
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.system.service.knowledgeBase.QaPairService;
import java.io.FileOutputStream;

/**
 * @Classname KnowledgeBasesServiceImpl
 * <AUTHOR>
 */
@Service
@Transactional
public class KnowledgeBasesServiceImpl implements KnowledgeBasesService {


    @Autowired
    private KnowledgeBaseConfig knowledgeBaseConfig;

    @Value("${cndids.tenant-code}")
    private String tenantCode;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private KgContiDService kgContiDService;
    @Resource
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private KgBasesSpaceCMapper kgBasesSpaceCMapper;

    @Autowired
    private KgBasesSpaceRelationCMapper kgBasesSpaceRelationCMapper;

    @Autowired
    private KgBasesSpaceLabelDMapper kgBasesSpaceLabelDMapper;

    @Autowired
    private BasesAuditDimensionConfigMapper basesAuditDimensionConfigMapper;

    @Autowired
    private BasesAuditDimensionMapper basesAuditDimensionMapper;

    @Resource
    private DocumentMapper documentMapper;

    @Autowired
    private DtPermissionConverter dtPermissionConverter;

    @Autowired
    private CtdfsService ctdfsService;

    @Resource
    private BasesAuditPersonMapper basesAuditPersonMapper;

    @Resource
    private AuditorAllocationRuleConfigMapper auditorAllocationRuleConfigMapper;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private SmQryClient smQryClient;

    @Autowired
    private SearchClickLogMapper searchClickLogMapper;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private RegionMapper regionMapper;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private DocService docService;

    @Autowired
    private JtDocService jtDocService;

    @Resource
    private KnowledgeHomeIndexMapper knowledgeHomeIndexMapper;

    @Resource
    private KnowledgeBackgroundConfigMapper knowledgeBackgroundConfigMapper;

    @Resource
    private PublicMapper publicMapper;

    @Resource
    private CommonCategoryMapper commonCategoryMapper;

    @Resource
    private ModelKnowledgeBasesDMapper knowledgeBasesDMapper;

    @Resource
    private KgBasesReportLogMapper kgBasesReportLogMapper;

    @Autowired
    private CtgRedisService ctgRedisService;

    @Autowired(required = false)
    private FileSimilarityClient fileSimilarityClient;


    @Resource
    private RegionConfig regionConfig;

    @Resource
    private EsKnowSearchServiceImpl esKnowSearchService;

    @Autowired
    private QaPairService qaPairService;

    @Autowired
    private KnowledgeSpaceService spaceService;

    @Autowired
    private com.ffcs.oss.kg.system.service.rag.RagService ragService;


    /**
     * 创建上报批次记录
     *
     * @param reportStatus 上报状态：1-集团未上报，2-已上报集团，3-集团已下线，4-集团已删除
     * @param operUser     操作人
     * @return 批次号
     */
    private String createReportBatch(String reportStatus, String operUser, Date date) {
        log.info("入参分别为：reportStatus: {},operUser: {},date: {}", reportStatus, operUser, date);
        KgBasesReportLog logEntity = new KgBasesReportLog();
        logEntity.setBatchNo(UUID.randomUUID().toString().replace("-", ""));
        logEntity.setReportStatus(reportStatus);
        logEntity.setOperTime(date);
        logEntity.setOperUser(operUser);
        logEntity.setOperStatus("1"); // 进行中
        logEntity.setCreatedTime(date);
        logEntity.setUpdatedTime(date);
        log.info("批次实体日志：{}", logEntity.toString());
        kgBasesReportLogMapper.insert(logEntity);
        return logEntity.getBatchNo();
    }

    /**
     * 更新上报批次状态
     *
     * @param batchNo    批次号
     * @param operStatus 操作状态：0-进行中，1-已完成，2-失败
     * @param failReason 失败原因
     */
    private void updateReportBatchStatus(String batchNo, Integer operStatus, String failReason) {
        try {
            UpdateWrapper<KgBasesReportLog> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("batch_no", batchNo)
                    .set("oper_status", operStatus.toString())
                    .set("fail_reason", failReason)
                    .set("updated_time", new Date());

            int updateCount = kgBasesReportLogMapper.update(null, updateWrapper);
            if (updateCount == 0) {
                log.warn("更新上报批次状态失败，批次号: {}, 可能记录不存在", batchNo);
            } else {
                log.info("更新上报批次状态成功，批次号: {}, 状态: {}", batchNo, operStatus);
            }
        } catch (Exception e) {
            log.error("更新上报批次状态异常，批次号: {}", batchNo, e);
        }
    }

    @Override
    public ServiceResp searchKnowledgeBackGroundAndInfo() {
        return ServiceResp.success("操作成功", knowledgeBackgroundConfigMapper.selectList(new QueryWrapper<>()));
    }

    @Override
    /**
     * 搜索知识应用的有效统计信息
     *
     * 本方法用于查询知识首页中统计类型为0（有效）的知识应用统计信息，
     * 并将其转换为适合前端展示的格式返回
     *
     * @return ServiceResp 包含知识应用有效统计信息的响应对象
     */
    public ServiceResp searchKnowledgeApplicationEffective() {
        // 创建查询条件，筛选统计类型为0（有效）的记录，并按排序字段升序排列
        LambdaQueryWrapper<KgKnowledgeHomeIndexD> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KgKnowledgeHomeIndexD::getStatisticType, 0).orderByAsc(KgKnowledgeHomeIndexD::getSort);

        // 执行查询，获取符合条件的知识应用统计信息列表
        List<KgKnowledgeHomeIndexD> kgKnowledgeHomeIndexdS = knowledgeHomeIndexMapper.selectList(queryWrapper);

        // 初始化用于存储转换后的知识应用统计信息的列表
        List<KgKnowledgeHomeIndexDVm> homeIndexCountList = new ArrayList<>();

        // 如果查询结果不为空，则遍历查询结果，进行数据转换
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(kgKnowledgeHomeIndexdS)) {
            for (KgKnowledgeHomeIndexD kgKnowledgeHomeIndexD : kgKnowledgeHomeIndexdS) {
                // 创建一个新的VM对象，用于存储转换后的知识应用统计信息
                KgKnowledgeHomeIndexDVm kgKnowledgeHomeIndexdVm = new KgKnowledgeHomeIndexDVm();

                // 设置统计名称，如果名称为空字符串，则设置为null
                kgKnowledgeHomeIndexdVm.setStatisticName(StringUtils.isNotBlank(kgKnowledgeHomeIndexD.getStatisticName()) ? kgKnowledgeHomeIndexD.getStatisticName() : null);

                // 转换单位，将统计数量转换为合适的单位
                SizeInfo sizeInfo = UnitConverterUtil.convertSize(publicMapper.getPublicItems(kgKnowledgeHomeIndexD.getStatisticSql()), kgKnowledgeHomeIndexD.getUnit());
                kgKnowledgeHomeIndexdVm.setCount(sizeInfo.getValue());

                // 设置统计图片URL，如果URL为空字符串，则设置为null
                kgKnowledgeHomeIndexdVm.setStatisticPhotoUrl(StringUtils.isNotBlank(kgKnowledgeHomeIndexD.getStatisticPhotoUrl()) ? kgKnowledgeHomeIndexD.getStatisticPhotoUrl() : null);

                // 设置单位
                kgKnowledgeHomeIndexdVm.setUnit(sizeInfo.getUnit());

                // 将转换后的知识应用统计信息添加到列表中
                homeIndexCountList.add(kgKnowledgeHomeIndexdVm);
            }
        }

        // 返回包含知识应用有效统计信息的响应对象
        return ServiceResp.success("操作成功", homeIndexCountList);
    }

    /**
     * 执行SQL脚本并返回结果
     *
     * @param sqlScript SQL脚本
     * @return 返回执行结果列表
     */
    private List<Map<Integer, String>> executeRankSqlScript(String sqlScript) {
        if (StringUtils.isBlank(sqlScript)) {
            return new ArrayList<>();
        }
        
        // 使用publicMapper执行SQL脚本
        return publicMapper.executeRankSqlScript(sqlScript);
    }

    @Override
    public ServiceResp queryMajorRank() {
        LambdaQueryWrapper<KgKnowledgeHomeIndexD> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KgKnowledgeHomeIndexD::getStatisticType, 1).eq(KgKnowledgeHomeIndexD::getSort, 1).last(" limit 1");

        // 执行查询，获取符合条件的知识应用统计信息
        KgKnowledgeHomeIndexD kgKnowledgeHomeIndexD = knowledgeHomeIndexMapper.selectOne(queryWrapper);
        
        // 如果查询结果不为空，则执行SQL脚本获取结果
        List<Map<Integer, String>> majorRankList = new ArrayList<>();
        if (kgKnowledgeHomeIndexD != null && StringUtils.isNotBlank(kgKnowledgeHomeIndexD.getStatisticSql())) {
            // 执行SQL脚本并获取结果
            majorRankList = executeRankSqlScript(kgKnowledgeHomeIndexD.getStatisticSql());
        }

        return ServiceResp.success("操作成功", majorRankList);
    }

    @Override
    public ServiceResp queryLocalNetworkRank() {
        LambdaQueryWrapper<KgKnowledgeHomeIndexD> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KgKnowledgeHomeIndexD::getStatisticType, 1).eq(KgKnowledgeHomeIndexD::getSort, 2).last(" limit 1");

        // 执行查询，获取符合条件的知识应用统计信息
        KgKnowledgeHomeIndexD kgKnowledgeHomeIndexD = knowledgeHomeIndexMapper.selectOne(queryWrapper);
        
        // 如果查询结果不为空，则执行SQL脚本获取结果
        List<Map<Integer, String>> localNetworkRankList = new ArrayList<>();
        if (kgKnowledgeHomeIndexD != null && StringUtils.isNotBlank(kgKnowledgeHomeIndexD.getStatisticSql())) {
            // 执行SQL脚本并获取结果
            localNetworkRankList = executeRankSqlScript(kgKnowledgeHomeIndexD.getStatisticSql());
        }

        return ServiceResp.success("操作成功", localNetworkRankList);
    }

    /**
     * 添加或更新知识库
     *
     * @param evt 插入知识库的事件对象，包含需要添加或更新的知识库信息
     * @return 返回添加或更新后的知识库实体对象
     * @throws KnowledgeGraphException 当知识库添加或更新失败时抛出
     */
    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public KnowledgeBasesEntity addOrUpdateKnowledgeBases(InsertKnowledgeBasesEvt evt) throws Exception {

        if (evt.getKgBasesSpaceCId() == null) {
            throw new KnowledgeGraphException("请选择知识空间");
        }
        if (StringUtils.isEmpty(evt.getBasesType())) {
            // 校验标题
            checkBases(evt);
        }

        KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
        KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();

        // 替换操作词汇中的分号为逗号，以统一格式
        if (StringUtils.isNotBlank(evt.getOperativeWord())) {
            if (evt.getOperativeWord().contains(";")) {
                evt.setOperativeWord(evt.getOperativeWord().replace(";", ","));
            } else if (evt.getOperativeWord().contains("；")) {
                evt.setOperativeWord(evt.getOperativeWord().replace("；", ","));
            }
        }

        // 将事件对象属性复制到知识库实体对象
        BeanUtil.copyProperties(evt, knowledgeBases);

        // 根据文件路径设置文档类型
        if (StringUtils.isNotBlank(evt.getKnowledgeFilePath())) {
            String fileName = FileUtil.getFileName(evt.getKnowledgeFilePath());
            String fileType = FileUtil.getTypePart(fileName);
            if (StringUtils.isNotBlank(fileType)) {
                knowledgeBases.setDocumentFormat(fileType.toLowerCase());
            }
        }

        // 设置通用的属性值
        knowledgeBases.setIsDeleted(CommonConstant.IS_DELETED);
        //knowledgeBases.setAuditStatus(CommonConstant.STATUS_PASS);
        
        // 设置上报状态初始值
        if (knowledgeBases.getReportStatus() == null) {
            knowledgeBases.setReportStatus(ReportStatusConstant.NOT_REPORTED);
        }

        // 判断是否为新生成的知识库
        if (ObjectUtils.isEmpty(evt.getKgKnowledgeBasesId())) {
            // 添加默认权限
            if (commonConfig.getPerSwitch()) {
                //添加默认权限
                if (evt.getCategoryId() != null) {
                    dtPermissionConverter.initPermission(knowledgeBases, DataPermissionConstant.BASES, Long.valueOf(evt.getCategoryId()));
                }
            }
            // 设置知识库状态为"新建"
            knowledgeBases.setState(CaseStateEnum.NEW.getStateValue());

            // 检查是否存在相同名称的知识库
            LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KnowledgeBasesEntity::getKnowledgeName, evt.getKnowledgeName());
            wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
            List<KnowledgeBasesEntity> knowledgeBasesList = knowledgeBasesDMapper.selectList(wrapper);

            if (ObjectUtils.isEmpty(knowledgeBasesList)) {
                knowledgeBases.setSearchNumber(0);
                try {
                    // 存储到PG数据库
                    knowledgeBases.setCreatedUserName(PtSecurityUtils.getUsername());
                    knowledgeBasesDMapper.insert(knowledgeBases);
                    // 存储到知识空间与知识关联关系表
                    KgBasesSpaceRelationC kgBasesSpaceRelationC = new KgBasesSpaceRelationC();
                    kgBasesSpaceRelationC.setKgBasesSpaceCId(evt.getKgBasesSpaceCId());
                    kgBasesSpaceRelationC.setKgKnowledgeBasesId(knowledgeBases.getKgKnowledgeBasesId());
                    kgBasesSpaceRelationC.setBaseType(KnowModelType.KNOWLEDGE_BASE.getType());
                    kgBasesSpaceRelationCMapper.insertKgBasesSpaceRelationC(kgBasesSpaceRelationC);

                    // 存储到ES
                    BeanUtil.copyProperties(knowledgeBases, knowledgeBasesIdx);
                    extracted(knowledgeBases, knowledgeBasesIdx);
                    knowledgeBasesIdx.setFullText();
                    elasticsearchRestTemplate.save(knowledgeBasesIdx);
                } catch (Exception e) {
                    if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                        log.error("es新增失败，异常信息：{}", e.getMessage());
                        throw new KnowledgeGraphException("新增失败:{}", e.getMessage());
                    }
                }
                //文件内容存到缓存
                savefileContentRedis(evt, knowledgeBases);
                //异步查重
                if (knowledgeBases.getKgKnowledgeBasesId() != null) {
                    checkFileRepeatAndSaveToRedis(knowledgeBases.getKgKnowledgeBasesId(), false);
                }
                return knowledgeBases;
            } else {
                throw new KnowledgeGraphException(ExceptionDefinition.BASES_TITLE_RET_ERROR.getMsg());
            }
        } else {
            // 更新PG数据库中的知识库
            try {
                LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KnowledgeBasesEntity::getKnowledgeName, evt.getKnowledgeName());
                wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
                wrapper.ne(KnowledgeBasesEntity::getKgKnowledgeBasesId, evt.getKgKnowledgeBasesId());
                List<KnowledgeBasesEntity> knowledgeBasesList = knowledgeBasesDMapper.selectList(wrapper);

                if (knowledgeBasesList.size() > 0) {
                    throw new KnowledgeGraphException(ExceptionDefinition.BASES_TITLE_RET_ERROR.getMsg());
                }

                KnowledgeBasesEntity knowledgeBasesOrgianl = knowledgeBasesDMapper.selectById(evt.getKgKnowledgeBasesId());
                setOrgianlData(knowledgeBases, knowledgeBasesOrgianl);
                knowledgeBases.setUpdatedUserName(PtSecurityUtils.getUsername());
                knowledgeBasesDMapper.updateById(knowledgeBases);

                // 更新ES中的知识库
                try {
                    elasticsearchRestTemplate.delete(String.valueOf(evt.getKgKnowledgeBasesId()), KnowledgeBasesIdx.class);
                } catch (Exception ex) {
                    if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                        throw new KnowledgeGraphException("更新失败:{}", ex.getMessage());
                    }
                }
                // 复制属性
                BeanUtil.copyProperties(knowledgeBases, knowledgeBasesIdx);
                extracted(knowledgeBases, knowledgeBasesIdx);
                knowledgeBasesIdx.setFullText();
                // 保存数据
                try {
                    elasticsearchRestTemplate.save(knowledgeBasesIdx);
                } catch (Exception ex) {
                    if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                        throw new KnowledgeGraphException("更新失败:{}", ex.getMessage());
                    }
                }
                //文件内容存到缓存
                savefileContentRedis(evt, knowledgeBases);
                //异步查重
                if (knowledgeBases.getKgKnowledgeBasesId() != null) {
                    checkFileRepeatAndSaveToRedis(knowledgeBases.getKgKnowledgeBasesId(), false);
                }
                return knowledgeBases;
            } catch (Exception e) {
                log.error("更新失败：{}", e);
                log.error("更新失败：{}", e.getMessage());
                throw new KnowledgeGraphException("更新失败:{}", e.getMessage());
            }
        }
    }

    private void savefileContentRedis(InsertKnowledgeBasesEvt evt, KnowledgeBasesEntity knowledgeBases) {
        if (StrUtil.isNotBlank(evt.getFileContent())) {
            KgKnowledgeFile kgKnowledgeFile = new KgKnowledgeFile();
            kgKnowledgeFile.setKgKnowledgeBasesId(knowledgeBases.getKgKnowledgeBasesId());
            kgKnowledgeFile.setFileContent(evt.getFileContent());
            kgKnowledgeFile.setOperativeWord(evt.getKnowledgeName());
            //获取文件的后缀
            int lastDotIndex = knowledgeBases.getKnowledgeFilePath().lastIndexOf('.');
            if (lastDotIndex != -1) {
                String fileExtension = knowledgeBases.getKnowledgeFilePath().substring(lastDotIndex + 1);
                kgKnowledgeFile.setFileSuffix(fileExtension);
            }
            String prefix = KnowledgeConstant.FILE_REDIS;
            prefix = prefix + knowledgeBases.getKgKnowledgeBasesId();
            ctgRedisService.set(prefix, JSONObject.toJSONString(kgKnowledgeFile));
            log.info("文件内容存到缓存知识库名称" + knowledgeBases.getKnowledgeName());
        }
    }

    @Override
    public KnowledgeBasesInfoVm getKnowledgeBasesInfo(HttpServletRequest request, InsertKnowledgeBasesEvt evt) {
        KnowledgeBasesInfoVm knowledgeBasesInfoVm = null;
        try {
            KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesDMapper.selectById(evt.getKgKnowledgeBasesId());
            log.info("知识表:{}", JSONObject.toJSONString(knowledgeBasesEntity));
            if (knowledgeBasesEntity.getSearchNumber() == null) {
                knowledgeBasesEntity.setSearchNumber(1);
            } else {
                knowledgeBasesEntity.setSearchNumber(knowledgeBasesEntity.getSearchNumber() + 1);
            }
            if (knowledgeBasesEntity.getClickCount() == null) {
                knowledgeBasesEntity.setClickCount(1);
            } else {
                knowledgeBasesEntity.setClickCount(knowledgeBasesEntity.getClickCount() + 1);
            }
            //pg库更新
            knowledgeBasesDMapper.updateById(knowledgeBasesEntity);
            //es更新
            Map<String, Integer> map = new HashMap<>();
            map.put("searchNumber", knowledgeBasesEntity.getSearchNumber());
            String toJson = JsonUtil.objectToJson(map);
            UpdateQuery updateQuery = UpdateQuery.builder(String.valueOf(knowledgeBasesEntity.getKgKnowledgeBasesId()))
                    .withDocument(Document.parse(toJson))
                    .build();
            IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
            try {
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            } catch (Exception ex) {
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("查看知识详情失败:{}", ex);
                    throw new KnowledgeGraphException("查看知识详情失败:{}", ex.getMessage());
                }
            }
            if (knowledgeBasesEntity.getCategoryId() != null) {
                List<CommonCategoryEntity> categoryEntityList = commonCategoryMapper.selectParentInfoByCategoryId(Long.valueOf(knowledgeBasesEntity.getCategoryId().toString()));
                if (CollectionUtils.isNotEmpty(categoryEntityList)) {
                    categoryEntityList.stream().forEach(commonCategoryEntity -> {
                        if (StringUtils.isNotBlank(commonCategoryEntity.getTemplateDocumentLink())) {
                            knowledgeBasesEntity.setKnowledgeFilePath(commonCategoryEntity.getTemplateDocumentLink());
                        }
                    });
                }
            }
            LambdaQueryWrapper<DocumentEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DocumentEntity::getBusiId, evt.getKgKnowledgeBasesId())
                    .eq(DocumentEntity::getBusiType, CommonConstant.UPLOAD_BASES_TYPE);
            List<DocumentEntity> documentEntityList = documentMapper.selectList(wrapper);
            knowledgeBasesInfoVm = new KnowledgeBasesInfoVm();
            if (Objects.nonNull(knowledgeBasesEntity.getRegion())) {
                QueryRegionListVm queryRegionList = regionMapper.findRegionById(knowledgeBasesEntity.getRegion(), regionConfig.getRegionSchema());
                if (queryRegionList != null) {
                    knowledgeBasesEntity.setFullRegion(queryRegionList.getFullRegionId());
                    knowledgeBasesEntity.setRegionName(queryRegionList.getRegionName());
                }
            }
            knowledgeBasesInfoVm.setKnowledgeBasesEntity(knowledgeBasesEntity);
            knowledgeBasesInfoVm.setDocumentEntityList(documentEntityList);
            //知识点击日志表插入
            SearchClickLogEntity searchClickLogEntity = getSearchClickLogEntity(request, knowledgeBasesEntity);
            searchClickLogMapper.insert(searchClickLogEntity);
        } catch (Exception e) {
            log.error("查询详情出错", e);
            throw new KnowledgeGraphException("查看知识详情失败:{}", e.getMessage());
        }
        return knowledgeBasesInfoVm;
    }

    /**
     * 添加知识文件
     * 该方法从HTTP请求中获取描述信息和多个文件，将每个文件上传到服务器，并在数据库中记录相关信息
     * 如果所有文件都成功上传并记录，则返回true；如果有任何一个文件上传或记录失败，则返回false
     *
     * @param request        包含描述信息和文件上传请求的HTTP请求对象
     * @param multipartFiles 上传的多个文件
     * @return 如果所有文件都成功上传并记录，则返回true；否则返回false
     * @throws IOException 如果文件上传过程中发生IO异常
     */
    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public boolean addKnowledgeFile(HttpServletRequest request, MultipartFile[] multipartFiles) throws IOException {
        // 获取描述信息
        String des = request.getParameter(CommonConstant.DES);
        // 将描述信息分割成数组
        String[] dess = des.split(",");
        // 初始化索引，用于遍历描述信息数组
        int index = 0;

        // 遍历每个上传的文件
        for (MultipartFile multipartFile : multipartFiles) {
            // 获取文件原始名称
            String fileName = multipartFile.getOriginalFilename();
            // 获取文件扩展名
            String fileSuffix = FilenameUtils.getExtension(fileName);
            // 上传文件并获取服务器保存的文件路径
            FileInfo filePath = docService.uploadFile(multipartFile, CommonConstant.UPLOAD_BASES_TYPE);

            List<String> documentIds = new ArrayList<>();
            // 如果文件路径不为空，表示文件上传成功
            if (StringUtils.isNotBlank(filePath.getFilePath())) {
                if (knowledgeBaseConfig.getRagflow().isEnabled()) {
                    // 上传到ragFlow的知识空间中
                    String kgBasesSpaceCId = request.getParameter("kgBasesSpaceCId");
                    QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
                    kgBasesSpaceCQueryWrapper.eq("is_deleted", false)
                            .eq("kg_bases_space_c_id", Long.valueOf(kgBasesSpaceCId));
                    KgBasesSpaceC kgBasesSpaceC = kgBasesSpaceCMapper.selectOne(kgBasesSpaceCQueryWrapper);
                    try {
                        // 文件上传到rag
                        UploadApiResponse uploadApiResponse = uploadFileToRag(filePath.getFilePath(), kgBasesSpaceC.getRagFlowId());
                        List<DatasetResponse> data = uploadApiResponse.getData();
                        for (DatasetResponse datum : data) {
                            String documentId = datum.getId();
                            documentIds.add(documentId);
                        }
                        // 异步构建解析文档
                        DatasetCreateRequest requests = new DatasetCreateRequest();
                        requests.setDocument_ids(documentIds);
                        requests.setDatasetId(kgBasesSpaceC.getRagFlowId());
                        CompletableFuture.supplyAsync(() -> {
                            if (ObjectUtils.isNotEmpty(requests)) {
                                this.ragBuild(requests);
                            }
                            return null;
                        });
                    } catch (Exception e) {
                        throw new KnowledgeGraphException("rag文件上传异常:{}", e.getMessage());
                    }
                }

                List<String> docIds = new ArrayList<>();
                if (knowledgeBaseConfig.getRagweb().isEnabled()) {
                    // 上传到ragFlow的知识空间中
                    String kgBasesSpaceCId = request.getParameter("kgBasesSpaceCId");
                    QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
                    kgBasesSpaceCQueryWrapper.eq("is_deleted", false)
                            .eq("kg_bases_space_c_id", Long.valueOf(kgBasesSpaceCId));
                    KgBasesSpaceC kgBasesSpaceC = kgBasesSpaceCMapper.selectOne(kgBasesSpaceCQueryWrapper);
                    try {
                        // 文件上传到rag
                        docIds = this.uploadFileToRagwebDataset(filePath.getFilePath(), kgBasesSpaceC.getBasesSpaceName(), multipartFile);
                        for (String s : docIds) {
                            // 异步构建解析文档
                            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                            stringObjectHashMap.put("fileId", s);
                            stringObjectHashMap.put("fileType", kgBasesSpaceC.getBasesSpaceName());
                            if (s != null) {
                                CompletableFuture.supplyAsync(() -> {
                                    this.buildFileToRagwebDataset(stringObjectHashMap);
                                    return null;
                                });
                            }
                        }
                    } catch (Exception e) {
                        throw new KnowledgeGraphException("rag文件上传或构建异常:{}", e.getMessage());
                    }
                }

                // 创建文档实体对象，用于记录文件相关信息到数据库
                DocumentEntity documentEntity = new DocumentEntity();
                // 从请求中获取知识库ID
                String kgbsId = request.getParameter(CommonConstant.KG_KNOWLEDGE_BASES_ID);
                // 设置业务ID为知识库ID
                documentEntity.setBusiId(Long.parseLong(kgbsId));
                // 设置业务类型为上传知识类型
                documentEntity.setBusiType(CommonConstant.UPLOAD_BASES_TYPE);
                // 设置文档名称为文件原始名称
                documentEntity.setDocName(fileName);
                // 设置扩展名
                documentEntity.setExtensionName(fileSuffix);
                // 设置文档描述，从描述信息数组中获取对应索引的描述信息
                documentEntity.setDocDesc(dess[index]);
                // 设置文档URL为服务器保存的文件路径
                documentEntity.setDocUrl(filePath.getFilePath());
                // 设置上传文档到 RagFlow知识空间中的文件Id
                if (CollectionUtils.isNotEmpty(documentIds)) {
                    documentEntity.setRagFlowDocId(documentIds.get(0));
                }
                if (CollectionUtils.isNotEmpty(docIds)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < docIds.size(); i++) {
                        if (docIds.get(i) != null) {
                            stringBuilder.append(docIds.get(i));
                            // 如果不是最后一个元素，则添加逗号
                            if (i < docIds.size() - 1) {
                                stringBuilder.append(",");
                            }
                        }
                    }
                    documentEntity.setRagFlowDocId(stringBuilder.toString());
                }
                // 设置文档大小
                documentEntity.setDocSize((multipartFile.getSize()));
                // 将文档实体对象插入到数据库
                documentMapper.insert(documentEntity);
                // 索引加一，用于获取下一个描述信息
                index++;
            } else {
                // 如果文件上传失败，返回false
                return false;
            }
        }
        // 所有文件上传和记录成功，返回true
        return true;
    }

    @Override
    public KnowledgeBasesVm getKnowledgeBases(GetBasesEvt evt) {
        if (evt.getKgBasesSpaceCId() == null) {
            return null;
        }
        
        // 设置用户信息
        if (StringUtil.isEmpty(evt.getBeforeOneUser())) {
            evt.setBeforeOneUser(PtSecurityUtils.getUsername());
        }
        
        // 处理操作词
        if (StringUtils.isNotBlank(evt.getOperativeWord())) {
            evt.setOperativeWordList(new ArrayList<>(Arrays.asList(evt.getOperativeWord().split(";"))));
        }
        
        // 处理区域信息
        if (CollectionUtil.isNotEmpty(evt.getCurrentUserList()) && CollectionUtil.isEmpty(evt.getRegion())) {
            evt.setRegion(evt.getCurrentUserList().stream().map(Integer::parseInt).collect(Collectors.toList()));
        }
        
        String username = PtSecurityUtils.getUsername();
        
        // 处理审核规则
        handleAuditRules(evt, username);
        
        // 处理时间范围
        if (CollectionUtil.isNotEmpty(evt.getCreatedTimeList())) {
            evt.setStartTime(evt.getCreatedTimeList().get(0));
            evt.setEndTime(evt.getCreatedTimeList().get(1));
        }
        
        // 获取知识空间关联关系 - 只查询一次
        QueryWrapper<KgBasesSpaceRelationC> relationQueryWrapper = new QueryWrapper<>();
        relationQueryWrapper.eq("kg_bases_space_c_id", evt.getKgBasesSpaceCId())
                .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                .eq("is_deleted", false);
        List<KgBasesSpaceRelationC> kgBasesSpaceRelationCS = kgBasesSpaceRelationCMapper.selectList(relationQueryWrapper);
        
        List<Long> kgKnowledgeBasesIds = kgBasesSpaceRelationCS.stream()
                .map(KgBasesSpaceRelationC::getKgKnowledgeBasesId)
                .collect(Collectors.toList());
        
        // 如果指定了currentSpaceId，需要过滤掉该空间已关联的知识库
        if (evt.getCurrentSpaceId() != null) {
            log.info("需要过滤掉知识空间ID：{} 已关联的知识库", evt.getCurrentSpaceId());
            
            // 查询currentSpaceId关联的知识库ID列表
            QueryWrapper<KgBasesSpaceRelationC> currentSpaceRelationQueryWrapper = new QueryWrapper<>();
            currentSpaceRelationQueryWrapper.eq("kg_bases_space_c_id", evt.getCurrentSpaceId())
                    .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                    .eq("is_deleted", false);
            List<KgBasesSpaceRelationC> currentSpaceRelationList = kgBasesSpaceRelationCMapper.selectList(currentSpaceRelationQueryWrapper);
            
            // 获取需要排除的知识库ID列表
            List<Long> excludeKnowledgeBasesIds = currentSpaceRelationList.stream()
                    .map(KgBasesSpaceRelationC::getKgKnowledgeBasesId)
                    .collect(Collectors.toList());
            
            if (CollectionUtils.isNotEmpty(excludeKnowledgeBasesIds)) {
                // 从结果中排除这些已关联的知识库
                int originalSize = kgKnowledgeBasesIds.size();
                kgKnowledgeBasesIds = kgKnowledgeBasesIds.stream()
                        .filter(id -> !excludeKnowledgeBasesIds.contains(id))
                        .collect(Collectors.toList());
                
                log.info("过滤前知识库数量：{}，过滤后知识库数量：{}，已排除{}个知识库", 
                        originalSize, kgKnowledgeBasesIds.size(), excludeKnowledgeBasesIds.size());
            } else {
                log.info("知识空间ID：{} 暂无关联的知识库，无需过滤", evt.getCurrentSpaceId());
            }
        }
        
        evt.setKgKnowledgeBasesIds(kgKnowledgeBasesIds);
        
        if (CollectionUtils.isEmpty(kgKnowledgeBasesIds)) {
            return null;
        }
        
        // 分页查询知识库列表
        PageInfo<KnowledgeBasesEntity> pageInfo;
        List<KnowledgeBasesEntity> allKnowledgeBasesList;
        
        if (StringUtil.isEmpty(evt.getFullText())) {
            PageHelper.startPage(evt.getPageNo(), evt.getPageSize());
            List<KnowledgeBasesEntity> pageKnowledgeBasesList = knowledgeBasesDMapper.selectBasesListByHighLevel(evt);
            
            // 处理操作词格式
            for (KnowledgeBasesEntity entity : pageKnowledgeBasesList) {
                if (StringUtils.isNotBlank(entity.getOperativeWord()) && entity.getOperativeWord().contains(",")) {
                    entity.setOperativeWord(entity.getOperativeWord().replace(",", ";"));
                }
            }
            
            pageInfo = new PageInfo<>(pageKnowledgeBasesList);
            
            // 查询所有知识库（不分页）- 用于统计
            allKnowledgeBasesList = knowledgeBasesDMapper.selectBasesListByHighLevel(evt);
        } else {
            // 全文搜索
            pageInfo = esKnowSearchService.getCasesOnFullTextWithPage(evt);
            allKnowledgeBasesList = esKnowSearchService.getCasesOnFullTextWithoutPage(evt);
        }
        
        // 创建返回对象
        KnowledgeBasesVm knowledgeBasesVm = new KnowledgeBasesVm();
        
        // 收集需要查询的ID
        List<Long> kgbsIdsList = allKnowledgeBasesList.stream()
                .map(KnowledgeBasesEntity::getKgKnowledgeBasesId)
                .collect(Collectors.toList());
        
        // 统计专业信息 - 一次性查询
        List<MajorStatisticsVm> allMajorStatisticsList = new ArrayList<>();
        Integer majorTotal = 0;
        if (CollectionUtil.isNotEmpty(kgbsIdsList)) {
            allMajorStatisticsList = knowledgeBasesDMapper.selectAllMajorStatistics(kgbsIdsList);
            majorTotal = knowledgeBasesDMapper.selectMajorStatistics(kgbsIdsList);
        }
        knowledgeBasesVm.setAllMajorStatisticsList(allMajorStatisticsList);
        knowledgeBasesVm.setMajorTotal(majorTotal);
        
        // 批量查询分类信息
        enrichCategoryInfo(pageInfo.getList());
        
        // 批量查询区域信息
        enrichRegionInfo(pageInfo.getList());
        
        // 处理文件大小显示
        for (KnowledgeBasesEntity entity : pageInfo.getList()) {
            if (Objects.nonNull(entity.getFilesize())) {
                SizeInfo sizeInfo = UnitConverterUtil.convertSize(entity.getFilesize(), SizeUnit.B.name());
                entity.setFilesizeShow(sizeInfo.getValue() + " " + sizeInfo.getUnit());
            }
        }
        
        // 权限转换
        DtPermissionConverter.permissionConvert(pageInfo.getList(), evt);
        knowledgeBasesVm.setKnowledgeBasesEntityPageInfo(pageInfo);
        return knowledgeBasesVm;
    }

    // 提取审核规则处理逻辑为单独方法
    private void handleAuditRules(GetBasesEvt evt, String username) {
        if (evt.getState() != null && ((evt.getWhetherAuditOrder() != null && evt.getState().contains(CaseStateEnum.PENDING_AUDIT.getStateValue()))
                || evt.getWhetherOnline() != null)) {
            KgAuditorAllocationRuleConfigD ruleConfig = auditorAllocationRuleConfigMapper.selectById(BigInteger.ONE);
            
            if (evt.getType() == null || (evt.getType() != null && !CommonConstant.YES.equals(evt.getType()))) {
                if (StringUtils.isNotBlank(username)) {
                    KgBasesAuditPersonDEvt personEvt = new KgBasesAuditPersonDEvt();
                    personEvt.setRealName(username);
                    List<KgBasesAuditPersonDVm> auditPersons = basesAuditPersonMapper.getBasesAuditPerson(personEvt);
                    
                    if (CollectionUtil.isNotEmpty(auditPersons)) {
                        KgBasesAuditPersonDVm auditPerson = auditPersons.get(0);
                        //省级管理员不做限制
                        if (CommonConstant.NO_PASS.equals(auditPerson.getWhetherProvinceAdmin())) {
                            if (!CommonConstant.PASS.equals(auditPerson.getWhetherRegionAdmin())) {
                                //不是区域管理员，就需要通过分配规则过滤数据
                                switch (ruleConfig.getValue()) {
                                    case "1":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(auditPerson.getMajor().split(","))));
                                        break;
                                    case "2":
                                        evt.setStateRegion(auditPerson.getRegionList().stream().map(Integer::valueOf).collect(Collectors.toList()));
                                        break;
                                    case "3":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(auditPerson.getMajor().split(","))));
                                        evt.setStateRegion(auditPerson.getRegionList().stream().map(Integer::valueOf).collect(Collectors.toList()));
                                        break;
                                }
                            } else {
                                //是区域管理员，只需要过滤区域，不受专业限制
                                evt.setStateRegion(auditPerson.getRegionList().stream().map(Integer::valueOf).collect(Collectors.toList()));
                            }
                        }
                    }
                }
            }
        }
    }

    // 提取分类信息查询为单独方法
    private void enrichCategoryInfo(List<KnowledgeBasesEntity> knowledgeBasesList) {
        List<Long> categoryIdList = knowledgeBasesList.stream()
                .filter(fi -> Objects.nonNull(fi.getCategoryId()))
                .map(KnowledgeBasesEntity::getCategoryId)
                .collect(Collectors.toList());
        
        if (CollectionUtil.isNotEmpty(categoryIdList)) {
            QueryWrapper<CommonCategoryEntity> categoryQueryWrapper = new QueryWrapper<>();
            categoryQueryWrapper.eq("category_type", "BASES").in("category_id", categoryIdList);
            List<CommonCategoryEntity> categoryEntityList = commonCategoryMapper.selectList(categoryQueryWrapper);
            
            if (CollectionUtils.isNotEmpty(categoryEntityList)) {
                Map<Long, CommonCategoryEntity> categoryEntityMap = CommonListToMapUtils.listToMap(
                        categoryEntityList, CommonCategoryEntity::getCategoryId);
                
                for (KnowledgeBasesEntity entity : knowledgeBasesList) {
                    if (entity.getCategoryId() != null && categoryEntityMap.containsKey(entity.getCategoryId())) {
                        entity.setCategoryName(categoryEntityMap.get(entity.getCategoryId()).getCategoryName());
                    }
                }
            }
        }
    }

    // 提取区域信息查询为单独方法
    private void enrichRegionInfo(List<KnowledgeBasesEntity> knowledgeBasesList) {
        List<Long> regionIds = knowledgeBasesList.stream()
                .filter(fi -> Objects.nonNull(fi.getRegion()))
                .map(KnowledgeBasesEntity::getRegion)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(regionIds)) {
            List<QueryRegionListVm> regions = regionMapper.findRegionByIds(regionIds, regionConfig.getRegionSchema());
            Map<Long, String> regionMap = regions.stream()
                    .collect(Collectors.toMap(
                            QueryRegionListVm::getRegionId,
                            QueryRegionListVm::getRegionName,
                            (v1, v2) -> v1
                    ));
            
            for (KnowledgeBasesEntity entity : knowledgeBasesList) {
                if (entity.getRegion() != null && regionMap.containsKey(entity.getRegion())) {
                    entity.setRegionName(regionMap.get(entity.getRegion()));
                }
            }
        }
    }

    /**
     * 根据ID列表删除知识库
     * 此方法遍历给定的ID列表，针对每个ID，检查其审核状态，并根据状态执行删除操作
     * 如果审核状态为空，则从数据库和Elasticsearch中删除相应的记录，并删除关联的文档
     * 如果审核状态不为空，则更新Elasticsearch中的记录状态，并更新数据库中的相关记录
     *
     * @param evt 包含要删除的ID列表的事件对象
     * @return 总是返回true，如果删除过程中发生异常，则抛出KnowledgeGraphException
     * @throws KnowledgeGraphException 如果删除过程中遇到除特定错误之外的任何异常
     */
    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public boolean deleteKgbsByIds(KgBsIdsEvt evt) {
        // 获取要删除的ID列表
        List<Long> ids = evt.getIds();
        try {
            boolean jtEnableds = false;
            // 批量删除并下线知识库
            if (CollectionUtil.isNotEmpty(ids) && "1".equals(evt.getDelStatus())) {
                log.info("开始调用集团下线接口");
                jtEnableds = callGroupDocApi(ids, "offLine", evt.getOperUserId(), "0");
                log.info("开始调用集团删除接口");
                jtEnableds = callGroupDocApi(ids, "delete", evt.getOperUserId(), "0");
                if (jtEnableds) {
                    KnowledgeBasesEntity knowledgeBasesEntity = new KnowledgeBasesEntity();
                    QueryWrapper<KnowledgeBasesEntity> knowledgeBasesEntityQueryWrapper = new QueryWrapper<>();
                    knowledgeBasesEntityQueryWrapper.in("kg_knowledge_bases_id", ids);
                    knowledgeBasesEntity.setReportStatus(evt.getReportStatus());
                    knowledgeBasesDMapper.update(knowledgeBasesEntity, knowledgeBasesEntityQueryWrapper);
                }
                return true;
            }
        } catch (Exception e) {
            log.error("批量删除并下线知识库失败：{}", e.getMessage());
            throw new KnowledgeGraphException("批量删除或下线知识库失败:{}", e.getMessage());
        }

        //批量删除缓存
        try {
            if (CollUtil.isNotEmpty(ids)) {
                for (Long id : ids) {
                    ctgRedisService.remove(KnowledgeConstant.FILE_REDIS + id);
                    log.info("删除知识库文件内容缓存成功redis key" + KnowledgeConstant.FILE_REDIS + id);
                }
            }
        } catch (Exception e) {
            log.error("批量删除知识库文件缓存失败：{}", e.getMessage());
            throw new KnowledgeGraphException("批量删除知识库文件缓存失败:{}", e.getMessage());
        }
        for (Long id : ids) {
            // 创建查询条件以获取知识库实体
            LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KnowledgeBasesEntity::getKgKnowledgeBasesId, id);
            KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesDMapper.selectOne(wrapper);
            // 检查知识库实体的审核状态
            if (StringUtils.isEmpty(knowledgeBasesEntity.getAuditStatus())) {
                try {
                    // 创建查询条件以获取关联的文档实体列表
                    LambdaQueryWrapper<DocumentEntity> documentWrapper = new LambdaQueryWrapper<>();
                    documentWrapper.eq(DocumentEntity::getBusiType, CommonConstant.UPLOAD_BASES_TYPE)
                            .eq(DocumentEntity::getBusiId, id);
                    List<DocumentEntity> documentEntities = documentMapper.selectList(documentWrapper);
                    // 遍历文档实体列表并删除每个文档
                    if (CollectionUtil.isNotEmpty(documentEntities)) {
                        for (DocumentEntity documentEntity : documentEntities) {
                            if (!ObjectUtils.isEmpty(documentEntity.getDocUrl())) {
                                docService.deleteFile(documentEntity.getDocUrl());
                            }
                            if (knowledgeBaseConfig.getRagflow().isEnabled()) {
                                QueryWrapper<KgBasesSpaceRelationC> kgBasesSpaceRelationCQueryWrapper = new QueryWrapper<>();
                                kgBasesSpaceRelationCQueryWrapper.eq("kg_knowledge_bases_id", id)
                                        .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                                        .eq("is_deleted", false);
                                KgBasesSpaceRelationC kgBasesSpaceRelationC = kgBasesSpaceRelationCMapper.selectOne(kgBasesSpaceRelationCQueryWrapper);

                                QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
                                kgBasesSpaceCQueryWrapper.eq("kg_bases_space_c_id", kgBasesSpaceRelationC.getKgBasesSpaceCId())
                                        .eq("is_deleted", false);
                                KgBasesSpaceC kgBasesSpaceC = kgBasesSpaceCMapper.selectOne(kgBasesSpaceCQueryWrapper);
                                List<String> docIds = new ArrayList<>();
                                String ragFlowDocId = documentEntity.getRagFlowDocId();
                                docIds.add(ragFlowDocId);
                                DatasetCreateRequest request = new DatasetCreateRequest();
                                request.setIds(docIds);
                                request.setRagFlowId(kgBasesSpaceC.getRagFlowId());
                                this.delRagFlowSpaceFile(request);
                            }

                            if (knowledgeBaseConfig.getRagweb().isEnabled()) {
                                QueryWrapper<KgBasesSpaceRelationC> kgBasesSpaceRelationCQueryWrapper = new QueryWrapper<>();
                                kgBasesSpaceRelationCQueryWrapper.eq("kg_knowledge_bases_id", id)
                                        .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                                        .eq("is_deleted", false);
                                KgBasesSpaceRelationC kgBasesSpaceRelationC = kgBasesSpaceRelationCMapper.selectOne(kgBasesSpaceRelationCQueryWrapper);

                                QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
                                kgBasesSpaceCQueryWrapper.eq("kg_bases_space_c_id", kgBasesSpaceRelationC.getKgBasesSpaceCId())
                                        .eq("is_deleted", false);
                                KgBasesSpaceC kgBasesSpaceC = kgBasesSpaceCMapper.selectOne(kgBasesSpaceCQueryWrapper);
                                String ragFlowDocId = documentEntity.getRagFlowDocId();
                                HashMap<String, Object> stringStringHashMap = new HashMap<>();
                                stringStringHashMap.put("fileType", kgBasesSpaceC.getBasesSpaceName());
                                if (ragFlowDocId.contains(",")) {
                                    String[] split = ragFlowDocId.split(",");
                                    for (String s : split) {
                                        stringStringHashMap.put("file_id", s);
                                        this.delFileToRagwebDataset(stringStringHashMap);
                                    }
                                } else {
                                    stringStringHashMap.put("file_id", ragFlowDocId);
                                    this.delFileToRagwebDataset(stringStringHashMap);
                                }

                            }
                        }
                    }

                    // 从数据库中删除知识库实体
                    knowledgeBasesDMapper.deleteById(id);
                    QueryWrapper<KgBasesSpaceRelationC> kgBasesSpaceRelationCQueryWrapper1 = new QueryWrapper<>();
                    kgBasesSpaceRelationCQueryWrapper1.eq("kg_knowledge_bases_id", id)
                            .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                            .eq("is_deleted", false);
                    KgBasesSpaceRelationC kgBasesSpaceRelationC1 = kgBasesSpaceRelationCMapper.selectOne(kgBasesSpaceRelationCQueryWrapper1);
                    kgBasesSpaceRelationC1.setIsDeleted(true);
                    kgBasesSpaceRelationCMapper.updateById(kgBasesSpaceRelationC1);
                    try {
                        // 从Elasticsearch中删除对应的索引
                        elasticsearchRestTemplate.delete(String.valueOf(id), KnowledgeBasesIdx.class);
                    } catch (Exception ex) {
                        // 处理Elasticsearch删除操作中可能发生的异常
                        if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                            throw new KnowledgeGraphException("删除失败:{}", ex.getMessage());
                        }
                    }
                } catch (Exception e) {
                    // 处理删除过程中可能发生的异常
                    log.error("删除失败：{}", e.getMessage());
                    throw new KnowledgeGraphException("删除失败:{}", e.getMessage());
                }
            } else {
                try {
                    // 创建更新查询以更新Elasticsearch中的记录状态
                    UpdateQuery updateQuery = UpdateQuery.builder(String.valueOf(id))
                            .withDocument(Document.parse("{\"isDeleted\": \"1\"}"))
                            .build();
                    IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                    try {
                        // 执行Elasticsearch更新操作
                        elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
                    } catch (Exception ex) {
                        // 处理Elasticsearch更新操作中可能发生的异常
                        if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                            throw new KnowledgeGraphException("删除更新失败:{}", ex.getMessage());
                        }
                    }

                    if (knowledgeBaseConfig.getRagflow().isEnabled()) {
                        QueryWrapper<DocumentEntity> documentEntityQueryWrapper = new QueryWrapper<>();
                        documentEntityQueryWrapper.eq("busi_id", id);
                        List<DocumentEntity> documentEntities = documentMapper.selectList(documentEntityQueryWrapper);
                        for (DocumentEntity documentEntity : documentEntities) {
                            QueryWrapper<KgBasesSpaceRelationC> kgBasesSpaceRelationCQueryWrapper = new QueryWrapper<>();
                            kgBasesSpaceRelationCQueryWrapper.eq("kg_knowledge_bases_id", id)
                                    .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                                    .eq("is_deleted", false);
                            KgBasesSpaceRelationC kgBasesSpaceRelationC = kgBasesSpaceRelationCMapper.selectOne(kgBasesSpaceRelationCQueryWrapper);

                            QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
                            kgBasesSpaceCQueryWrapper.eq("kg_bases_space_c_id", kgBasesSpaceRelationC.getKgBasesSpaceCId())
                                    .eq("is_deleted", false);
                            KgBasesSpaceC kgBasesSpaceC = kgBasesSpaceCMapper.selectOne(kgBasesSpaceCQueryWrapper);
                            List<String> docIds = new ArrayList<>();
                            String ragFlowDocId = documentEntity.getRagFlowDocId();
                            docIds.add(ragFlowDocId);
                            DatasetCreateRequest request = new DatasetCreateRequest();
                            request.setIds(docIds);
                            request.setRagFlowId(kgBasesSpaceC.getRagFlowId());
                            this.delRagFlowSpaceFile(request);
                        }
                    }

                    if (knowledgeBaseConfig.getRagweb().isEnabled()) {
                        QueryWrapper<DocumentEntity> documentEntityQueryWrapper = new QueryWrapper<>();
                        documentEntityQueryWrapper.eq("busi_id", id);
                        List<DocumentEntity> documentEntities = documentMapper.selectList(documentEntityQueryWrapper);
                        for (DocumentEntity documentEntity : documentEntities) {

                            KgBasesSpaceRelationC kgBasesSpaceRelationC = kgBasesSpaceRelationCMapper.selectkgBasesSpaceRelationC(id,KnowModelType.KNOWLEDGE_BASE.getType());

                            QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
                            kgBasesSpaceCQueryWrapper.eq("kg_bases_space_c_id", kgBasesSpaceRelationC.getKgBasesSpaceCId())
                                    .eq("is_deleted", false);
                            KgBasesSpaceC kgBasesSpaceC = kgBasesSpaceCMapper.selectOne(kgBasesSpaceCQueryWrapper);
                            String ragFlowDocId = documentEntity.getRagFlowDocId();
                            HashMap<String, Object> stringStringHashMap = new HashMap<>();
                            stringStringHashMap.put("fileType", kgBasesSpaceC.getBasesSpaceName());
                            if (ragFlowDocId.contains(",")) {
                                String[] split = ragFlowDocId.split(",");
                                for (String s : split) {
                                    stringStringHashMap.put("file_id", s);
                                    this.delFileToRagwebDataset(stringStringHashMap);
                                }
                            } else {
                                stringStringHashMap.put("file_id", ragFlowDocId);
                                this.delFileToRagwebDataset(stringStringHashMap);
                            }
                        }
                    }

                    QueryWrapper<KgBasesSpaceRelationC> kgBasesSpaceRelationCQueryWrapper1 = new QueryWrapper<>();
                    kgBasesSpaceRelationCQueryWrapper1.eq("kg_knowledge_bases_id", id)
                            .eq("base_type",KnowModelType.KNOWLEDGE_BASE.getType())
                            .eq("is_deleted", false);
                    KgBasesSpaceRelationC kgBasesSpaceRelationC1 = kgBasesSpaceRelationCMapper.selectOne(kgBasesSpaceRelationCQueryWrapper1);
                    kgBasesSpaceRelationC1.setIsDeleted(true);
                    kgBasesSpaceRelationCMapper.update(kgBasesSpaceRelationC1, kgBasesSpaceRelationCQueryWrapper1);

                    knowledgeBasesDMapper.updateBykgBasesId(id);

                } catch (Exception e) {
                    // 处理更新过程中可能发生的异常
                    log.error("删除更新失败：{}", e.getMessage());
                    throw new KnowledgeGraphException("删除更新失败:{}", e.getMessage());
                }
            }
        }
        return true;
    }

    private SearchClickLogEntity getSearchClickLogEntity(HttpServletRequest request, KnowledgeBasesEntity knowledgeBasesEntity) {
        SearchClickLogEntity searchClickLogEntity = new SearchClickLogEntity();
        searchClickLogEntity.setBusinessId(knowledgeBasesEntity.getKgKnowledgeBasesId());
        searchClickLogEntity.setBusinessType(CommonConstant.BUSI_TYPE);
        searchClickLogEntity.setIp(request.getRemoteAddr());
        searchClickLogEntity.setMark(knowledgeBasesEntity.getKnowledgeName());
        searchClickLogEntity.setOperateTime(new Date());
        searchClickLogEntity.setTenantCode(tenantCode);
        searchClickLogEntity.setOperateGoal(CommonConstant.GOAL_INFO);
        searchClickLogEntity.setOperateUser(PtSecurityUtils.getCurrentUserLogin().orElse("system"));
        return searchClickLogEntity;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public boolean downLoadFileDocument(HttpServletResponse response, DownLoadDocEvt evt) {
        DocumentEntity documentEntity = documentMapper.selectById(evt.getKgDocId());
        ctdfsService.downloadFile(evt.getDocUrl(), documentEntity.getDocName(), response);
        return true;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp topUpBases(List<KnowledgeBasesEntity> evt) {
        if (CollectionUtil.isNotEmpty(evt)) {
            evt.stream().forEach(knowledgeBasesEntity -> {
                knowledgeBasesEntity.setTopUp(CommonConstant.YES);
                knowledgeBasesEntity.setUpdatedTime(new Date());
                knowledgeBasesEntity.setUpdatedUserName(PtSecurityUtils.getUsername());
                knowledgeBasesDMapper.updateById(knowledgeBasesEntity);
                KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
                BeanUtil.copyProperties(knowledgeBasesEntity, knowledgeBasesIdx);

                ObjectMapper mapper = new ObjectMapper();
                mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
                String jsonString = null;
                try {
                    jsonString = mapper.writeValueAsString(knowledgeBasesIdx);
                    UpdateQuery updateQuery = UpdateQuery.builder(String.valueOf(knowledgeBasesEntity.getKgKnowledgeBasesId()))
                            .withDocument(Document.parse(jsonString))
                            .build();
                    IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                    try {
                        elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
                    } catch (Exception ex) {
                        if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                            log.error("修改索引失败:{}", ex);
                            throw new KnowledgeGraphException("修改索引失败:{}", ex.getMessage());
                        }
                    }
                } catch (JsonProcessingException e) {
                    log.error("知识库索引转化失败", e);
                }
            });
        }
        return ServiceResp.success("操作成功");
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp cancelTopUpBases(List<KnowledgeBasesEntity> evt) {
        if (CollectionUtil.isNotEmpty(evt)) {
            evt.stream().forEach(knowledgeBasesEntity -> {
                knowledgeBasesEntity.setTopUp(CommonConstant.NO);
                knowledgeBasesEntity.setUpdatedTime(new Date());
                knowledgeBasesEntity.setUpdatedUserName(PtSecurityUtils.getUsername());
                knowledgeBasesDMapper.updateById(knowledgeBasesEntity);
                KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
                BeanUtil.copyProperties(knowledgeBasesEntity, knowledgeBasesIdx);

                ObjectMapper mapper = new ObjectMapper();
                mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
                String jsonString = null;
                try {
                    jsonString = mapper.writeValueAsString(knowledgeBasesIdx);
                    UpdateQuery updateQuery = UpdateQuery.builder(String.valueOf(knowledgeBasesEntity.getKgKnowledgeBasesId()))
                            .withDocument(Document.parse(jsonString))
                            .build();
                    IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                    try {
                        elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
                    } catch (Exception ex) {
                        if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                            log.error("修改索引失败:{}", ex);
                            throw new KnowledgeGraphException("修改索引失败:{}", ex.getMessage());
                        }
                    }
                } catch (JsonProcessingException e) {
                    log.error("知识库索引转化失败", e);
                }
            });
        }
        return ServiceResp.success("操作成功");
    }

    @Override
    public PageInfo<List<KnowledgeBasesEntity>> getBasesByAuthor(AuthorEvt evt) {
        Pageable pageable = PageRequest.of(evt.getPageNo(), evt.getPageSize());
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termsQuery("author", evt.getAuthor()))
                        .filter(QueryBuilders.termsQuery("isDeleted", CommonConstant.NO))
                        .filter(QueryBuilders.termsQuery("auditStatus", CommonConstant.YES)))
                .withPageable(pageable)
                .build();
        SearchHits<KnowledgeBasesIdx> searchHits = elasticsearchRestTemplate.search(query, KnowledgeBasesIdx.class);
        List<SearchHit<KnowledgeBasesIdx>> searchHitsList = searchHits.getSearchHits();
        List<KnowledgeBasesEntity> knowledgeBasesEntities = new ArrayList<>();
        for (SearchHit<KnowledgeBasesIdx> knowledgeBasesIdxSearchHit : searchHitsList) {
            KnowledgeBasesIdx content = knowledgeBasesIdxSearchHit.getContent();
            KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
            BeanUtil.copyProperties(content, knowledgeBases);
            knowledgeBasesEntities.add(knowledgeBases);
        }
        PageInfo pageInfo = new PageInfo(knowledgeBasesEntities);
        pageInfo.setTotal(searchHits.getTotalHits());
        return pageInfo;
    }

    @Override
    public List<DocumentEntity> getDocumentsByKgBaseId(GetBasesDocumentsEvt evt) {
        LambdaQueryWrapper<DocumentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentEntity::getBusiId, evt.getKgBaseId())
                .eq(DocumentEntity::getBusiType, CommonConstant.UPLOAD_BASES_TYPE);
        List<DocumentEntity> documentEntities = documentMapper.selectList(wrapper);
        return documentEntities;
    }

    @Override
    public void downLoadBaseManualExcel(HttpServletResponse response) throws IOException {
        ExcelData data = new ExcelData();
        String fileName = CommonConstant.BASES_NAME;
        String[] head = CommonConstant.BASES_HEADS;
        data.setHead(head);
        data.setFileName(fileName);
        ExcelUtil.orderExportExcel(response, data);
    }

    @Override
    public void downLoadBasesManualDocx(HttpServletResponse response) throws IOException {
        // 创建文档对象
        XWPFDocument document = new XWPFDocument();
        // 添加标题并设置样式
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("关于XXXXX故障处置的知识");
        titleRun.setFontSize(22);
        // 设置标题字体样式
        titleRun.setFontFamily("方正小标宋简体");
        // 添加摘要并设置样式
        XWPFParagraph abstractParagraph = document.createParagraph();
        XWPFRun abstractRun = abstractParagraph.createRun();
        abstractRun.setText("【摘要】");
        // 设置摘要字体样式
        getWordStyle(abstractRun);
        // 添加摘要（）并设置样式
        XWPFRun abstractRunChild = abstractParagraph.createRun();
        abstractRunChild.setText("（50-200字，对知识的整体情况给予概要介绍，起到了解知识大致内容的目的）");
        // 设置摘要字体样式
        extracted(abstractRunChild);
        // 添加关键词并设置样式
        XWPFParagraph keywordsParagraph = document.createParagraph();
        XWPFRun keywordsRun = keywordsParagraph.createRun();
        keywordsRun.setText("【关键词】");
        // 设置关键词字体样式
        getWordStyle(keywordsRun);
        // 添加关键词并设置样式
        XWPFRun keywordsRunChild = keywordsParagraph.createRun();
        keywordsRunChild.setText("（5个词以内，用逗号隔开；突出知识的主题，便于检索、学习和使用）");
        // 设置关键词字体样式
        extracted(keywordsRunChild);

        // 添加一级标题并设置样式
        XWPFParagraph section1Paragraph = document.createParagraph();
        section1Paragraph.setStyle("Heading1");
        XWPFRun section1Run = section1Paragraph.createRun();
        section1Run.setText("一、业务/网络情况介绍");
        // 设置一级标题字体样式
        getWordStyle(section1Run);
        XWPFRun section1RunParagraphChild = section1Paragraph.createRun();
        section1RunParagraphChild.setText("（故障所属业务/网络的情况介绍，包括业务/网络结构、系统/设备厂家、网络拓扑、运行年限等）");
        // 设置一级标题字体样式
        extracted(section1RunParagraphChild);

        // 添加二级标题并设置样式
        XWPFParagraph section2Paragraph = document.createParagraph();
        section2Paragraph.setStyle("Heading2");
        XWPFRun section2Run = section2Paragraph.createRun();
        section2Run.setText("二、故障情况");
        // 设置二级标题字体样式
        getWordStyle(section2Run);

        section2Paragraph.setStyle("Heading2");
        XWPFRun section2RunParagraphChild = section2Paragraph.createRun();
        section2RunParagraphChild.setText("（对故障情况的详细介绍，包括必要的测试结果、分析处置过程、图片说明等）");
        // 设置二级标题字体样式
        extracted(section2RunParagraphChild);

        // 添加故障情况内容
        XWPFParagraph SecneParagraph = document.createParagraph();
        XWPFRun secneRun = SecneParagraph.createRun();
        secneRun.setText("1、故障场景");
        getWordStyle(secneRun);
        XWPFRun runOne = SecneParagraph.createRun();
        runOne.setText("（业务/云网故障情况说明，故障影响及舆情影响等）");
        extracted(runOne);

        XWPFParagraph reasonParagraph = document.createParagraph();
        XWPFRun reasonParagraphRun = reasonParagraph.createRun();
        reasonParagraphRun.setText("2、原因分析");
        getWordStyle(reasonParagraphRun);
        XWPFRun runTwo = reasonParagraph.createRun();
        runTwo.setText("（分析故障发生的原因）");
        extracted(runTwo);
        XWPFParagraph processParagraph = document.createParagraph();
        XWPFRun processParagraphRun = processParagraph.createRun();
        processParagraphRun.setText("3、处置过程");
        getWordStyle(processParagraphRun);
        XWPFRun runThree = processParagraph.createRun();
        runThree.setText("（故障发生及闭环处置过程、信息公布及舆情处置过程、客户服务及投诉处理等）");
        extracted(runThree);

        // 添加三级标题并设置样式
        XWPFParagraph section3Paragraph = document.createParagraph();
        section3Paragraph.setStyle("Heading3");
        XWPFRun section3Run = section3Paragraph.createRun();
        section3Run.setText("三、思考与启示");
        // 设置三级标题字体样式
        getWordStyle(section3Run);

        XWPFRun section3RunParagraphChild = section3Paragraph.createRun();
        section3RunParagraphChild.setText("（举一反三，故障产生的深层次原因，如网络或业务规划、流程或管理等方面问题，提出可供全网借鉴的网络或业务架构优化、规范处置、应急预案、支撑手段完善及流程机制优化等方面的建议）");
        // 设置三级标题字体样式
        section3RunParagraphChild.setFontFamily("仿宋_GB2312");
        section3RunParagraphChild.setFontSize(16);
        section3RunParagraphChild.setColor("808080");

        response.reset();
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("知识库模板_V1.0.docx", "UTF-8"));
        ServletOutputStream outputStream = response.getOutputStream();
        document.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    @Override
    public PageInfo<KnowledgeBasesEntity> getBasesOnFullText(GetBasesEvt evt) {

        Pageable pageable = PageRequest.of(evt.getPageNo() - 1, evt.getPageSize());
        NativeSearchQuery searchQuery = null;

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("auditStatus", CommonConstant.YES))
                .filter(QueryBuilders.termsQuery("isDeleted", CommonConstant.NO));
        if (StringUtil.isNotEmpty(evt.getFullText())) {
            boolQueryBuilder.must(QueryBuilders.multiMatchQuery(evt.getFullText(), "fullText")
                    .type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX)
                    .slop(0));
        }
        if (CollectionUtils.isNotEmpty(evt.getMajor())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("major", evt.getMajor()));
        }
        if (CollectionUtils.isNotEmpty(evt.getRegion())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("region", evt.getRegion()));
        }

        if (CollectionUtils.isNotEmpty(evt.getState())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("state", evt.getState()));
        }
        //拼接es权限的查询条件
        if (StrUtil.isNotBlank(evt.getUserType()) && !evt.getUserType().equals(CommonConstant.YES)) {
//            DtPermissionConverter.esPermission(evt.getUserType(), boolQueryBuilder, evt.getBeforeOneUser(), evt.getPerUserNames());
        }
        searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withPageable(pageable)
                .withSort(CommonConstant.YES_INT.equals(evt.getWhetherClickCountSort()) ? SortBuilders.fieldSort("clickCount").order(SortOrder.DESC) : SortBuilders.fieldSort("topUp").order(SortOrder.DESC))
                .withSort(SortBuilders.fieldSort("updatedTime").order(SortOrder.DESC))
                .build();

        SearchHits<KnowledgeBasesIdx> searchHits = elasticsearchRestTemplate.search(searchQuery, KnowledgeBasesIdx.class);
        List<SearchHit<KnowledgeBasesIdx>> searchHitList = searchHits.getSearchHits();
        List<KnowledgeBasesEntity> knowledgeBasesEntities = new ArrayList<>();
        for (SearchHit<KnowledgeBasesIdx> knowledgeBasesIdxSearchHit : searchHitList) {
            KnowledgeBasesIdx content = knowledgeBasesIdxSearchHit.getContent();
            KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
            BeanUtil.copyProperties(content, knowledgeBases);
            if (StringUtils.isNotBlank(knowledgeBases.getOperativeWord())) {
                if (knowledgeBases.getOperativeWord().contains(",")) {
                    knowledgeBases.setOperativeWord(knowledgeBases.getOperativeWord().replace(",", ";"));
                }
            }
            knowledgeBasesEntities.add(knowledgeBases);
        }
        PageInfo pageInfo = new PageInfo(knowledgeBasesEntities);
        pageInfo.setTotal(searchHits.getTotalHits());
        return pageInfo;
    }

    @Override
    public List<String> getBasesImageInfo(InsertKnowledgeBasesEvt evt) {
        LambdaQueryWrapper<DocumentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentEntity::getBusiId, evt.getKgKnowledgeBasesId())
                .eq(DocumentEntity::getBusiType, CommonConstant.BUSI_TYPE_IMAGE);
        List<DocumentEntity> documentEntities = documentMapper.selectList(wrapper);
        List<String> imageList = new ArrayList<>();
        for (DocumentEntity documentEntity : documentEntities) {
            String docUrl = documentEntity.getDocUrl();
            imageList.add(docUrl);
        }
        return imageList;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public String insertBasesImages(MultipartFile file) {
        FileInfo filePath = null;
        try {
            String filename = file.getOriginalFilename();
            String fileSuffix = FilenameUtils.getExtension(filename);
            filePath = docService.uploadFile(file, CommonConstant.UPLOAD_IAMGE_TYPE);
            if (StringUtils.isNotEmpty(filePath.getFilePath())) {
                DocumentEntity documentEntity = new DocumentEntity();
                documentEntity.setDocName(filename);
                documentEntity.setExtensionName(fileSuffix);
                documentEntity.setDocUrl(filePath.getFilePath());
                documentEntity.setDocSize(file.getSize());
                documentMapper.insert(documentEntity);
            }
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new KnowledgeGraphException("上传文件失败：" + e.getMessage());
        }
        return filePath.getFilePath();
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public boolean deleteDoc(DocEvt evt) {
        List<Long> ids = evt.getIds();
        for (Long id : ids) {
            DocumentEntity documentEntity = documentMapper.selectById(id);
            ctdfsService.deleteFile(documentEntity.getDocUrl());
            documentMapper.deleteById(id);
        }
        return true;
    }

    @Override
    public String lookForBases(LookForBasesEvt evt) {
        LambdaQueryWrapper<DocumentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentEntity::getBusiId, evt.getKgKnowledgeBasesId())
                .eq(DocumentEntity::getBusiType, CommonConstant.UPLOAD_IMPORT_TYPE);
        DocumentEntity documentEntity = documentMapper.selectOne(wrapper);
        if (ObjectUtils.isNotEmpty(documentEntity)) {
            throw new KnowledgeGraphException("该知识为页面新增知识，没有原文");
        }
        return documentEntity.getDocUrl();
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp submitKnowledgeBases(BatchUpdateBasesStateEvt evt) {
        log.info("开始批量提交知识库，知识库IDs: {}, 目标状态: {}, 操作人: {}",
                evt.getKgKnowledgeBasesIds(), evt.getState(), evt.getOperUserId());

        try {
            // 1. 参数校验
            validateSubmitRequest(evt);

            // 2. 查询知识库信息
            List<KnowledgeBasesEntity> knowledgeBases = knowledgeBasesDMapper.selectBatchIds(evt.getKgKnowledgeBasesIds());
            if (CollectionUtils.isEmpty(knowledgeBases)) {
                return ServiceResp.fail("未找到指定的知识库");
            }

            // 3. 根据状态分发处理
            return processKnowledgeBasesSubmit(evt, knowledgeBases);

        } catch (Exception e) {
            log.error("批量提交知识库失败", e);
            throw new KnowledgeGraphException("批量提交知识库失败: " + e.getMessage());
        }
    }

    /**
     * 校验提交请求参数
     */
    private void validateSubmitRequest(BatchUpdateBasesStateEvt evt) {
        if (CollectionUtils.isEmpty(evt.getKgKnowledgeBasesIds())) {
            throw new KnowledgeGraphException("知识库ID列表不能为空");
        }
        if (StringUtils.isBlank(evt.getState())) {
            throw new KnowledgeGraphException("目标状态不能为空");
        }
    }

    /**
     * 根据状态分发处理知识库提交
     */
    private ServiceResp processKnowledgeBasesSubmit(BatchUpdateBasesStateEvt evt, List<KnowledgeBasesEntity> knowledgeBases) {
        String targetState = evt.getState();

        if (CaseStateEnum.PUBLISHED.getStateValue().equals(targetState)) {
            return handlePublishKnowledgeBases(evt, knowledgeBases);
        } else if (CaseStateEnum.OFF_LINE.getStateValue().equals(targetState)) {
            return handleOfflineKnowledgeBases(evt, knowledgeBases);
        } else if (CaseStateEnum.PENDING_AUDIT.getStateValue().equals(targetState)) {
            return handleSubmitForAudit(evt, knowledgeBases);
        } else {
            return handleGeneralStateUpdate(evt, knowledgeBases);
        }
    }

    /**
     * 处理知识库发布
     */
    private ServiceResp handlePublishKnowledgeBases(BatchUpdateBasesStateEvt evt, List<KnowledgeBasesEntity> knowledgeBases) {
        Date currentTime = new Date();

        // 1. 检查文档格式并筛选符合上报要求的知识库
        ReportEligibilityResult eligibilityResult = checkReportEligibility(knowledgeBases);

        // 2. 处理集团同步（如果需要）
        if (CommonConstant.IS_SYNC_YES.equals(evt.getIsSync()) && !eligibilityResult.getEligibleIds().isEmpty()) {
            checkAndAutoReportAfterReviewApproval(eligibilityResult.getEligibleIds());
        } else {
            // 不同步到集团，只更新本地状态
            updateLocalReportStatus(evt.getKgKnowledgeBasesIds(), evt.getReportStatus(), currentTime, evt.getIsSync());
        }

        // 3. 处理知识库状态更新和RAG构建
        return processKnowledgeBasesPublish(evt, currentTime, eligibilityResult.getFormatIneligibleNames());
    }

    /**
     * 处理知识库下线
     */
    private ServiceResp handleOfflineKnowledgeBases(BatchUpdateBasesStateEvt evt, List<KnowledgeBasesEntity> knowledgeBases) {
        Date currentTime = new Date();
        String batchNo = null;

        // 1. 处理集团同步（如果需要）
        if (CommonConstant.IS_SYNC_YES.equals(evt.getIsSync())) {
            batchNo = handleGroupOfflineSync(evt, currentTime);
        } else {
            // 不同步到集团，只更新本地状态
            updateLocalReportStatus(evt.getKgKnowledgeBasesIds(), evt.getReportStatus(), currentTime, evt.getIsSync());
        }

        // 2. 更新知识库状态为下线，并设置首次下线标记
        updateKnowledgeBasesStateForOffline(evt.getKgKnowledgeBasesIds(), evt.getState(), currentTime);

        // 3. 批量更新ES索引
        batchUpdateElasticsearchIndex(evt.getKgKnowledgeBasesIds());

        return ServiceResp.success("知识库下线成功");
    }

    /**
     * 处理提交审核
     */
    private ServiceResp handleSubmitForAudit(BatchUpdateBasesStateEvt evt, List<KnowledgeBasesEntity> knowledgeBases) {
        Date currentTime = new Date();

        // 更新知识库状态为待审核，并设置提交时间
        updateKnowledgeBasesStateForAudit(evt.getKgKnowledgeBasesIds(), evt.getState(), currentTime);

        // 批量更新ES索引
        batchUpdateElasticsearchIndex(evt.getKgKnowledgeBasesIds());

        return ServiceResp.success("知识库提交审核成功");
    }

    /**
     * 处理一般状态更新
     */
    private ServiceResp handleGeneralStateUpdate(BatchUpdateBasesStateEvt evt, List<KnowledgeBasesEntity> knowledgeBases) {
        Date currentTime = new Date();

        // 更新知识库状态
        updateKnowledgeBasesState(evt.getKgKnowledgeBasesIds(), evt.getState(), currentTime, false);

        // 批量更新ES索引
        batchUpdateElasticsearchIndex(evt.getKgKnowledgeBasesIds());

        return ServiceResp.success("知识库状态更新成功");
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查文档格式是否符合上报要求
     */
    private ReportEligibilityResult checkReportEligibility(List<KnowledgeBasesEntity> knowledgeBases) {
        List<String> formatIneligibleNames = new ArrayList<>();
        List<Long> eligibleIds = new ArrayList<>();

        for (KnowledgeBasesEntity kb : knowledgeBases) {
            // 跳过已上报的文档
            if (ReportStatusConstant.isReported(kb.getReportStatus())) {
                continue;
            }
            if (reportFormatUtil.isFormatEligibleForReport(kb.getDocumentFormat())) {
                eligibleIds.add(kb.getKgKnowledgeBasesId());
            } else {
                formatIneligibleNames.add(kb.getKnowledgeName());
            }
        }

        return new ReportEligibilityResult(eligibleIds, formatIneligibleNames);
    }

    /**
     * 更新本地上报状态
     */
    private void updateLocalReportStatus(List<Long> knowledgeBaseIds, String reportStatus, Date currentTime, String isSync) {
        UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("report_status", reportStatus)
                .set("report_time", currentTime)
                .set("is_sync", isSync)
                .in("kg_knowledge_bases_id", knowledgeBaseIds);
        knowledgeBasesDMapper.update(null, updateWrapper);
    }

    /**
     * 处理集团下线同步
     */
    private String handleGroupOfflineSync(BatchUpdateBasesStateEvt evt, Date currentTime) {
        String batchNo = createReportBatch(evt.getState(), evt.getOperUserId(), currentTime);
        try {
            log.info("开始调用集团下线接口");
            boolean success = callGroupDocApi(evt.getKgKnowledgeBasesIds(), "offLine", evt.getOperUserId(), "0");
            log.info("调用集团下线接口返回的状态: {}", success);

            if (success) {
                // 更新知识库状态为集团已下线
                updateGroupSyncStatus(evt.getKgKnowledgeBasesIds(), evt.getReportStatus(), currentTime,
                                    batchNo, evt.getOperUserId(), evt.getIsSync());
                // 更新批次状态为已完成
                updateReportBatchStatus(batchNo, CommonConstant.YES_INT, "success");
            } else {
                // 更新批次状态为失败
                updateReportBatchStatus(batchNo, CommonConstant.NO_INT, "调用集团接口失败");
            }
        } catch (Exception e) {
            // 更新批次状态为失败
            updateReportBatchStatus(batchNo, CommonConstant.NO_INT, "同步集团知识库失败");
            log.error("同步下线集团知识库失败", e);
            throw new KnowledgeGraphException("同步下线集团知识库失败: " + e.getMessage());
        }
        return batchNo;
    }

    /**
     * 更新集团同步状态
     */
    private void updateGroupSyncStatus(List<Long> knowledgeBaseIds, String reportStatus, Date currentTime,
                                     String batchNo, String operUserId, String isSync) {
        UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("report_status", reportStatus)
                .set("report_time", currentTime)
                .set("report_batch_no", batchNo)
                .set("jt_author", operUserId)
                .set("is_sync", isSync)
                .in("kg_knowledge_bases_id", knowledgeBaseIds);
        knowledgeBasesDMapper.update(null, updateWrapper);
    }

    /**
     * 更新知识库状态为下线
     */
    private void updateKnowledgeBasesStateForOffline(List<Long> knowledgeBaseIds, String state, Date currentTime) {
        UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("updated_time", currentTime)
                .set("change_time", currentTime)
                .set("state", state)
                .set("whether_first_offline", CommonConstant.YES_INT)
                .in("kg_knowledge_bases_id", knowledgeBaseIds);
        knowledgeBasesDMapper.update(null, updateWrapper);
    }

    /**
     * 更新知识库状态为待审核
     */
    private void updateKnowledgeBasesStateForAudit(List<Long> knowledgeBaseIds, String state, Date currentTime) {
        UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("updated_time", currentTime)
                .set("change_time", currentTime)
                .set("state", state)
                .set("submit_time", currentTime)
                .in("kg_knowledge_bases_id", knowledgeBaseIds);
        knowledgeBasesDMapper.update(null, updateWrapper);
    }

    /**
     * 更新知识库状态（通用方法）
     */
    private void updateKnowledgeBasesState(List<Long> knowledgeBaseIds, String state, Date currentTime, boolean setReleaseTime) {
        UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("updated_time", currentTime)
                .set("change_time", currentTime)
                .set("state", state);

        if (setReleaseTime) {
            updateWrapper.set("release_time", currentTime);
        }

        updateWrapper.in("kg_knowledge_bases_id", knowledgeBaseIds);
        knowledgeBasesDMapper.update(null, updateWrapper);
    }

    /**
     * 处理知识库发布的核心逻辑
     */
    private ServiceResp processKnowledgeBasesPublish(BatchUpdateBasesStateEvt evt, Date currentTime, List<String> formatIneligibleNames) {
        QueryWrapper<KnowledgeBasesEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("kg_knowledge_bases_id", evt.getKgKnowledgeBasesIds());
        List<KnowledgeBasesEntity> knowledgeBasesList = knowledgeBasesDMapper.selectList(queryWrapper);

        for (KnowledgeBasesEntity basesEntity : knowledgeBasesList) {
            // 1. 确定发布状态
            String publishState = determinePublishState(basesEntity);

            // 2. 处理二次上线的集团同步
            if (CommonConstant.YES_INT.equals(basesEntity.getWhetherFirstOffline()) &&
                CommonConstant.IS_SYNC_YES.equals(evt.getIsSync())) {
                checkAndAutoReportAfterReviewApproval(Collections.singletonList(basesEntity.getKgKnowledgeBasesId()));
            }

            // 3. 更新知识库状态
            updateSingleKnowledgeBaseForPublish(basesEntity.getKgKnowledgeBasesId(), publishState, currentTime);

            // 4. 更新ES索引
            updateElasticsearchForPublish(basesEntity, publishState, currentTime);

            // 5. 处理RAG构建
            processRagBuild(basesEntity);
        }

        return buildSuccessResponse(formatIneligibleNames);
    }

    /**
     * 确定发布状态
     */
    private String determinePublishState(KnowledgeBasesEntity basesEntity) {
        return CommonConstant.YES_INT.equals(basesEntity.getWhetherFirstOffline())
                ? CaseStateEnum.SECOND_ONLINE.getStateValue()
                : CaseStateEnum.PUBLISHED.getStateValue();
    }

    /**
     * 更新单个知识库的发布状态
     */
    private void updateSingleKnowledgeBaseForPublish(Long knowledgeBaseId, String state, Date currentTime) {
        UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("release_time", currentTime)
                .set("state", state)
                .set("updated_time", currentTime)
                .set("change_time", currentTime)
                .eq("kg_knowledge_bases_id", knowledgeBaseId);
        knowledgeBasesDMapper.update(null, updateWrapper);
    }

    /**
     * 更新ES索引用于发布
     */
    private void updateElasticsearchForPublish(KnowledgeBasesEntity basesEntity, String state, Date currentTime) {
        try {
            KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
            knowledgeBasesIdx.setState(state);
            knowledgeBasesIdx.setKgKnowledgeBasesId(basesEntity.getKgKnowledgeBasesId());
            knowledgeBasesIdx.setUpdatedTime(currentTime);
            knowledgeBasesIdx.setChangeTime(currentTime);
            knowledgeBasesIdx.setReleaseTime(currentTime);

            esKnowSearchService.updateInfo(knowledgeBasesIdx, basesEntity.getKgKnowledgeBasesId(),
                    knowledgeBaseConfig.getBaseInfo().getIndexName());
        } catch (Exception e) {
            log.error("更新ES索引失败，知识库ID: {}", basesEntity.getKgKnowledgeBasesId(), e);
            // 不抛出异常，避免影响其他记录的更新
        }
    }

    /**
     * 处理RAG构建
     */
    private void processRagBuild(KnowledgeBasesEntity basesEntity) {
        if (StringUtils.isBlank(basesEntity.getKnowledgeFilePath())) {
            return;
        }

        try {
            // 获取知识空间信息
            KgBasesSpaceC kgBasesSpaceC = getKnowledgeBaseSpace(basesEntity.getKgKnowledgeBasesId());

            // 调用RAG服务处理文件
            if (kgBasesSpaceC != null) {
                ragService.processPublishedKnowledgeBase(
                        basesEntity.getKnowledgeFilePath(), basesEntity.getKgKnowledgeBasesId(),
                        kgBasesSpaceC.getBasesSpaceName(), kgBasesSpaceC.getRagFlowId()
                );
            } else {
                // 如果没有知识空间信息，只传文件路径和知识库ID
                ragService.processPublishedKnowledgeBase(
                        basesEntity.getKnowledgeFilePath(), basesEntity.getKgKnowledgeBasesId(),
                        null, null
                );
            }
        } catch (Exception e) {
            // 只记录日志，不影响主流程
            log.error("调用RAG服务处理知识库文件异常，知识库ID：{}，异常：{}",
                     basesEntity.getKgKnowledgeBasesId(), e.getMessage());
        }
    }

    /**
     * 获取知识库空间信息
     */
    private KgBasesSpaceC getKnowledgeBaseSpace(Long knowledgeBaseId) {
        try {
            KgBasesSpaceRelationC relation = kgBasesSpaceRelationCMapper.selectkgBasesSpaceRelationC(
                    knowledgeBaseId, KnowModelType.KNOWLEDGE_BASE.getType());

            if (Objects.nonNull(relation) && Objects.nonNull(relation.getKgKnowledgeBasesId())) {
                return kgBasesSpaceCMapper.selectById(relation.getKgBasesSpaceCId());
            }
        } catch (Exception e) {
            log.error("获取知识库空间信息失败，知识库ID: {}", knowledgeBaseId, e);
        }
        return null;
    }

    /**
     * 构建成功响应
     */
    private ServiceResp buildSuccessResponse(List<String> formatIneligibleNames) {
        if (!formatIneligibleNames.isEmpty()) {
            String message = String.format("以下文档格式不符合集团上报要求，将不同步上报集团：%s",
                    String.join("、", formatIneligibleNames));
            return ServiceResp.success(message);
        }
        return ServiceResp.success("操作成功");
    }

    /**
     * 批量更新ES索引
     * 根据知识库ID列表，从数据库查询最新数据并更新到ES
     */
    private void batchUpdateElasticsearchIndex(List<Long> knowledgeBaseIds) {
        if (CollectionUtils.isEmpty(knowledgeBaseIds)) {
            return;
        }

        log.info("开始批量更新ES索引，数量: {}", knowledgeBaseIds.size());

        try {
            // 1. 批量查询知识库最新数据
            List<KnowledgeBasesEntity> knowledgeBases = knowledgeBasesDMapper.selectBatchIds(knowledgeBaseIds);
            if (CollectionUtils.isEmpty(knowledgeBases)) {
                log.warn("未找到对应的知识库数据，跳过ES更新");
                return;
            }

            // 2. 批量更新ES索引
            for (KnowledgeBasesEntity entity : knowledgeBases) {
                try {
                    updateSingleElasticsearchIndex(entity);
                } catch (Exception e) {
                    log.error("更新ES索引失败，知识库ID: {}", entity.getKgKnowledgeBasesId(), e);
                    // 不抛出异常，避免影响其他记录的更新
                }
            }

            log.info("批量更新ES索引完成，成功处理数量: {}", knowledgeBases.size());

        } catch (Exception e) {
            log.error("批量更新ES索引异常", e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 更新单个知识库的ES索引
     */
    private void updateSingleElasticsearchIndex(KnowledgeBasesEntity entity) {
        try {
            // 1. 构建ES索引对象
            KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
            BeanUtil.copyProperties(entity, knowledgeBasesIdx);

            // 2. 设置时间字段
            extracted(entity, knowledgeBasesIdx);

            // 3. 设置全文检索字段
            knowledgeBasesIdx.setFullText();

            // 4. 使用统一的ES更新服务
            esKnowSearchService.updateInfo(knowledgeBasesIdx, entity.getKgKnowledgeBasesId(),
                    knowledgeBaseConfig.getBaseInfo().getIndexName());

        } catch (Exception e) {
            log.error("更新单个ES索引失败，知识库ID: {}", entity.getKgKnowledgeBasesId(), e);
            throw e; // 重新抛出，让调用方决定如何处理
        }
    }

    /**
     * 上报资格检查结果
     */
    private static class ReportEligibilityResult {
        private final List<Long> eligibleIds;
        private final List<String> formatIneligibleNames;

        public ReportEligibilityResult(List<Long> eligibleIds, List<String> formatIneligibleNames) {
            this.eligibleIds = eligibleIds;
            this.formatIneligibleNames = formatIneligibleNames;
        }

        public List<Long> getEligibleIds() {
            return eligibleIds;
        }

        public List<String> getFormatIneligibleNames() {
            return formatIneligibleNames;
        }
    }

    @Override
    public void exportBasesWordOrPdf(Long kgBasesId, String fileType, HttpServletResponse response) {
        if (kgBasesId != null) {
            KnowledgeBasesEntity knowledgeBasesEntity = knowledgeBasesDMapper.selectById(kgBasesId);
            if (StringUtils.isNotBlank(knowledgeBasesEntity.getKnowledgeFilePath())) {
                try {
                    ctdfsService.downWordTmpFile(knowledgeBasesEntity.getKnowledgeFilePath(), fileType, new java.io.File(knowledgeBasesEntity.getKnowledgeFilePath()).getName(), response);
                } catch (Exception e) {
                    log.error("知识库导出word或pdf失败", e.getMessage());
                }
            }
        }
    }

    @Override
    public boolean commonDownLoadFile(HttpServletResponse response, CommonDownloadFileEvt evt) {
        ctdfsService.downloadFile(evt.getFilePath(), new java.io.File(evt.getFilePath()).getName(), response);
        return true;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp addOrUpdateBasesAuditDimensionConfig(List<KgBasesAuditDimensionConfigD> evt) {
        if (CollectionUtil.isEmpty(evt)) {
            return ServiceResp.fail("待新增的审核维度不能为空");
        }
        AtomicReference<Integer> orderNum = new AtomicReference<>(0);
        basesAuditDimensionConfigMapper.delete(new QueryWrapper<>());
        evt.stream().forEach(kgBasesAuditDimensionConfigD -> {
            kgBasesAuditDimensionConfigD.setCreatedTime(new Date());
            kgBasesAuditDimensionConfigD.setCreatedUserName(PtSecurityUtils.getUsername());
            kgBasesAuditDimensionConfigD.setUpdatedTime(new Date());
            kgBasesAuditDimensionConfigD.setUpdatedUserName(PtSecurityUtils.getUsername());
            orderNum.set(orderNum.get() + 1);
            kgBasesAuditDimensionConfigD.setOrder(orderNum.get());
            basesAuditDimensionConfigMapper.insert(kgBasesAuditDimensionConfigD);
        });
        return ServiceResp.success("操作成功");
    }

    @Override
    public ServiceResp getBasesAuditDimensionConfig() {
        QueryWrapper<KgBasesAuditDimensionConfigD> queryWrapper = new QueryWrapper();
        queryWrapper.orderByAsc("\"order\"");
        return ServiceResp.success("操作成功", basesAuditDimensionConfigMapper.selectList(queryWrapper));
    }

    @Override
    public ServiceResp deleteBasesAuditDimensionConfig(KgBasesAuditDimensionConfigD evt) {
        return ServiceResp.success(basesAuditDimensionConfigMapper.deleteById(evt.getKgBasesAuditDimensionId()) > 0 ? "操作成功" : "操作失败");
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp addOrUpdateBasesReviewConclusion(BasesReviewConclusionEvt evt) {
        // 1. 构建部分更新的实体对象（只设置需要更新的字段）
        KnowledgeBasesIdx updateEntity = new KnowledgeBasesIdx();
        updateEntity.setKgKnowledgeBasesId(evt.getKgKnowledgeBasesId()); // @Id字段必须设置

        if (CollectionUtil.isEmpty(evt.getKgBasesAuditDimensionDList())) {
            basesAuditDimensionMapper.delete(new QueryWrapper<KgBasesAuditDimensionD>().eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId()));
            AtomicReference<Integer> orderNum = new AtomicReference<>(0);
            KgBasesAuditDimensionD kgBasesAuditDimensionD = new KgBasesAuditDimensionD();
            kgBasesAuditDimensionD.setCreatedTime(new Date());
            kgBasesAuditDimensionD.setCreatedUserName(PtSecurityUtils.getUsername());
            kgBasesAuditDimensionD.setUpdatedTime(new Date());
            kgBasesAuditDimensionD.setUpdatedUserName(PtSecurityUtils.getUsername());
            orderNum.set(orderNum.get() + 1);
            kgBasesAuditDimensionD.setOrder(orderNum.get());
            kgBasesAuditDimensionD.setComment(evt.getComment());
            kgBasesAuditDimensionD.setWhetherPass(evt.getWhetherPass());
            kgBasesAuditDimensionD.setKgKnowledgeBasesId(evt.getKgKnowledgeBasesId());
            kgBasesAuditDimensionD.setKgBasesAuditDimensionId(null);
            basesAuditDimensionMapper.insert(kgBasesAuditDimensionD);

            Date updateTime = new Date();
            if (CommonConstant.PASS.equals(evt.getWhetherPass())) {
                knowledgeBasesDMapper.update(null, new UpdateWrapper<KnowledgeBasesEntity>().set("state", CaseStateEnum.TO_BE_RELEASED.getStateValue())
                        .set("updated_time", updateTime)
                        .set("audit_status", AuditEnum.PASS.getAuditStatus())
                        .set("updated_user_name", PtSecurityUtils.getUsername())
                        .set("reviewer", PtSecurityUtils.getUsername())
                        .set("audit_time", updateTime)
                        .eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId()));
                updateEntity.setState(CaseStateEnum.TO_BE_RELEASED.getStateValue());
                updateEntity.setAuditStatus(AuditEnum.PASS.getAuditStatus());
            } else if (CommonConstant.NO_PASS.equals(evt.getWhetherPass())) {
                knowledgeBasesDMapper.update(null, new UpdateWrapper<KnowledgeBasesEntity>().set("state", CaseStateEnum.AUDIT_NOT_PASS.getStateValue())
                        .set("updated_time", updateTime)
                        .set("audit_status", AuditEnum.NO_PASS.getAuditStatus())
                        .set("updated_user_name", PtSecurityUtils.getUsername())
                        .set("reviewer", PtSecurityUtils.getUsername())
                        .set("audit_time", updateTime)
                        .eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId()));
                updateEntity.setState(CaseStateEnum.AUDIT_NOT_PASS.getStateValue());
                updateEntity.setAuditStatus(AuditEnum.NO_PASS.getAuditStatus());
            }
            esKnowSearchService.updateInfo(updateEntity, evt.getKgKnowledgeBasesId(), knowledgeBaseConfig.getBaseInfo().getIndexName());
            return ServiceResp.success("操作成功");
        }
        basesAuditDimensionMapper.delete(new QueryWrapper<KgBasesAuditDimensionD>().eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId()));
        AtomicReference<Integer> orderNum = new AtomicReference<>(0);
        evt.getKgBasesAuditDimensionDList().stream().forEach(kgBasesAuditDimensionD -> {
            kgBasesAuditDimensionD.setCreatedTime(new Date());
            kgBasesAuditDimensionD.setCreatedUserName(PtSecurityUtils.getUsername());
            kgBasesAuditDimensionD.setUpdatedTime(new Date());
            kgBasesAuditDimensionD.setUpdatedUserName(PtSecurityUtils.getUsername());
            orderNum.set(orderNum.get() + 1);
            kgBasesAuditDimensionD.setOrder(orderNum.get());
            kgBasesAuditDimensionD.setWhetherPass(evt.getWhetherPass());
            kgBasesAuditDimensionD.setKgKnowledgeBasesId(evt.getKgKnowledgeBasesId());
            kgBasesAuditDimensionD.setKgBasesAuditDimensionId(null);
            basesAuditDimensionMapper.insert(kgBasesAuditDimensionD);
        });
        Date updateTime = new Date();
        if (CommonConstant.PASS.equals(evt.getWhetherPass())) {
            knowledgeBasesDMapper.update(null, new UpdateWrapper<KnowledgeBasesEntity>().set("state", CaseStateEnum.TO_BE_RELEASED.getStateValue())
                    .set("updated_time", updateTime)
                    .set("updated_user_name", PtSecurityUtils.getUsername())
                    .set("audit_status", AuditEnum.PASS.getAuditStatus())
                    .set("reviewer", PtSecurityUtils.getUsername())
                    .set("audit_time", updateTime)
                    .eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId()));
            updateEntity.setState(CaseStateEnum.TO_BE_RELEASED.getStateValue());
            updateEntity.setAuditStatus(AuditEnum.PASS.getAuditStatus());
            // 审核通过后，检查文档格式是否符合集团上报要求，如果符合则自动上报
            try {
                checkAndAutoReportAfterReviewApproval(Arrays.asList(evt.getKgKnowledgeBasesId()));
            } catch (Exception e) {
                log.error("自动上报集团失败，知识库ID: {}", evt.getKgKnowledgeBasesId(), e);
                // 不影响主流程，继续执行
            }
        } else if (CommonConstant.NO_PASS.equals(evt.getWhetherPass())) {
            knowledgeBasesDMapper.update(null, new UpdateWrapper<KnowledgeBasesEntity>().set("state", CaseStateEnum.AUDIT_NOT_PASS.getStateValue())
                    .set("updated_time", updateTime)
                    .set("audit_status", AuditEnum.NO_PASS.getAuditStatus())
                    .set("updated_user_name", PtSecurityUtils.getUsername())
                    .set("reviewer", PtSecurityUtils.getUsername())
                    .set("audit_time", updateTime)
                    .eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId()));
            updateEntity.setState(CaseStateEnum.AUDIT_NOT_PASS.getStateValue());
            updateEntity.setAuditStatus(AuditEnum.NO_PASS.getAuditStatus());
        }

        esKnowSearchService.updateInfo(updateEntity, evt.getKgKnowledgeBasesId(), knowledgeBaseConfig.getBaseInfo().getIndexName());

        return ServiceResp.success("操作成功");
    }

    @Override
    public ServiceResp getBasesReviewConclusion(BasesReviewConclusionEvt evt) {
        QueryWrapper<KgBasesAuditDimensionD> queryWrapper = new QueryWrapper();
        queryWrapper.eq("kg_knowledge_bases_id", evt.getKgKnowledgeBasesId());
        return ServiceResp.success("操作成功", basesAuditDimensionMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp addOrUpdateBasesAuditPerson(SaveKgBasesAuditPersonDEvt evt) {
        // 1 先获取用户信息
        if (Objects.isNull(evt.getUserId())) {
            throw new KnowledgeGraphException("用户ID不能为空");
        }
        UserVm userInfo = getUserInfo(null, evt.getUserId());
        if(Objects.isNull(userInfo)){
            throw new KnowledgeGraphException("用户不存在,查询用户服务失败");
        }

        LambdaQueryWrapper<KgBasesAuditPersonD> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.and(wrapper -> wrapper
                .eq(KgBasesAuditPersonD::getName, userInfo.getAlias()).or()
                .eq(KgBasesAuditPersonD::getTelephone, userInfo.getTelephone()))
                .ne(evt.getKgBasesAuditPersonId() != null, KgBasesAuditPersonD::getKgBasesAuditPersonId, evt.getKgBasesAuditPersonId());
        Integer count = basesAuditPersonMapper.selectCount(queryWrapper);

        //fixed 重新复制 用户信息
        evt.setTelephone(userInfo.getTelephone());
        evt.setName(userInfo.getAlias());
        evt.setLoginName(userInfo.getLoginName());
        if (count > 0) {
            return ServiceResp.fail("该手机号或名称已存在，请重新输入");
        }
        if (evt.getKgBasesAuditPersonId() == null) {
            evt.setCreatedTime(new Date());
            evt.setCreatedUserName(PtSecurityUtils.getUsername());
            evt.setIsDeleted(CommonConstant.IS_DELETED);
            if (CollectionUtil.isNotEmpty(evt.getTotalFullRegionList())) {
                evt.setTotalFullRegion(evt.getTotalFullRegionList().stream().flatMap(List::stream).collect(Collectors.joining(",")).trim());
            }
            basesAuditPersonMapper.insert(evt);
        } else {
            evt.setUpdatedTime(new Date());
            evt.setUpdatedUserName(PtSecurityUtils.getUsername());
            if (CollectionUtil.isNotEmpty(evt.getTotalFullRegionList())) {
                evt.setTotalFullRegion(evt.getTotalFullRegionList().stream().flatMap(List::stream).collect(Collectors.joining(",")).trim());
            }
            basesAuditPersonMapper.updateById(evt);
        }
        return ServiceResp.success("操作成功");
    }

    /**
     * 默认用 13328223567 账号
     *
     * @param userName
     * @return
     */
    public UserVm getUserInfo(String userName, Long userId) {
        QryUserEvt user = new QryUserEvt();
        if (StringUtils.isNotBlank(userName)) {
            user.setLoginName(userName);
        }
        if (Objects.nonNull(userId)) {
            user.setUserId(userId);
        }
        UserVm simpleUserVm = null;
        try {
            simpleUserVm = smQryClient.qryUser(user).getBody();
            if (simpleUserVm != null) {
                return simpleUserVm;
            }
            return simpleUserVm;
        } catch (Exception e) {
            log.error("请求qry服务报错，暂时跳过", e);
            return simpleUserVm;
        }
    }

    @Override
    public QueryPageVm<KgBasesAuditPersonDVm> getBasesAuditPerson(KgBasesAuditPersonDEvt evt) {
        if (evt.getType() == null || (evt.getType() != null && !CommonConstant.YES.equals(evt.getType()))) {
            String userName = PtSecurityUtils.getUsername();
            //todo
            //userName = "xulinsen";

            if (StringUtils.isNotBlank(userName)) {
                KgBasesAuditPersonDEvt kgAuditPersonDEvt = new KgBasesAuditPersonDEvt();
                kgAuditPersonDEvt.setRealName(userName);
                List<KgBasesAuditPersonDVm> caseAuditPersonList = basesAuditPersonMapper.getBasesAuditPerson(kgAuditPersonDEvt);
                if (CollectionUtil.isNotEmpty(caseAuditPersonList)) {
                    KgBasesAuditPersonDVm kgAuditPersonDVm = caseAuditPersonList.get(0);
                    evt.setCurrentUserRegionList(kgAuditPersonDVm.getRegionList());
                    if (StringUtils.isNotBlank(kgAuditPersonDVm.getRole()) && kgAuditPersonDVm.getRole().contains(AuditPersonRoleEnum.PROVINCE_ADMIN.getRoleValue())) {
                        evt.setWhetherGetProvince(CommonConstant.NO);
                    } else if (StringUtils.isNotBlank(kgAuditPersonDVm.getRole()) && kgAuditPersonDVm.getRole().contains(AuditPersonRoleEnum.REGION_ADMIN.getRoleValue())) {
                        evt.setWhetherGetProvinceAndRegion(CommonConstant.NO);
                    }
                } else {
                    return QueryPageVm.getInstance(evt, Collections.EMPTY_LIST, 0L);
                }
            }
        }

        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            Page page = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
            List<KgBasesAuditPersonDVm> caseAuditPersonList = basesAuditPersonMapper.getBasesAuditPerson(evt);
            caseAuditPersonList.stream().forEach(kgAuditPersonDVm -> {
                if (kgAuditPersonDVm.getTelephone() != null && kgAuditPersonDVm.getTelephone().length() == 11) {
                    kgAuditPersonDVm.setTelephone(kgAuditPersonDVm.getTelephone().substring(0, 3) + "****" + kgAuditPersonDVm.getTelephone().substring(7));
                }
            });
            return QueryPageVm.getInstance(evt, caseAuditPersonList, page.getTotal());
        } else {
            List<KgBasesAuditPersonDVm> caseAuditPersonList = basesAuditPersonMapper.getBasesAuditPerson(evt);
            return QueryPageVm.getInstance(evt, caseAuditPersonList, 0L);
        }
    }

    @Override
    public KgBasesAuditPersonDVm getBasesAuditPersonByName(KgBasesAuditPersonDEvt evt) {
        List<KgBasesAuditPersonDVm> caseAuditPerson = basesAuditPersonMapper.getBasesAuditPerson(evt);
        if (CollectionUtil.isNotEmpty(caseAuditPerson)) {
            return caseAuditPerson.get(0);
        } else {
            return new KgBasesAuditPersonDVm();
        }
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp batchDeleteBasesAuditPerson(List<KgBasesAuditPersonD> evt) {
        if (CollectionUtils.isNotEmpty(evt)) {
            basesAuditPersonMapper.deleteBatchIds(evt.stream().map(KgBasesAuditPersonD::getKgBasesAuditPersonId).collect(Collectors.toList()));
            return ServiceResp.success("操作成功");
        } else {
            return ServiceResp.fail("待删除的审核人id不能为空");
        }
    }

    @Override
    public ServiceResp batchSetRegionAdmin(List<KgBasesAuditPersonD> evt) {
        if (CollectionUtils.isNotEmpty(evt)) {
            List<KgBasesAuditPersonD> auditPersonDList = basesAuditPersonMapper.selectList(new QueryWrapper<KgBasesAuditPersonD>().in("kg_bases_audit_person_id", evt.stream().map(KgBasesAuditPersonD::getKgBasesAuditPersonId).collect(Collectors.toList())));
            auditPersonDList.stream().forEach(auditPersonD -> {
                if (StringUtils.isNotBlank(auditPersonD.getRole())) {
                    if (!auditPersonD.getRole().contains(AuditPersonRoleEnum.REGION_ADMIN.getRoleValue())) {
                        auditPersonD.setRole(auditPersonD.getRole() + "," + AuditPersonRoleEnum.REGION_ADMIN.getRoleValue());
                    }
                } else {
                    auditPersonD.setRole(AuditPersonRoleEnum.REGION_ADMIN.getRoleValue());
                }
                auditPersonD.setUpdatedTime(new Date());
                auditPersonD.setUpdatedUserName(PtSecurityUtils.getUsername());
                basesAuditPersonMapper.updateById(auditPersonD);
            });
        }
        return ServiceResp.success("操作成功");
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp batchCancelRegionAdmin(List<KgBasesAuditPersonD> evt) {
        if (CollectionUtils.isNotEmpty(evt)) {
            List<KgBasesAuditPersonD> auditPersonDList = basesAuditPersonMapper.selectList(new QueryWrapper<KgBasesAuditPersonD>().in("kg_bases_audit_person_id", evt.stream().map(KgBasesAuditPersonD::getKgBasesAuditPersonId).collect(Collectors.toList())));
            auditPersonDList.stream().forEach(auditPersonD -> {
                if (StringUtils.isNotBlank(auditPersonD.getRole())) {
                    if (auditPersonD.getRole().contains(AuditPersonRoleEnum.REGION_ADMIN.getRoleValue())) {
                        List<String> roleList = new ArrayList<>(Arrays.asList(auditPersonD.getRole().split(",")));
                        roleList.remove(AuditPersonRoleEnum.REGION_ADMIN.getRoleValue());
                        auditPersonD.setRole(StringUtils.join(roleList, ","));
                    }
                }
                auditPersonD.setUpdatedTime(new Date());
                auditPersonD.setUpdatedUserName(PtSecurityUtils.getUsername());
                basesAuditPersonMapper.updateById(auditPersonD);
                log.info("{}被取消区域管理员,修改人:{}", auditPersonD.getName(), PtSecurityUtils.getUsername());
            });
        }
        return ServiceResp.success("操作成功");
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp updateAuditorAllocationRuleConfig(KgAuditorAllocationRuleConfigD evt) {
        if (evt == null) {
            return ServiceResp.fail("审核人分配规则配置id不能为空");
        }
        List<KgAuditorAllocationRuleConfigD> auditorAllocationRuleConfigDList = auditorAllocationRuleConfigMapper.selectList(new QueryWrapper<>());
        evt.setKgAuditorAllocationRuleConfigId(BigInteger.ONE);
        evt.setUpdatedTime(new Date());
        evt.setUpdatedUserName(PtSecurityUtils.getUsername());
        auditorAllocationRuleConfigMapper.updateById(evt);
        return ServiceResp.success("操作成功");
    }

    @Override
    public ServiceResp getWaitingAuditAndHistoryAuditNums(KnowledgeBasesEntity evt) {
        QueryWrapper<KnowledgeBasesEntity> waitingAuditQueryWrapper = new QueryWrapper<>();
        waitingAuditQueryWrapper.eq("state", CaseStateEnum.PENDING_AUDIT.getStateValue());
        waitingAuditQueryWrapper.eq("is_deleted", CommonConstant.IS_DELETED);
        Integer waitingAuditNum = knowledgeBasesDMapper.selectCount(waitingAuditQueryWrapper);
        QueryWrapper<KnowledgeBasesEntity> historyAuditQueryWrapper = new QueryWrapper<>();
        historyAuditQueryWrapper.and(wrapper -> wrapper.eq("state", CaseStateEnum.TO_BE_RELEASED.getStateValue()).or().eq("state", CaseStateEnum.AUDIT_NOT_PASS.getStateValue()))
                .eq("is_deleted", CommonConstant.IS_DELETED);
        if (StringUtils.isNotBlank(PtSecurityUtils.getUsername())) {
            QryUserEvt user = new QryUserEvt();
            user.setLoginName(PtSecurityUtils.getUsername());
            SimpleUserVm simpleUserVm = smQryClient.qrySimpleUser(user).getBody();
            if (simpleUserVm != null && !CommonConstant.YES.equals(simpleUserVm.getUserType())) {
                historyAuditQueryWrapper.eq("reviewer", PtSecurityUtils.getUsername());
            }
        }
        Integer historyAuditNum = knowledgeBasesDMapper.selectCount(historyAuditQueryWrapper);

        return ServiceResp.success("操作成功", new HashMap<String, Integer>() {{
            put("waitingAuditNum", waitingAuditNum);
            put("historyAuditNum", historyAuditNum);
        }});
    }

    @Override
    public ServiceResp getAuditorAllocationRuleConfig() {
        QueryWrapper<KgAuditorAllocationRuleConfigD> queryWrapper = new QueryWrapper();
        queryWrapper.eq("kg_auditor_allocation_rule_config_id", BigInteger.ONE);
        List<KgAuditorAllocationRuleConfigD> auditorAllocationRuleConfigDList = auditorAllocationRuleConfigMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(auditorAllocationRuleConfigDList)) {
            return ServiceResp.success("操作成功", null);
        }
        return ServiceResp.success("操作成功", auditorAllocationRuleConfigDList.get(0));
    }

    @Override
    public ServiceResp getBasesAuditPersonTelPhone(KgBasesAuditPersonD evt) {
        if (evt.getKgBasesAuditPersonId() == null) {
            return ServiceResp.fail("审核人id不能为空");
        }
        return ServiceResp.success("操作成功", basesAuditPersonMapper.selectById(evt.getKgBasesAuditPersonId()));
    }

    @Override
    public ServiceResp getBasesRelatedTopFive(KnowledgeBasesEntity evt) {
        if (evt.getKgKnowledgeBasesId() == null) {
            return ServiceResp.fail("知识id不能为空");
        }
        KnowledgeBasesEntity rawCaseEntity = knowledgeBasesDMapper.selectById(evt.getKgKnowledgeBasesId());
        // 获取 operativeWord 字符串
        String operativeWord = rawCaseEntity.getOperativeWord();

        // 判空处理后切割字符串
        List<String> operativeWordList = new ArrayList<>();
        if (operativeWord != null && !operativeWord.trim().isEmpty()) {
            operativeWordList = new ArrayList<>(Arrays.asList(operativeWord.split(",")));
        } else {
            // 可选：处理空的情况，比如初始化一个空列表或者添加默认值
            operativeWordList = new ArrayList<>(); // 确保返回一个空列表而不是 null
        }
        GetBasesEvt getBasesEvt = new GetBasesEvt();
        getBasesEvt.setOperativeWordList(operativeWordList);
        if (rawCaseEntity != null && rawCaseEntity.getClickCount() != 0) {
            getBasesEvt.setWhetherClickCountSort(0);
        } else {
            getBasesEvt.setWhetherClickCountSort(1);
        }
        getBasesEvt.setUserType("1");
        getBasesEvt.setBasesType("1");
        List<KnowledgeBasesEntity> rawCaseEntityList = knowledgeBasesDMapper.selectBasesListByHighLevel(getBasesEvt);
        List<KnowledgeBasesEntity> caseEntityList = rawCaseEntityList.subList(0, Math.min(5, rawCaseEntityList.size()));
        List<KnowledgeBasesEntity> result = caseEntityList.stream().filter((r) -> !r.getKgKnowledgeBasesId().equals(evt.getKgKnowledgeBasesId())).collect(Collectors.toList());
        return ServiceResp.success("操作成功", result);
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public UploadApiResponse uploadFileToRag(String filePath, String ragFlowId) {
        InputStream inputStream = ctdfsService.downloadStream(filePath);
        //上传文件到rag库
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        try {
            String url = knowledgeBaseConfig.getRagflow().getApi().getUrl() + "/" + ragFlowId + "/documents";
            // 获取文件名（假设文件名是从路径中提取）
            String fileName = FileUtil.getFileName(filePath);
            // 设置 MediaType 和 RequestBody
            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/octet-stream");
            byte[] bytes = IOUtils.toByteArray(inputStream);
            RequestBody fileBody = RequestBody.create(mediaType, bytes);
            // 构建 Multipart 请求体
            RequestBody body = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", fileName, fileBody)
                    .build();
            log.info("上传文件到rag,接口地址:{},文件路径:{}", url, filePath);
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Authorization", "Bearer " + knowledgeBaseConfig.getRagflow().getApi().getKey()) // 添加 Authorization 头
                    .addHeader("Content-Type", "application/json") // 如果需要设置 Content-Type
                    .method("POST", body)
                    .build();
            // 执行请求
            try (Response response = client.newCall(request).execute()) {

                if (response.isSuccessful()) {
                    // 获取响应体
                    ResponseBody responseBody = response.body();
                    if (responseBody != null) {
                        // 将响应体转换为字符串
                        String result = responseBody.string();
                        // 创建Gson实例
                        Gson gson = new Gson();
                        // 将JSON字符串转换为指定类型的对象
                        UploadApiResponse results = gson.fromJson(result, UploadApiResponse.class);
                        if (results.getCode() == 102) {
                            throw new KnowledgeGraphException(results.getMessage());
                        }
                        log.info("上传到 RAG 库成功，返回结果:{}", results);
                        return results;
                    } else {
                        log.error("响应体为空");
                    }
                } else {
                    log.error("上传到 RAG 库失败，返回编码: {}, 返回消息:{}", response.code(), response.message());
                }
            } catch (IOException e) {
                log.error("上传文件到 RAG 库失败", e.getMessage());
            }
        } catch (IOException e) {
            log.error("上传文件到 RAG 库失败", e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public boolean deleteFileDocument(DeletDocEvt evt) {
        boolean flag = ctdfsService.deleteFile(evt.getDocUrl());
        return flag;
    }

    @Override
    public Map<String, List<DictionaryVm>> getAllBasesDictionary(List<String> codeTypeList) {
        List<DictionaryEntity> dictionaryList = dictionaryMapper.findByCodeTypeIn(codeTypeList);
        Map<String, List<DictionaryVm>> map = new HashMap<>();
        for (DictionaryEntity dictionary : dictionaryList) {
            List<DictionaryVm> dictionaryVmList = map.get(dictionary.getCodeType());
            if (CollectionUtils.isEmpty(dictionaryVmList)) {
                dictionaryVmList = new ArrayList<>();
                // 判断是否是特殊处理的类型
                if ("case_state".equals(dictionary.getCodeType()) || "audit_role".equals(dictionary.getCodeType())) {
                    dictionaryVmList.add(new DictionaryVm(dictionary.getCodeName(), dictionary.getCodeValue()));
                } else {
                    String codeValue = dictionary.getBdpDirId().toString();
                    dictionaryVmList.add(new DictionaryVm(dictionary.getCodeName(), codeValue));
                }
                map.put(dictionary.getCodeType(), dictionaryVmList);
            } else {
                // 同样地，对已存在的列表添加新的元素时也需要进行同样的判断
                if ("case_state".equals(dictionary.getCodeType()) || "audit_role".equals(dictionary.getCodeType())) {
                    dictionaryVmList.add(new DictionaryVm(dictionary.getCodeName(), dictionary.getCodeValue()));
                } else {
                    String codeValue = dictionary.getBdpDirId().toString();
                    dictionaryVmList.add(new DictionaryVm(dictionary.getCodeName(), codeValue));
                }
            }
        }
        return map;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public KnowledgeBasesEntity importBasesManualExcel(MultipartFile file) throws IOException {
        // file ==》 调用AI 去查重 pdf word excel ppt html  单个文件与历史文件查重 0.9


        // 读取文件并确保其类型正确
        String fileName = file.getOriginalFilename();
        String typePart = FileUtil.getTypePart(fileName);
        InputStream inputStream = file.getInputStream();
//        if (!StringUtils.isEmpty(fileName) && !CommonConstant.FILE_TYPE.equals(typePart)) {
//            throw new KnowledgeGraphException(ExceptionDefinition.FILE_TYPE_ERROR.getMsg());
//        }
        if (ObjectUtils.isEmpty(inputStream)) {
            throw new KnowledgeGraphException(ExceptionDefinition.FILE_EMPY_ERROR.getMsg());
        }


        // 初始化实体对象
        KnowledgeBasesEntity knowledgeBasesEntity = new KnowledgeBasesEntity();
        // 查找最后一个 "." 的位置
        int lastDotIndex = fileName.lastIndexOf('.');
        // 提取文件名部分（不含扩展名）
        String fileNameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        knowledgeBasesEntity.setKnowledgeName(fileNameWithoutExt);

        // 设置其他默认值
        //knowledgeBasesEntity.setAuditStatus("1");
        knowledgeBasesEntity.setIsDeleted("0");
        knowledgeBasesEntity.setDocumentFormat(typePart);
        knowledgeBasesEntity.setSearchNumber(0);
        knowledgeBasesEntity.setCreatedUserName(PtSecurityUtils.getUsername());

        // 8. 上传文件到文件系统
        log.info("开始上传文件: {}", fileName);
        FileInfo filePath;
        try {
            filePath = docService.uploadFile(file, CommonConstant.UPLOAD_BASES_TYPE);
            knowledgeBasesEntity.setKnowledgeFilePath(filePath.getFilePath());
            knowledgeBasesEntity.setFilesize(filePath.getFileSize());
            knowledgeBasesEntity.setUploadResult("成功");
            knowledgeBasesEntity.setUploadProgress(100);
        } catch (Exception e) {
            knowledgeBasesEntity.setUploadResult("文件上传失败: " + e.getMessage());
            // 失败时处理
            Random random = new Random();
            int randomProgress = random.nextInt(98) + 1;
            knowledgeBasesEntity.setUploadProgress(randomProgress);
        }

        String fileContent = null;

        try {
            fileContent = kgContiDService.analyzeFile(file);
            knowledgeBasesEntity.setFileContent(fileContent);
        } catch (Exception e) {
            knowledgeBasesEntity.setUploadResult("文件分析失败: " + e.getMessage());
        }
        // 返回：查重重复率 0-100
        //读取出文件内容
        return knowledgeBasesEntity;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public Object getBasesSpaceById(Long id) {
        QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
        kgBasesSpaceCQueryWrapper.eq("is_deleted", false)
                .eq("kg_bases_space_c_id", id);
        List<KgBasesSpaceC> kgBasesSpaceCS = kgBasesSpaceCMapper.selectList(kgBasesSpaceCQueryWrapper);
        return kgBasesSpaceCS;
    }

    @Override
    public PageInfo<KgBasesSpaceC> getAvailableBasesSpaces(SpacePageInfoEvt evt) {
        Integer pageNo = evt.getPageNo();
        Integer pageSize = evt.getPageSize();
        QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
        kgBasesSpaceCQueryWrapper.eq("is_deleted", false);
        List<KgBasesSpaceC> allItems = kgBasesSpaceCMapper.selectList(kgBasesSpaceCQueryWrapper);
        // 计算分页起始点
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, allItems.size());

        // 检查fromIndex是否超出范围
        if (fromIndex > allItems.size()) {
            fromIndex = allItems.size();
        }

        // 切片操作，获取对应页的数据
        List<KgBasesSpaceC> pageItems = allItems.subList(fromIndex, toIndex);

        // 创建PageInfo对象，假设有一个合适的构造函数
        PageInfo<KgBasesSpaceC> pageInfo = new PageInfo<>(pageItems);
        pageInfo.setPageNum(pageNo);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotal(allItems.size());

        return pageInfo;
    }

    @Override
    public Map<String, List<DictionaryTreeVm>> getAllBasesTreeDictionary(List<String> codeTypeList) {
        if (codeTypeList == null || codeTypeList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 获取所有相关的字典列表
        List<DictionaryEntity> dictionaryList = dictionaryMapper.findByCodeTypeIn(codeTypeList);
        if (dictionaryList == null || dictionaryList.isEmpty()) {
            return codeTypeList.stream().collect(Collectors.toMap(
                    Function.identity(),
                    k -> Collections.emptyList()
            ));
        }

        // 创建一个映射，用于存储每个codeType对应的树形结构
        Map<String, List<DictionaryTreeVm>> resultMap = new HashMap<>();
        // 定义需要特殊处理的codeType集合
        Set<String> specialCodeTypes = new HashSet<>(Arrays.asList("case_state", "audit_role"));

        // 按codeType分组，避免多次遍历
        Map<String, List<DictionaryEntity>> groupedByCodeType = dictionaryList.stream()
                .collect(Collectors.groupingBy(DictionaryEntity::getCodeType));

        for (String codeType : codeTypeList) {
            List<DictionaryEntity> entities = groupedByCodeType.getOrDefault(codeType, Collections.emptyList());

            // 创建vmMap来存放当前codeType下的所有节点
            Map<Long, DictionaryTreeVm> vmMap = new HashMap<>();
            Set<Long> childIds = new HashSet<>(); // 用于记录所有有父节点的ID

            // 第一次遍历：创建所有节点并记录有父节点的ID
            for (DictionaryEntity entity : entities) {
                boolean isSpecial = specialCodeTypes.contains(codeType);
                String codeValue = isSpecial ? entity.getCodeValue() : entity.getBdpDirId().toString();
                if (codeValue.equals("11742474")) {
                    codeValue = "99";
                }
                DictionaryTreeVm vm = new DictionaryTreeVm(entity.getCodeName(), codeValue);

                if (entity.getParentId() != null) {
                    childIds.add(entity.getBdpDirId());
                }

                vmMap.put(entity.getBdpDirId(), vm);
            }

            // 第二次遍历：构建父子关系
            for (DictionaryEntity entity : entities) {
                if (entity.getParentId() != null && vmMap.containsKey(entity.getParentId())) {
                    DictionaryTreeVm parent = vmMap.get(entity.getParentId());
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(vmMap.get(entity.getBdpDirId()));
                }
            }

            // 收集根节点（没有父节点且不是其他节点的子节点）
            List<DictionaryTreeVm> rootNodes = entities.stream()
                    .filter(entity -> entity.getParentId() == null && !childIds.contains(entity.getBdpDirId()))
                    .map(entity -> vmMap.get(entity.getBdpDirId()))
                    .collect(Collectors.toList());

            resultMap.put(codeType, rootNodes);
        }

        return resultMap;
    }

    @Override
    public List<KgBasesSpaceLabelD> getLabels() {
        QueryWrapper<KgBasesSpaceLabelD> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", false);
        List<KgBasesSpaceLabelD> kgBasesSpaceLabelDS = kgBasesSpaceLabelDMapper.selectList(queryWrapper);
        return kgBasesSpaceLabelDS;
    }

    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public boolean checkAndValidateLabelName(String labelName, Long kgBasesSpaceCId, Long currentLabelId) throws KnowledgeGraphException {
        QueryWrapper<KgBasesSpaceLabelD> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", false);

        if (labelName != null) {
            queryWrapper.eq("label_name", labelName);
        }
        if (kgBasesSpaceCId != null) {
            queryWrapper.eq("kg_bases_space_c_id", kgBasesSpaceCId);
        }
        // 如果提供了当前标签ID，则排除自己进行比较
        if (currentLabelId != null) {
            queryWrapper.ne("kg_bases_space_label_id", currentLabelId);
        }

        KgBasesSpaceLabelD existingLabel = kgBasesSpaceLabelDMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(existingLabel)) {
            throw new KnowledgeGraphException("标签名不能重复");
        }
        return true;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public void createLabel(KgBasesSpaceLabelD evt) {
        checkAndValidateLabelName(evt.getLabelName(), evt.getKgBasesSpaceCId(), null);
        kgBasesSpaceLabelDMapper.insert(evt);
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public void updateLabel(KgBasesSpaceLabelD evt) {
        checkAndValidateLabelName(evt.getLabelName(), null, evt.getKgBasesSpaceLabelId());
        QueryWrapper<KgBasesSpaceLabelD> kgBasesSpaceLabelDQueryWrapper = new QueryWrapper<>();
        kgBasesSpaceLabelDQueryWrapper.eq("kg_bases_space_label_id", evt.getKgBasesSpaceLabelId())
                .eq("is_deleted", false);
        kgBasesSpaceLabelDMapper.update(evt, kgBasesSpaceLabelDQueryWrapper);
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public void deleteLabels(List<Long> ids) throws KnowledgeGraphException {
        Integer b = kgBasesSpaceCMapper.selexistsLebels(ids);
        if (b > 0) {
            throw new KnowledgeGraphException("存在引用的标签名，该标签不能删除！");
        }
        for (Long id : ids) {
            KgBasesSpaceLabelD kgBasesSpaceLabelD = new KgBasesSpaceLabelD();
            kgBasesSpaceLabelD.setIsDeleted(true);
            QueryWrapper<KgBasesSpaceLabelD> kgBasesSpaceLabelDQueryWrapper = new QueryWrapper<>();
            kgBasesSpaceLabelDQueryWrapper.eq("kg_bases_space_label_id", id);
            kgBasesSpaceLabelDMapper.update(kgBasesSpaceLabelD, kgBasesSpaceLabelDQueryWrapper);
        }
    }

    @Override
    public List<Map<String, String>> getSpacesNums() {
        List<Map<String, String>> maps = kgBasesSpaceRelationCMapper.selectSpaceCodeWithKnowledgeCount();
        return maps;
    }

    @Override
    public List<KgBasesSpaceC> getAllBasesSpaces() {
        QueryWrapper<KgBasesSpaceC> kgBasesSpaceCQueryWrapper = new QueryWrapper<>();
        kgBasesSpaceCQueryWrapper.eq("is_deleted", false);
        List<KgBasesSpaceC> allItems = kgBasesSpaceCMapper.selectList(kgBasesSpaceCQueryWrapper);
        return allItems;
    }

    @Override
    public PageInfo<KgBasesSpaceC> getKnowledgeBasess(GetBasesEvt evt) {
        // 设置默认用户
        if (StringUtil.isEmpty(evt.getBeforeOneUser())) {
            evt.setBeforeOneUser(PtSecurityUtils.getUsername());
        }
        
        // 处理操作词
        if (StringUtils.isNotBlank(evt.getOperativeWord())) {
            evt.setOperativeWordList(new ArrayList<>(Arrays.asList(evt.getOperativeWord().split(";"))));
        }
        
        // 处理用户权限和区域信息
        handleUserPermissions(evt);
        
        // 处理审核权限
        handleAuditPermissions(evt);
        
        // 根据是否有全文检索分别处理
        if (StringUtil.isEmpty(evt.getFullText())) {
            return handleNormalQuery(evt);
        } else {
            return handleFullTextQuery(evt);
        }
    }

    /**
     * 处理用户权限和区域信息
     */
    private void handleUserPermissions(GetBasesEvt evt) {
        if (CollectionUtil.isNotEmpty(evt.getCurrentUserList()) && CollectionUtil.isEmpty(evt.getRegion())) {
            evt.setRegion(evt.getCurrentUserList().stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 处理审核权限
     */
    private void handleAuditPermissions(GetBasesEvt evt) {
        if (evt.getWhetherAuditOrder() != null && evt.getState() != null 
                && evt.getState().contains(CaseStateEnum.PENDING_AUDIT.getStateValue())) {
            
            KgAuditorAllocationRuleConfigD ruleConfig = auditorAllocationRuleConfigMapper.selectById(BigInteger.ONE);
            if (ruleConfig == null) {
                return;
            }
            
            String currentUser = PtSecurityUtils.getUsername();
            if (StringUtils.isBlank(currentUser) || CommonConstant.YES.equals(evt.getType())) {
                return;
            }
            
            KgBasesAuditPersonDEvt auditPersonEvt = new KgBasesAuditPersonDEvt();
            auditPersonEvt.setName(currentUser);
            
            List<KgBasesAuditPersonDVm> auditPersons = basesAuditPersonMapper.getBasesAuditPerson(auditPersonEvt);
            if (CollectionUtil.isEmpty(auditPersons)) {
                return;
            }
            
            KgBasesAuditPersonDVm auditPerson = auditPersons.get(0);
            
            // 省级管理员不做限制
            if (CommonConstant.NO_PASS.equals(auditPerson.getWhetherProvinceAdmin())) {
                applyAuditRules(evt, ruleConfig, auditPerson);
            }
        }
    }

    /**
     * 应用审核规则
     */
    private void applyAuditRules(GetBasesEvt evt, KgAuditorAllocationRuleConfigD ruleConfig, KgBasesAuditPersonDVm auditPerson) {
        boolean isRegionAdmin = CommonConstant.PASS.equals(auditPerson.getWhetherRegionAdmin());
        
        if (!isRegionAdmin) {
            // 非区域管理员，按分配规则过滤
            switch (ruleConfig.getValue()) {
                case "1": // 按专业过滤
                    applyMajorFilter(evt, auditPerson);
                    break;
                case "2": // 按区域过滤
                    applyRegionFilter(evt, auditPerson);
                    break;
                case "3": // 按专业和区域过滤
                    applyMajorFilter(evt, auditPerson);
                    applyRegionFilter(evt, auditPerson);
                    break;
            }
        } else {
            // 区域管理员只过滤区域
            applyRegionFilter(evt, auditPerson);
        }
    }

    /**
     * 应用专业过滤
     */
    private void applyMajorFilter(GetBasesEvt evt, KgBasesAuditPersonDVm auditPerson) {
        if (StringUtils.isBlank(auditPerson.getMajor())) {
            return;
        }
        
        List<String> auditPersonMajors = Arrays.asList(auditPerson.getMajor().split(","));
        if (CollectionUtil.isEmpty(evt.getMajor())) {
            evt.setMajor(new ArrayList<>(auditPersonMajors));
        } else {
            evt.getMajor().addAll(auditPersonMajors);
        }
    }

    /**
     * 应用区域过滤
     */
    private void applyRegionFilter(GetBasesEvt evt, KgBasesAuditPersonDVm auditPerson) {
        List<String> regionList = auditPerson.getRegionList();
        List<Integer> region = regionList.stream().map(FormatUtil::intParse).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(regionList)) {
            return;
        }
        
        if (CollectionUtil.isEmpty(evt.getRegion())) {
            evt.setRegion(new ArrayList<>(region));
        } else {
            evt.getRegion().addAll(region);
        }
    }

    /**
     * 处理普通查询（非全文检索）
     */
    private PageInfo<KgBasesSpaceC> handleNormalQuery(GetBasesEvt evt) {
        PageHelper.startPage(evt.getPageNo(), evt.getPageSize());
        
        // 设置时间范围
        if (CollectionUtil.isNotEmpty(evt.getCreatedTimeList())) {
            evt.setStartTime(evt.getCreatedTimeList().get(0));
            evt.setEndTime(evt.getCreatedTimeList().get(1));
        }

        // 查询知识库列表
        List<KnowledgeBasesEntity> knowledgeBases = knowledgeBasesDMapper.selectBasesListByHighLevel(evt);
        if (CollectionUtils.isEmpty(knowledgeBases)) {
            return new PageInfo<>(new ArrayList<>());
        }

        // 获取空间列表
        List<KgBasesSpaceC> spaceList = getSpacesByKnowledgeBases(knowledgeBases, evt);
        
        // 处理特殊查询逻辑
        if (isEmptyFilters(evt)) {
            spaceList = knowledgeBasesDMapper.selectBasesListByHighLevelss(evt);
        }

        return new PageInfo<>(removeDuplicateSpaces(spaceList));
    }

    /**
     * 处理全文检索查询
     */
    private PageInfo<KgBasesSpaceC> handleFullTextQuery(GetBasesEvt evt) {
        PageInfo<KnowledgeBasesEntity> fullTextResult = getCasesOnFullText(evt);
        List<KnowledgeBasesEntity> knowledgeBases = fullTextResult.getList();
        
        if (CollectionUtils.isEmpty(knowledgeBases)) {
            return new PageInfo<>(new ArrayList<>());
        }

        List<KgBasesSpaceC> spaceList = getSpacesByKnowledgeBases(knowledgeBases, evt);
        if (CollectionUtils.isEmpty(spaceList)) {
            return new PageInfo<>(new ArrayList<>());
        }

        return new PageInfo<>(removeDuplicateSpaces(spaceList));
    }

    /**
     * 根据知识库列表获取对应的空间列表
     * 优化：批量查询替代循环查询，支持一对多关系
     */
    private List<KgBasesSpaceC> getSpacesByKnowledgeBases(List<KnowledgeBasesEntity> knowledgeBases, GetBasesEvt evt) {
        // 提取知识库ID列表
        List<Long> knowledgeBaseIds = knowledgeBases.stream()
                .map(KnowledgeBasesEntity::getKgKnowledgeBasesId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询空间关联关系（支持一对多）
        List<Long> spaceIds = getSpaceIdsByKnowledgeBaseIds(knowledgeBaseIds);
        if (CollectionUtils.isEmpty(spaceIds)) {
            return new ArrayList<>();
        }

        // 处理数字员工过滤
        spaceIds = filterSpaceIdsByStaff(spaceIds, evt);
        if (CollectionUtils.isEmpty(spaceIds)) {
            return new ArrayList<>();
        }

        // 批量查询空间信息
        return kgBasesSpaceCMapper.selectByIds(spaceIds);
    }

    /**
     * 批量查询空间ID列表
     * 修复：使用 selectList 支持一对多关系，避免 selectOne 报错
     */
    private List<Long> getSpaceIdsByKnowledgeBaseIds(List<Long> knowledgeBaseIds) {
        QueryWrapper<KgBasesSpaceRelationC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("kg_knowledge_bases_id", knowledgeBaseIds)
                   .eq("base_type", KnowModelType.KNOWLEDGE_BASE.getType())
                   .eq("is_deleted", false);
        
        return kgBasesSpaceRelationCMapper.selectList(queryWrapper).stream()
                .map(KgBasesSpaceRelationC::getKgBasesSpaceCId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据数字员工设置过滤空间ID
     */
    private List<Long> filterSpaceIdsByStaff(List<Long> spaceIds, GetBasesEvt evt) {
        if (!evt.isFromStaff() || evt.getStaffType() == null || evt.getMtPlatStaffId() == null) {
            return spaceIds;
        }
        
        if (evt.getStaffType() == 0) {
            // 只包含指定的员工ID
            return spaceIds.stream()
                    .filter(id -> evt.getMtPlatStaffId().contains(id))
                    .collect(Collectors.toList());
        } else if (evt.getStaffType() == 1) {
            // 排除指定的员工ID
            return spaceIds.stream()
                    .filter(id -> !evt.getMtPlatStaffId().contains(id))
                    .collect(Collectors.toList());
        }
        
        return spaceIds;
    }

    /**
     * 检查是否为空的过滤条件
     */
    private boolean isEmptyFilters(GetBasesEvt evt) {
        return CollectionUtils.isEmpty(evt.getApplicationSceneList())
                && CollectionUtils.isEmpty(evt.getDocumentTypeList())
                && CollectionUtils.isEmpty(evt.getDocumentFormatList())
                && CollectionUtils.isEmpty(evt.getFlowSceneList())
                && CollectionUtils.isEmpty(evt.getMajor())
                && CollectionUtils.isEmpty(evt.getKnowledgeOriginList())
                && CollectionUtils.isEmpty(evt.getSecretLevelList());
    }

    /**
     * 去除重复的空间对象
     */
    private List<KgBasesSpaceC> removeDuplicateSpaces(List<KgBasesSpaceC> spaceList) {
        return spaceList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(KgBasesSpaceC::getKgBasesSpaceCId))),
                        ArrayList::new));
    }

    @Override
    public String basesCheckRepeat(BasesCheckRepeatEvt evt) {
        if (CollUtil.isEmpty(evt.getKgbsIds())) {
            return "请选择查重的知识";
        }
        CompletableFuture.runAsync(() -> checkFilesRepeat(evt.getKgbsIds()));
        return "后台正在查重中";
    }

    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public Map<String, Object> createRagwebDataset(RagWebDatasetCreateRequest ragWebDatasetCreateRequest) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            HttpEntity<RagWebDatasetCreateRequest> requestEntity = new HttpEntity<>(ragWebDatasetCreateRequest, headers);

            String url = knowledgeBaseConfig.getRagweb().getUrl() + "/createPartition";
            // 发送请求
            log.info("创建RAG空间请求参数：{}", ragWebDatasetCreateRequest.toString());
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("创建RAG空间返回参数：{}", response.toString());

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                String body = response.getBody();
                // 创建Gson实例
                Gson gson = new Gson();
                // 将JSON字符串转换为指定类型的对象
                Map<String, Object> result = gson.fromJson(body, Map.class);
                return result;
            } else {
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("code", -1);
                stringObjectHashMap.put("message", "API请求失败");
                return stringObjectHashMap;
            }
        } catch (Exception e) {
            log.error("创建ragweb知识空间失败：{}", e.getMessage(), e);
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("code", -1);
            stringObjectHashMap.put("message", " 创建知识库时发生错误: " + e.getMessage());
            return stringObjectHashMap;
        }
    }


    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public Map<String, Object> deleteRagwebDataset(HashMap<String, Object> request) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<>(request, headers);
            String urls = knowledgeBaseConfig.getRagweb().getUrl() + "/delPartition";
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    urls,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                String body = response.getBody();
                // 创建Gson实例
                Gson gson = new Gson();
                // 将JSON字符串转换为指定类型的对象
                Map<String, Object> result = gson.fromJson(body, Map.class);
                return result;
            } else {
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("code", -1);
                stringObjectHashMap.put("message", "API请求失败");
                return stringObjectHashMap;
            }
        } catch (Exception e) {
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("code", -1);
            stringObjectHashMap.put("message", " 创建知识库时发生错误: " + e.getMessage());
            return stringObjectHashMap;
        }
    }

    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public List<String> uploadFileToRagwebDataset(String filePath, String fileType, MultipartFile multipartFile) {
//        InputStream inputStream = ctdfsService.downloadStream(filePath);
        //上传文件到rag库
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        try {
            String url = knowledgeBaseConfig.getRagweb().getUrl() + "/batchUploadFile";
            String fileName = FileUtil.getFileName(filePath);
            // 设置 MediaType 和 RequestBody
            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/octet-stream");
//            byte[] bytes = IOUtils.toByteArray(inputStream);
            byte[] bytes = multipartFile.getBytes();
            RequestBody fileBody = RequestBody.create(mediaType, bytes);
            // 构建 Multipart 请求体
            RequestBody body = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", fileName, fileBody)
                    .addFormDataPart("fileType", fileType)
                    .build();
            log.info("上传文件到rag,接口地址:{},文件路径:{}", url, filePath);
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json") // 如果需要设置 Content-Type
                    .method("POST", body)
                    .build();
            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                log.info("上传文件到 rag 库成功，返回编码: {},返回消息:{}", response.code(), response.message());
                if (response.isSuccessful()) {
                    // 获取响应体
                    ResponseBody responseBody = response.body();
                    if (responseBody != null) {
                        // 将响应体转换为字符串
                        String result = responseBody.string();
                        log.info("上传文件到 rag 库成功，返回结果: {}", result);
                        // 创建Gson实例
                        Gson gson = new Gson();
                        // 将JSON字符串转换为指定类型的对象
                        FileProcessingResult results = gson.fromJson(result, FileProcessingResult.class);
                        List<FileProcessingResult.Success> success = results.getResult().getSuccess();

                        List<String> strings = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(success)) {
                            for (FileProcessingResult.Success s : success) {
                                strings.add(s.getFile_id());
                            }
                        }
                        return strings;
                    } else {
                        log.error("响应体为空");
                    }
                } else {
                    log.error("上传到 RAG 库失败，返回编码: {}, 返回消息:{}", response.code(), response.message());
                }
            } catch (Exception e) {
                log.error("上传文件到 RAG 库失败", e.getMessage());
            }
        } catch (Exception e) {
            log.error("上传文件到 RAG 库失败", e.getMessage());
        }
        return null;
    }

    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public Map<String, Object> buildFileToRagwebDataset(HashMap<String, Object> request) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<>(request, headers);
            String url = knowledgeBaseConfig.getRagweb().getUrl() + "/buildIndexByFileId";
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                String body = response.getBody();
                // 创建Gson实例
                Gson gson = new Gson();
                // 将JSON字符串转换为指定类型的对象
                Map<String, Object> result = gson.fromJson(body, Map.class);
                return result;
            } else {
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("code", -1);
                stringObjectHashMap.put("message", "API请求失败");
                return stringObjectHashMap;
            }
        } catch (Exception e) {
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("code", -1);
            stringObjectHashMap.put("message", " 创建知识库时发生错误: " + e.getMessage());
            return stringObjectHashMap;
        }
    }


    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public Map<String, Object> delFileToRagwebDataset(HashMap<String, Object> request) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<>(request, headers);
            String url = knowledgeBaseConfig.getRagweb().getUrl() + "/delFileById";
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                String body = response.getBody();
                // 创建Gson实例
                Gson gson = new Gson();
                // 将JSON字符串转换为指定类型的对象
                Map<String, Object> result = gson.fromJson(body, Map.class);
                return result;
            } else {
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                stringObjectHashMap.put("code", -1);
                stringObjectHashMap.put("message", "API请求失败");
                return stringObjectHashMap;
            }
        } catch (Exception e) {
            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("code", -1);
            stringObjectHashMap.put("message", " 创建知识库时发生错误: " + e.getMessage());
            return stringObjectHashMap;
        }
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ApiResponse ragBuild(DatasetCreateRequest request) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + knowledgeBaseConfig.getRagflow().getApi().getKey());

            // 构建请求体
            HttpEntity<DatasetCreateRequest> requestEntity = new HttpEntity<>(request, headers);
            String url = knowledgeBaseConfig.getRagflow().getApi().getUrl() + "/" + request.getDatasetId() + "/chunks";
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                String body = response.getBody();
                // 创建Gson实例
                Gson gson = new Gson();
                // 将JSON字符串转换为指定类型的对象
                ApiResponse result = gson.fromJson(body, ApiResponse.class);
                return result;
            } else {
                ApiResponse errorResponse = new ApiResponse();
                errorResponse.setCode(-1);
                errorResponse.setMessage("API 请求失败，状态码: " + response.getStatusCode());
                return errorResponse;
            }
        } catch (Exception e) {
            ApiResponse errorResponse = new ApiResponse();
            errorResponse.setCode(-1);
            errorResponse.setMessage(" 创建知识库时发生错误: " + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ApiResponse delRagFlowSpaceFile(DatasetCreateRequest request) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + knowledgeBaseConfig.getRagflow().getApi().getKey());

            // 构建请求体
            HttpEntity<DatasetCreateRequest> requestEntity = new HttpEntity<>(request, headers);
            String url = knowledgeBaseConfig.getRagflow().getApi().getUrl() + "/" + request.getRagFlowId() + "/documents";
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    requestEntity,
                    String.class
            );

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                String body = response.getBody();
                // 创建Gson实例
                Gson gson = new Gson();
                // 将JSON字符串转换为指定类型的对象
                ApiResponse result = gson.fromJson(body, ApiResponse.class);
                return result;
            } else {
                ApiResponse errorResponse = new ApiResponse();
                errorResponse.setCode(-1);
                errorResponse.setMessage("API 请求失败，状态码: " + response.getStatusCode());
                return errorResponse;
            }
        } catch (Exception e) {
            ApiResponse errorResponse = new ApiResponse();
            errorResponse.setCode(-1);
            errorResponse.setMessage(" 创建知识库时发生错误: " + e.getMessage());
            return errorResponse;
        }
    }


    // 批量上报 只上报集团 但是上报状态和时间和批次号需要更新到当前的pg数据库中
    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp reportKnowledgeBases(BatchUpdateBasesStateEvt evt) {
        List<Long> kgKnowledgeBasesIds = evt.getKgKnowledgeBasesIds();
        List<Long> repeatIds = new ArrayList<>();
        //查重校验
        if (CollUtil.isNotEmpty(kgKnowledgeBasesIds)) {
            for (Long kgKnowledgeBasesId : kgKnowledgeBasesIds) {
                //文件内容查重赋值
                List<SimilarFileInfo> similarList = getFileSimilarityListFromRedis(kgKnowledgeBasesId);
                boolean repetitive = similarList != null && similarList.stream()
                        .anyMatch(similar -> similar.getSimilarity() != null && similar.getSimilarity() >= CommonConstant.DUPLICATE_THRESHOLD);
                if (repetitive) {
                    repeatIds.add(kgKnowledgeBasesId);
                }
            }
        }
        if (repeatIds.size() > 0) {
            String knowledgeNames = knowledgeBasesDMapper.selectBatchIds(kgKnowledgeBasesIds).stream().map(KnowledgeBasesEntity::getKnowledgeName).collect(Collectors.joining(","));
            log.error("文档上报集团查重不通过  知识库名称:" + knowledgeNames);
            return ServiceResp.fail("文档上报集团查重不通过  不通过的知识库名称: " + knowledgeNames);
        }

        log.info("开始批量上报知识库 - 知识库IDs: {}, 操作人: {}, 上报状态: {}",
                evt.getKgKnowledgeBasesIds(), evt.getOperUserId(), evt.getState());

        UpdateWrapper<KnowledgeBasesEntity> knowledgeBasesEntityUpdateWrapper = new UpdateWrapper<>();
        Date date = new Date();
        String batchNo = null;

        // 执行集团上报
        checkAndAutoReportAfterReviewApproval(evt.getKgKnowledgeBasesIds());

        // 批量更新知识库状态
        batchUpdateKnowledgeBasesForReport(evt.getKgKnowledgeBasesIds(), date);

        // 批量更新ES索引
        batchUpdateEsForReport(evt.getKgKnowledgeBasesIds(), date);

        // 判断上报的状态
        List<KnowledgeBasesEntity> knowledgeBasesEntities = knowledgeBasesDMapper.selectBatchIds(evt.getKgKnowledgeBasesIds());
        List<KnowledgeBasesEntity> failList = knowledgeBasesEntities.stream().filter(ol -> ol.getReportStatus().equals(ReportStatusConstant.REPORT_FAILED)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(failList)) {
            String failContent = failList.stream().map(KnowledgeBasesEntity::getReportDescription).collect(Collectors.joining(";"));
            return ServiceResp.fail("上报文件失败：{}", failContent);
        }

        return ServiceResp.success("操作成功");
    }

    /**
     * 批量更新知识库状态（用于上报）
     */
    private void batchUpdateKnowledgeBasesForReport(List<Long> knowledgeBaseIds, Date updateTime) {
        try {
            UpdateWrapper<KnowledgeBasesEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("whether_first_offline", CommonConstant.YES_INT)
                    .set("updated_time", updateTime)
                    .set("change_time", updateTime)
                    .in("kg_knowledge_bases_id", knowledgeBaseIds);
            knowledgeBasesDMapper.update(null, updateWrapper);

            log.info("批量更新知识库状态成功，数量: {}", knowledgeBaseIds.size());
        } catch (Exception e) {
            log.error("批量更新知识库状态失败", e);
            throw new KnowledgeGraphException("批量更新知识库状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新ES索引（用于上报）
     */
    private void batchUpdateEsForReport(List<Long> knowledgeBaseIds, Date updateTime) {
        for (Long knowledgeBaseId : knowledgeBaseIds) {
            try {
                KnowledgeBasesIdx updateEntity = new KnowledgeBasesIdx();
                updateEntity.setWhetherFirstOffline(CommonConstant.YES_INT);
                updateEntity.setUpdatedTime(updateTime);
                updateEntity.setChangeTime(updateTime);

                esKnowSearchService.updateInfo(updateEntity, knowledgeBaseId,
                        knowledgeBaseConfig.getBaseInfo().getIndexName());
            } catch (Exception e) {
                log.error("更新ES索引失败，知识库ID: {}", knowledgeBaseId, e);
                // 不抛出异常，避免影响其他记录的更新
            }
        }
        log.info("批量更新ES索引完成，数量: {}", knowledgeBaseIds.size());
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public ServiceResp offerLineKnowledgeBases(BatchUpdateBasesStateEvt evt) {
        UpdateWrapper<KnowledgeBasesEntity> knowledgeBasesEntityUpdateWrapper = new UpdateWrapper<>();
        Date date = new Date();
        String batchNo = null;

        // 创建上报批次记录
        batchNo = createReportBatch(evt.getState(), evt.getOperUserId(), date); // 2-已上报集团
        try {
            // 调用集团文档接口
            boolean jtEnableds = callGroupDocApi(evt.getKgKnowledgeBasesIds(), "offLine", evt.getOperUserId(), "0");
            if (jtEnableds) {
                // 更新知识库状态为下线
                knowledgeBasesEntityUpdateWrapper
                        .set("report_status", evt.getReportStatus())
                        .set("report_time", date)
                        .set("report_batch_no", batchNo)
                        .set("jt_author", evt.getOperUserId())
                        .set("is_sync", evt.getIsSync());

                // 更新批次状态为已完成
                updateReportBatchStatus(batchNo, CommonConstant.YES_INT, "success");
            }
        } catch (Exception e) {
            // 更新批次状态为失败
            updateReportBatchStatus(batchNo, CommonConstant.NO_INT, "同步集团知识库失败");
            log.error("数据操作失败1：{}", e.getMessage());
            log.error("数据操作失败1：{}", e);
            throw new KnowledgeGraphException("同步集团知识库失败: " + e.getMessage());
        }

        // 更新知识库状态
        knowledgeBasesEntityUpdateWrapper
                .set("updated_time", date)
                .set("change_time", date)
                .in("kg_knowledge_bases_id", evt.getKgKnowledgeBasesIds());
        knowledgeBasesDMapper.update(null, knowledgeBasesEntityUpdateWrapper);

        return ServiceResp.success("操作成功");
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public List<Map<String, Object>> batchAddKnowledgeBases(List<InsertKnowledgeBasesEvt> insertKnowledgeBasesEvts) {
        // 创建结果集合，用于存储每个知识库的导入结果
        List<Map<String, Object>> results = new ArrayList<>();
        // 存储成功导入的知识库ID，用于后续批量查重
        List<Long> successfulKnowledgeBaseIds = new ArrayList<>();
        // 批处理大小
        final int BATCH_SIZE = 20;
        int totalSize = insertKnowledgeBasesEvts.size();

        long startTime = System.currentTimeMillis();
        
        log.info("开始批量导入知识库，共有{}条数据需要处理", totalSize);

        // 分批处理优化：将大量数据分批处理
        for (int batchStart = 0; batchStart < totalSize; batchStart += BATCH_SIZE) {
            int batchEnd = Math.min(batchStart + BATCH_SIZE, totalSize);
            List<InsertKnowledgeBasesEvt> batchEvts = insertKnowledgeBasesEvts.subList(batchStart, batchEnd);
            
            log.info("处理第{}批数据，批次范围[{}-{}]，本批次包含{}条数据", 
                    (batchStart / BATCH_SIZE) + 1, batchStart, batchEnd - 1, batchEvts.size());
            
            // 一次性处理整个批次的数据
            processBatchImport(batchEvts, results, successfulKnowledgeBaseIds);
        }

        // 批量异步查重 - 在所有知识库导入完成后一次性执行
        if (!successfulKnowledgeBaseIds.isEmpty()) {
            log.info("开始批量异步查重，共有{}个知识库需要处理", successfulKnowledgeBaseIds.size());
            // 使用改进的批量查重方法
            checkFilesRepeat(successfulKnowledgeBaseIds);
        }

        log.info("批量导入完成，消耗:{}ms", System.currentTimeMillis() - startTime);
        // 返回所有知识库的导入结果
        return results;
    }
    
    /**
     * 处理一个批次的知识库导入
     * 
     * @param batchEvts 批次中的知识库事件列表
     * @param results 结果列表，用于存储处理结果
     * @param successfulKnowledgeBaseIds 成功导入的知识库ID列表
     */
    private void processBatchImport(List<InsertKnowledgeBasesEvt> batchEvts, 
                                    List<Map<String, Object>> results, 
                                    List<Long> successfulKnowledgeBaseIds) {
        // 1. 参数预处理和验证阶段
        List<InsertKnowledgeBasesEvt> validEvts = new ArrayList<>();
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        
        // 批量收集所有有效的知识库名称，用于一次性查询数据库
        Set<String> knowledgeNames = new HashSet<>();
        
        for (InsertKnowledgeBasesEvt evt : batchEvts) {
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", evt.getKnowledgeName());
            result.put("success", false);
            resultMap.put(evt.getKnowledgeName(), result);
            
            // 参数验证
            if (evt.getKgBasesSpaceCId() == null) {
                result.put("message", "请选择知识空间");
                results.add(result);
                continue;
            }
            
            if (evt.getCategoryId() == null) {
                result.put("message", "请选择知识分类");
                results.add(result);
                continue;
            }
            
            if (StringUtils.isEmpty(evt.getKnowledgeName())) {
                result.put("message", "知识名为空");
                results.add(result);
                continue;
            }
            
            // 仅对新知识库执行后续处理
            if (ObjectUtils.isEmpty(evt.getKgKnowledgeBasesId())) {
                knowledgeNames.add(evt.getKnowledgeName());
                validEvts.add(evt);
            }
        }
        
        // 没有有效知识库，直接返回
        if (validEvts.isEmpty()) {
            for (Map<String, Object> result : resultMap.values()) {
                if (!results.contains(result)) {
                    results.add(result);
                }
            }
            return;
        }
        
        // 2. 一次性查询所有已存在的知识库名称
        log.info("批量查询是否存在重名知识库，查询数量: {}", knowledgeNames.size());
        Set<String> existingNames = new HashSet<>();
        if (!knowledgeNames.isEmpty()) {
            LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(KnowledgeBasesEntity::getKnowledgeName, knowledgeNames);
            wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
            List<KnowledgeBasesEntity> existingKnowledgeBases = knowledgeBasesDMapper.selectList(wrapper);
            
            for (KnowledgeBasesEntity entity : existingKnowledgeBases) {
                existingNames.add(entity.getKnowledgeName());
            }
            log.info("查询到{}个已存在的同名知识库", existingNames.size());
        }
        
        // 3. 准备批量插入的实体对象
        List<KnowledgeBasesEntity> knowledgeBasesToInsert = new ArrayList<>();
        List<KgBasesSpaceRelationC> relationsToInsert = new ArrayList<>();
        List<KnowledgeBasesIdx> indexesToSave = new ArrayList<>();
        Map<String, InsertKnowledgeBasesEvt> evtMap = new HashMap<>();
        Map<String, KnowledgeBasesEntity> entityMap = new HashMap<>();
        
        // 4. 处理每个有效知识库
        for (InsertKnowledgeBasesEvt evt : validEvts) {
            Map<String, Object> result = resultMap.get(evt.getKnowledgeName());
            
            // 跳过已存在同名知识库的情况
            if (existingNames.contains(evt.getKnowledgeName())) {
                result.put("message", ExceptionDefinition.BASES_TITLE_RET_ERROR.getMsg());
                results.add(result);
                continue;
            }
            
            try {
                // 创建实体对象
                KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
                
                // 处理操作词汇：将不同分隔符统一为逗号
                if (StringUtils.isNotBlank(evt.getOperativeWord())) {
                    if (evt.getOperativeWord().contains(";")) {
                        evt.setOperativeWord(evt.getOperativeWord().replace(";", ","));
                    } else if (evt.getOperativeWord().contains("；")) {
                        evt.setOperativeWord(evt.getOperativeWord().replace("；", ","));
                    }
                }
                
                // 复制属性：将事件对象属性拷贝到实体对象
                BeanUtil.copyProperties(evt, knowledgeBases);
                
                // 根据文件路径设置文档类型
                if (StringUtils.isNotBlank(evt.getKnowledgeFilePath())) {
                    String fileName = FileUtil.getFileName(evt.getKnowledgeFilePath());
                    String fileType = FileUtil.getTypePart(fileName);
                    if (StringUtils.isNotBlank(fileType)) {
                        knowledgeBases.setDocumentFormat(fileType.toLowerCase());
                    }
                }
                
                // 设置通用属性
                knowledgeBases.setIsDeleted(CommonConstant.IS_DELETED); // 删除状态
                //knowledgeBases.setAuditStatus(CommonConstant.STATUS_PASS); // 审核状态
                
                // 设置上报状态初始值
                if (knowledgeBases.getReportStatus() == null) {
                    knowledgeBases.setReportStatus(ReportStatusConstant.NOT_REPORTED);
                }
                
                // 如果权限开关打开，设置默认权限
                if (commonConfig.getPerSwitch()) {
                    dtPermissionConverter.initPermission(knowledgeBases,
                            DataPermissionConstant.BASES,
                            Long.valueOf(evt.getCategoryId()));
                }
                
                // 设置知识库状态和搜索次数  批量导入默认待审核
                knowledgeBases.setState(CaseStateEnum.PENDING_AUDIT.getStateValue());
                knowledgeBases.setSearchNumber(0);
                knowledgeBases.setCreatedUserName(PtSecurityUtils.getUsername());
                
                // 添加到批量插入集合
                knowledgeBasesToInsert.add(knowledgeBases);
                evtMap.put(evt.getKnowledgeName(), evt);
                entityMap.put(evt.getKnowledgeName(), knowledgeBases);
                
            } catch (Exception e) {
                // 处理异常
                log.error("准备知识库实体对象失败: {}", e.getMessage(), e);
                result.put("message", "导入失败: " + e.getMessage());
                results.add(result);
            }
        }
        
        // 如果没有要插入的知识库，直接返回
        if (knowledgeBasesToInsert.isEmpty()) {
            for (Map<String, Object> result : resultMap.values()) {
                if (!results.contains(result)) {
                    results.add(result);
                }
            }
            return;
        }
        
        // 5. 批量插入知识库
        try {
            log.info("开始批量插入知识库，数量: {}", knowledgeBasesToInsert.size());
            
            // 5.1 批量插入知识库实体
            for (KnowledgeBasesEntity entity : knowledgeBasesToInsert) {
                knowledgeBasesDMapper.insert(entity);
                
                // 获取插入后的ID
                Long kgKnowledgeBasesId = entity.getKgKnowledgeBasesId();
                if (kgKnowledgeBasesId != null) {
                    successfulKnowledgeBaseIds.add(kgKnowledgeBasesId);
                    
                    // 5.2 准备知识空间关联关系
                    InsertKnowledgeBasesEvt evt = evtMap.get(entity.getKnowledgeName());
                    KgBasesSpaceRelationC relation = new KgBasesSpaceRelationC();
                    relation.setKgBasesSpaceCId(evt.getKgBasesSpaceCId());
                    relation.setKgKnowledgeBasesId(kgKnowledgeBasesId);
                    relationsToInsert.add(relation);
                    
                    // 5.3 准备ES索引数据
                    KnowledgeBasesIdx index = new KnowledgeBasesIdx();
                    BeanUtil.copyProperties(entity, index);
                    extracted(entity, index);
                    index.setFullText();
                    indexesToSave.add(index);
                    
                    // 5.4 文件内容存到缓存
                    savefileContentRedis(evt, entity);
                    
                    // 5.5 更新结果
                    Map<String, Object> result = resultMap.get(entity.getKnowledgeName());
                    result.put("success", true);
                    result.put("message", "导入成功");
                    result.put("kgKnowledgeBasesId", kgKnowledgeBasesId);
                }
            }
            
            // 6. 批量插入知识空间关联关系
            if (!relationsToInsert.isEmpty()) {
                log.info("开始批量插入知识空间关联关系，数量: {}", relationsToInsert.size());
                for (KgBasesSpaceRelationC relation : relationsToInsert) {
                    kgBasesSpaceRelationCMapper.insertKgBasesSpaceRelationC(relation);
                }
            }
            
            // 7. 批量保存ES索引
            if (!indexesToSave.isEmpty()) {
                log.info("开始批量保存ES索引数据，数量: {}", indexesToSave.size());
                try {
                    elasticsearchRestTemplate.save(indexesToSave);
                } catch (Exception ex) {
                    if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                        throw new KnowledgeGraphException("更新失败:{}", ex.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("批量处理失败: {}", e.getMessage(), e);
            
            // 设置所有未处理的结果为失败
            for (Map<String, Object> result : resultMap.values()) {
                if (!result.containsKey("message") || !result.get("message").equals("导入成功")) {
                    result.put("message", "导入失败: " + e.getMessage());
                    if (!results.contains(result)) {
                        results.add(result);
                    }
                }
            }
            
            // 抛出异常使事务回滚
            throw new KnowledgeGraphException("批量导入失败，事务已回滚", e);
        }
        
        // 8. 将剩余结果添加到结果集合
        for (Map<String, Object> result : resultMap.values()) {
            if (!results.contains(result)) {
                results.add(result);
            }
        }
    }

    @Autowired
    private ReportFormatUtil reportFormatUtil;
    
    @Autowired
    private KnowledgeReportClient knowledgeReportClient;

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public List<KnowledgeBasesEntity> batchUploadBases(MultipartFile[] files) throws IOException {

        // 定义允许的文件类型集合
        Set<String> allowedFileTypes = new HashSet<>(Arrays.asList(
                "html", "htm",         // HTML文档
                "pdf",                 // PDF文档
                "doc", "docx",         // Word文档
                "xls", "xlsx",         // Excel文档
                "ppt", "pptx"          // PowerPoint文档
        ));

        // 初始化返回结果列表
        List<KnowledgeBasesEntity> evts = new ArrayList<>();
        MultipartFile analyseFile = null;
        // 遍历所有上传的文件
        for (MultipartFile file : files) {
            // 为每个文件创建一个知识库实体对象
            KnowledgeBasesEntity knowledgeBasesEntity = new KnowledgeBasesEntity();
            analyseFile = file;

            try {
                // 1. 获取文件名并进行基本校验
                String fileName = file.getOriginalFilename();
                if (StringUtils.isEmpty(fileName)) {
                    knowledgeBasesEntity.setUploadResult("文件名不能为空");
                    evts.add(knowledgeBasesEntity);
                    continue;  // 跳过当前文件，处理下一个
                }

                // 2. 提取不带扩展名的文件名
                int lastDotIndex = fileName.lastIndexOf('.');
                String fileNameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
                
                // 3. 检查数据库中是否已存在同名知识库，如果存在则自动添加时间戳
                String finalKnowledgeName = fileNameWithoutExt;
                LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(KnowledgeBasesEntity::getKnowledgeName, fileNameWithoutExt);
                wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
                if (!knowledgeBasesDMapper.selectList(wrapper).isEmpty()) {
                    // 如果重名，自动添加时间戳
                    finalKnowledgeName = reportFormatUtil.generateTimestampedFileName(fileNameWithoutExt);
                    log.info("检测到文件重名，自动修改文件名: {} -> {}", fileNameWithoutExt, finalKnowledgeName);
                }
                knowledgeBasesEntity.setKnowledgeName(finalKnowledgeName);

                // 5. 检查文件类型是否合法
                String typePart = FileUtil.getTypePart(fileName).toLowerCase();
                if (!allowedFileTypes.contains(typePart)) {
                    knowledgeBasesEntity.setUploadResult(ExceptionDefinition.FILE_TYPE_ERROR.getMsg()
                            + "，仅支持: html/pdf/word/excel/ppt 格式");
                    evts.add(knowledgeBasesEntity);
                    continue;
                }

                // 6. 检查文件内容是否为空
                InputStream inputStream = file.getInputStream();
                if (ObjectUtils.isEmpty(inputStream)) {
                    knowledgeBasesEntity.setUploadResult(ExceptionDefinition.FILE_EMPY_ERROR.getMsg());
                    evts.add(knowledgeBasesEntity);
                    continue;
                }

                // 7. 设置知识库实体基本属性
                //knowledgeBasesEntity.setAuditStatus("1");  // 审核状态
                knowledgeBasesEntity.setIsDeleted("0");    // 未删除
                knowledgeBasesEntity.setDocumentFormat(typePart);  // 文件格式
                knowledgeBasesEntity.setSearchNumber(0);   // 搜索次数初始为0
                knowledgeBasesEntity.setCreatedUserName(PtSecurityUtils.getUsername());  // 创建人
                // 导入成功直接提交审核
                knowledgeBasesEntity.setState(CaseStateEnum.PENDING_AUDIT.getStateValue());
                
                // 设置上报状态初始值
                knowledgeBasesEntity.setReportStatus(ReportStatusConstant.NOT_REPORTED);

                // 8. 上传文件到文件系统
                log.info("开始上传文件: {}", fileName);
                FileInfo filePath;
                try {
                    filePath = docService.uploadFile(file, CommonConstant.UPLOAD_BASES_TYPE);
                    knowledgeBasesEntity.setKnowledgeFilePath(filePath.getFilePath());
                    knowledgeBasesEntity.setUploadResult("成功");
                    knowledgeBasesEntity.setUploadProgress(100);
                    knowledgeBasesEntity.setFilesize(filePath.getFileSize());
                } catch (Exception e) {
                    knowledgeBasesEntity.setUploadResult("文件上传失败: " + e.getMessage());
                    // 失败时处理
                    Random random = new Random();
                    int randomProgress = random.nextInt(98) + 1;
                    knowledgeBasesEntity.setUploadProgress(randomProgress);
                }
                String fileContent = null;

                try {
                    fileContent = kgContiDService.analyzeFile(analyseFile);
                    knowledgeBasesEntity.setFileContent(fileContent);
                } catch (Exception e) {
                    knowledgeBasesEntity.setUploadResult("文件分析失败: " + e.getMessage());
                }
                // 9. 将处理完成的实体加入结果列表
                evts.add(knowledgeBasesEntity);

            } catch (Exception e) {
                // 10. 捕获处理过程中的任何意外异常，失败时处理
                Random random = new Random();
                int randomProgress = random.nextInt(98) + 1;

                log.error("文件处理失败，设置随机进度值: {}", randomProgress, e);
                knowledgeBasesEntity.setUploadResult("处理文件时发生错误: " + e.getMessage());
                knowledgeBasesEntity.setUploadProgress(randomProgress);
                evts.add(knowledgeBasesEntity);
            }
        }

        // 返回所有文件处理结果
        return evts;
    }

    private boolean checkRepeat(BasesCheckRepeatEvt evt) {
        List<Long> kgRawCaseIds = evt.getKgbsIds();
        for (Long kgRawCaseId : kgRawCaseIds) {
            KnowledgeBasesIdx knowledgeBasesIdx = elasticsearchRestTemplate.get(String.valueOf(kgRawCaseId), KnowledgeBasesIdx.class);
            String fullText = knowledgeBasesIdx.getFullText();
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(QueryBuilders.boolQuery()
                            .filter(QueryBuilders.termsQuery("isDeleted", CommonConstant.NO))
                            .filter(QueryBuilders.termsQuery("auditStatus", CommonConstant.YES))
                            .mustNot(QueryBuilders.termQuery("kgKnowledgeBasesId", kgRawCaseId))
                    ).build();
            SearchHits<KnowledgeBasesIdx> searchHits = elasticsearchRestTemplate.search(query, KnowledgeBasesIdx.class);
            List<SearchHit<KnowledgeBasesIdx>> searchHitsList = searchHits.getSearchHits();
            List<KnowledgeBasesIdx> knowledgeBasesIdxes = new ArrayList<>();
            for (SearchHit<KnowledgeBasesIdx> rawCaseIdxSearchHit : searchHitsList) {
                KnowledgeBasesIdx content = rawCaseIdxSearchHit.getContent();
                knowledgeBasesIdxes.add(content);
            }
            double repeatRate = 0;
            String caseTitle = " ";
            //查询出所有的文档进行查重
            for (KnowledgeBasesIdx caseIdx : knowledgeBasesIdxes) {
                repeatRate = TextPlagiarismCheckUtil.calculateCosSimilarity(fullText, caseIdx.getFullText());
                if (repeatRate > commonConfig.getRepeatRate()) {
                    log.info("知识id:{},对比知识id:{}", knowledgeBasesIdx, caseIdx.getKgKnowledgeBasesId());
                    log.info("知识内容:{},对比知识内容:{}", knowledgeBasesIdx.getFullText(), caseIdx.getFullText());
                    log.info("查重率:{}", repeatRate);
                    caseTitle = caseIdx.getKnowledgeName();
                    break;
                }
            }
            KnowledgeBasesEntity knowledgeBasesEntity = new KnowledgeBasesEntity();
            knowledgeBasesEntity.setKgKnowledgeBasesId(kgRawCaseId);
            knowledgeBasesEntity.setCheckRepeatResult(BigDecimal.valueOf(repeatRate));
            knowledgeBasesEntity.setSimilarKnowledgeBases(caseTitle);
            knowledgeBasesDMapper.updateById(knowledgeBasesEntity);
            try {
                //es更新
                Map<String, Object> map = new HashMap<>();
                map.put("checkRepeatResult", repeatRate);
                map.put("similarKnowledgeBases", caseTitle);
                String toJson = JsonUtil.objectToJson(map);
                UpdateQuery updateQuery = UpdateQuery.builder(String.valueOf(kgRawCaseId))
                        .withDocument(Document.parse(toJson))
                        .build();
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            } catch (JsonProcessingException e) {
                log.info("es更新异常:{}", e.getMessage());
            }
        }
        return true;
    }

    private String getCurrentUser() {
        // 从安全上下文获取当前用户，示例返回固定值
        return PtSecurityUtils.getUsername();
    }

    private void validateBasesSpace(KgBasesSpaceEvt evt) {
        if (kgBasesSpaceCMapper.existsByName(evt.getBasesSpaceName())) {
            throw new KnowledgeGraphException("知识空间名称已存在");
        }
        if (kgBasesSpaceCMapper.existsByCode(evt.getBasesSpaceCode())) {
            throw new KnowledgeGraphException("知识空间编码已存在");
        }
    }

    private void getWordStyle(XWPFRun abstractRun) {
        abstractRun.setFontFamily("仿宋_GB2312");
        abstractRun.setFontSize(16);
        abstractRun.setBold(true);
    }

    private void extracted(XWPFRun abstractRunChild) {
        abstractRunChild.setFontFamily("仿宋_GB2312");
        abstractRunChild.setFontSize(16);
        abstractRunChild.setColor("808080");
    }

    private static void setOrgianlData(KnowledgeBasesEntity knowledgeBasesEntity, KnowledgeBasesEntity knowledgeBasesEntityOriginal) {
        knowledgeBasesEntity.setSearchNumber(knowledgeBasesEntityOriginal.getSearchNumber());
        knowledgeBasesEntity.setCreatedTime(knowledgeBasesEntityOriginal.getCreatedTime());
        knowledgeBasesEntity.setUpdatedTime(new Date());
        knowledgeBasesEntity.setCreatedUserName(knowledgeBasesEntityOriginal.getCreatedUserName());
        knowledgeBasesEntity.setUpdatedUserName(PtSecurityUtils.getCurrentUserLogin().orElse("system"));
    }

    private void extracted(KnowledgeBasesEntity knowledgeBases, KnowledgeBasesIdx knowledgeBasesIdx) {
        knowledgeBasesIdx.setCreatedTime(knowledgeBases.getCreatedTime());
        knowledgeBasesIdx.setCreatedUserName(knowledgeBases.getCreatedUserName());
        knowledgeBasesIdx.setUpdatedTime(knowledgeBases.getUpdatedTime());
        knowledgeBasesIdx.setUpdatedTime(knowledgeBases.getUpdatedTime());
    }

    private void checkBases(InsertKnowledgeBasesEvt evt) throws KnowledgeGraphException {
        //标题
        if (ObjectUtils.isEmpty(evt.getKnowledgeName())) {
            throw new KnowledgeGraphException(ExceptionDefinition.BASES_TITLE_NUMBER_LIMIT_ERROR);
        }
    }

    public PageInfo<KnowledgeBasesEntity> getCasesOnFullText(GetBasesEvt evt) {

        Pageable pageable = PageRequest.of(evt.getPageNo() - 1, evt.getPageSize());
        NativeSearchQuery searchQuery = null;

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("auditStatus", CommonConstant.YES))
                .filter(QueryBuilders.termsQuery("isDeleted", CommonConstant.NO));
        if (StringUtil.isNotEmpty(evt.getFullText())) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("fullText", evt.getFullText()));
        }
        if (CollectionUtils.isNotEmpty(evt.getMajor())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("major", evt.getMajor()));
        }
        if (CollectionUtils.isNotEmpty(evt.getRegion())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("region", evt.getRegion()));
        }

        if (CollectionUtils.isNotEmpty(evt.getStateMajor())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("major", evt.getStateMajor()));
        }
        if (CollectionUtils.isNotEmpty(evt.getStateRegion())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("region", evt.getStateRegion()));
        }

        if (CollectionUtils.isNotEmpty(evt.getState())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("state", evt.getState()));
        }
        DtPermissionConverter.esPermission(evt, boolQueryBuilder);
        searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withPageable(pageable)
                .withSort(CommonConstant.YES_INT.equals(evt.getWhetherClickCountSort()) ? SortBuilders.fieldSort("clickCount").order(SortOrder.DESC) : SortBuilders.fieldSort("topUp").order(SortOrder.DESC))
                .withSort(SortBuilders.fieldSort("updatedTime").order(SortOrder.DESC))
                .build();

        SearchHits<KnowledgeBasesIdx> searchHits = elasticsearchRestTemplate.search(searchQuery, KnowledgeBasesIdx.class);
        List<SearchHit<KnowledgeBasesIdx>> searchHitList = searchHits.getSearchHits();
        List<KnowledgeBasesEntity> knowledgeBasesEntities = new ArrayList<>();
        for (SearchHit<KnowledgeBasesIdx> knowledgeBasesIdxSearchHit : searchHitList) {
            KnowledgeBasesIdx content = knowledgeBasesIdxSearchHit.getContent();
            KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
            BeanUtil.copyProperties(content, knowledgeBases);
            if (StringUtils.isNotBlank(knowledgeBases.getOperativeWord())) {
                if (knowledgeBases.getOperativeWord().contains(",")) {
                    knowledgeBases.setOperativeWord(knowledgeBases.getOperativeWord().replace(",", ";"));
                }
            }
            knowledgeBasesEntities.add(knowledgeBases);
        }
        PageInfo pageInfo = new PageInfo(knowledgeBasesEntities);

        DtPermissionConverter.permissionConvert(pageInfo.getList(), evt);
        pageInfo.setTotal(searchHits.getTotalHits());
        return pageInfo;
    }

    public List<KnowledgeBasesEntity> getCasesOnFullTextNoHavePage(GetBasesEvt evt) {
        NativeSearchQuery searchQuery = null;
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("auditStatus", CommonConstant.YES))
                .filter(QueryBuilders.termsQuery("isDeleted", CommonConstant.NO));

        if (StringUtil.isNotEmpty(evt.getFullText())) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("fullText", evt.getFullText()));
        }
        if (CollectionUtils.isNotEmpty(evt.getMajor())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("major", evt.getMajor()));
        }
        if (CollectionUtils.isNotEmpty(evt.getRegion())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("region", evt.getRegion()));
        }

        if (CollectionUtils.isNotEmpty(evt.getStateMajor())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("major", evt.getStateMajor()));
        }
        if (CollectionUtils.isNotEmpty(evt.getStateRegion())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("region", evt.getStateRegion()));
        }

        if (CollectionUtils.isNotEmpty(evt.getState())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("state", evt.getState()));
        }
        DtPermissionConverter.esPermission(evt, boolQueryBuilder);
        searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("updatedTime").order(SortOrder.DESC))
                .build();
        SearchHits<KnowledgeBasesIdx> searchHits = elasticsearchRestTemplate.search(searchQuery, KnowledgeBasesIdx.class);
        List<SearchHit<KnowledgeBasesIdx>> searchHitList = searchHits.getSearchHits();
        List<KnowledgeBasesEntity> knowledgeBasesEntities = new ArrayList<>();
        for (SearchHit<KnowledgeBasesIdx> knowledgeBasesIdxSearchHit : searchHitList) {
            KnowledgeBasesIdx content = knowledgeBasesIdxSearchHit.getContent();
            KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
            BeanUtil.copyProperties(content, knowledgeBases);
            knowledgeBasesEntities.add(knowledgeBases);
        }
        return knowledgeBasesEntities;
    }

    /**
     * 调用集团文档接口
     *
     * @param docIds     文档ID列表
     * @param operType   操作类型：offLine-下线，delete-删除
     * @param operUserId 操作人ID
     * @param isOpen     是否上线：1-上线，0-下线
     * @return 是否成功
     */
    private boolean callGroupDocApi(List<Long> docIds, String operType, String operUserId, String isOpen) {
        // 状态码
        String statueCode = "";
        String bodys = "";
        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-APP-ID", "706e60176b326035c658dfb3b00627c6");
            headers.set("X-APP-KEY", "b67701ddcfbab49ba1df7f5244df07b8");
            // 获取所有的知识名作为id传参。
            List<String> docNames = knowledgeBasesDMapper.selectBatchIds(docIds).stream()
                    .map(KnowledgeBasesEntity::getKnowledgeName)
                    .collect(Collectors.toList());

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("docNames", docNames);
            requestBody.put("operType", operType);
            requestBody.put("operUserId", operUserId);
            if (operType.equals("offLine")) {
                requestBody.put("isOpen", isOpen);
            }
            String requestBodyString = objectMapper.writeValueAsString(requestBody);

            log.info("删除或下线: {}", requestBodyString);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(
                    knowledgeBaseConfig.getJt().getUpdDocUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("调用集团文档接口成功: {}", response.getStatusCode().is2xxSuccessful());

            if (response.getStatusCode().is2xxSuccessful()) {
                String body = response.getBody();
                log.info("调用成功状态: {}, 调用集团返回的body：{}", response.getStatusCode().is2xxSuccessful(), body);
                // 定义一个类来映射响应结构
                ApiResponseVm apiResponse = objectMapper.readValue(body, ApiResponseVm.class);

                log.info("操作结果: {}, 代码: {}, 数据: {}",
                        apiResponse.getMsg(),
                        apiResponse.getCode(),
                        apiResponse.getData());
                // 根据需要进行进一步处理
                if (apiResponse.getCode() == 200) {
                    return true;
                } else {
                    // 修改这里：抛出包含集团返回的错误信息的异常
                    String errorMsg = String.format("集团接口返回错误: 代码=%d, 消息=%s",
                            apiResponse.getCode(), apiResponse.getMsg());
                    statueCode = response.getStatusCode().toString();
                    bodys = response.getBody();
                    throw new KnowledgeGraphException(errorMsg);
                }
            } else {
                statueCode = response.getStatusCode().toString();
                bodys = response.getBody();
                // 失败时的处理（如抛异常或记录错误日志）
                log.error("调用集团接口失败，请求状态码: {}，错误信息: {}", response.getStatusCode(), response.getBody());
                throw new KnowledgeGraphException("调用集团接口失败，请求状态码: {}，错误信息: {}", response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.error("调用集团接口失败，请求状态码: {}，错误信息: {}", statueCode, bodys);
            throw new KnowledgeGraphException("调用集团接口失败，请求状态码: {}，错误信息: {}", statueCode, bodys);

        }
    }


    /**
     * 调用集团文档上报接口
     *
     * @return 是否成功
     */
    private boolean batchSaveFiles(BatchUpdateBasesStateEvt evt) {
        // 定义允许的文件类型集合
        Set<String> allowedFileTypes = new HashSet<>(Arrays.asList(
                "pdf",                 // PDF文档
                "doc", "docx",         // Word文档
                "ppt", "pptx","html","xls","xlsx"          // PowerPoint文档
        ));
        log.info("调用集团文档上报接口开始 - 请求参数: {}", evt.toString());

        List<Long> kgKnowledgeBasesIds = evt.getKgKnowledgeBasesIds();
        List<KnowledgeBasesEntity> knowledgeBasesEntities = knowledgeBasesDMapper.selectBatchIds(kgKnowledgeBasesIds);
        List<FileReq> fileReqs = new ArrayList<>();

        for (KnowledgeBasesEntity knowledgeBases : knowledgeBasesEntities) {
            String fileName = FileUtil.getFileName(knowledgeBases.getKnowledgeFilePath());
            String docType = FileUtil.getTypePart(fileName).toLowerCase();
            String kgName = knowledgeBases.getKnowledgeName() + "." + docType;

            // 文件类型校验
            if (!allowedFileTypes.contains(docType)) {
                log.error("文件类型不支持 - 文件名: {}, 文件类型: {}, 允许的文件类型: {}, 新的原文件名：{}",
                        fileName, docType, allowedFileTypes, kgName);
                throw new KnowledgeGraphException("文件上传失败: 文件类型不支持 - 文件类型: {}, 允许的文件类型: {}, 文件名：{}",
                        docType, allowedFileTypes, kgName);
            }

            // try-with-resources 保证流关闭
            try (InputStream inputStream = ctdfsService.downloadStream(knowledgeBases.getKnowledgeFilePath())) {
                if (inputStream == null) {
                    log.error("文件流获取失败 - 文件名: {}", fileName);
                    throw new KnowledgeGraphException("文件流获取失败: {}", fileName);
                }
                byte[] fileBytes = IOUtils.toByteArray(inputStream);
                MultipartFile multipartFile = new MockMultipartFile(
                        fileName,
                        fileName,
                        MediaType.APPLICATION_OCTET_STREAM_VALUE,
                        fileBytes
                );

                String filePath = null;
                // 上传文件到集团文档系统
                try {
                    log.info("开始上传文件到集团文档系统 - 文件名: {}", fileName);
                    filePath = jtDocService.uploadjtFile(multipartFile, fileName, kgName);
                    log.info("文件上传成功 - 文件名: {}, 集团文件路径: {}", fileName, filePath);
                } catch (IOException e) {
                    log.error("文件上传IO异常 - 文件名: {}, 错误详情: {}, 堆栈信息: ",
                            fileName, e.getMessage(), e);
                    throw new KnowledgeGraphException("文件上传失败: " + fileName + ", 原因: " + e.getMessage(), e);
                }

                // 构请求对象
                FileReq fileReq = new FileReq();
                fileReq.setKnName(knowledgeBases.getKnowledgeName());
                String stpFilePath = knowledgeBaseConfig.getJt().getBaseFilePath() + kgName;
                log.info("batchSaveFiles ftpPath入参：{}", stpFilePath);
                fileReq.setFileFtpPath(stpFilePath);
                fileReq.setFileType(docType);
                fileReq.setIsWdd(false);
                fileReq.setSceneLabels(convertIdsToCodeValues(knowledgeBases.getApplicationScene()));
                fileReq.setProfessionalLabels(convertIdsToCodeValues(knowledgeBases.getMajor()));
                fileReq.setProcessLabels(convertIdsToCodeValues(knowledgeBases.getFlowScene()));
                fileReq.setDocOrigins(convertIdsToCodeValues(knowledgeBases.getKnowledgeOrigin()));

                // 公开范围
                String publicity = knowledgeBases.getPublicity();
                DictionaryEntity dictionaryEntity = dictionaryMapper.selectById(Long.parseLong(publicity));
                fileReq.setOpenScope(dictionaryEntity.getCodeValue());

                // 有效期
                String periodValidity = knowledgeBases.getPeriodValidity();
                DictionaryEntity periodValidityDictionaryEntity = dictionaryMapper.selectById(Long.parseLong(periodValidity));
                fileReq.setPeriodValidity(periodValidityDictionaryEntity.getCodeValue());

                // 区域编码
                fileReq.setRegionCode(knowledgeBaseConfig.getJt().getRegionCode());
                fileReq.setAuthor(evt.getOperUserId());
                if(StringUtils.isNotBlank(knowledgeBases.getAuthor())){
                    fileReq.setAuthorName(knowledgeBases.getAuthor());
                }else {
                    fileReq.setAuthorName(knowledgeBaseConfig.getJt().getJtAuthor());
                }
                fileReq.setCreateBy(knowledgeBases.getCreatedUserName());
                fileReq.setDocTypes(convertIdsToCodeValues(knowledgeBases.getDocumentType()));
                fileReq.setFileSource("province");
                String requestBodyString = objectMapper.writeValueAsString(fileReq);
                log.debug("构建集团文档请求对象 - 文件名: {}, 请求详情: {}", fileName, requestBodyString);
                fileReqs.add(fileReq);
            } catch (Exception e) {
                log.error("处理知识库文件异常 - 知识库ID: {}, 知识库名称: {}, 错误详情: {}, 堆栈信息: ",
                        knowledgeBases.getKgKnowledgeBasesId(), knowledgeBases.getKnowledgeName(),
                        e.getMessage(), e);
                throw new KnowledgeGraphException("处理知识库文件失败: " + knowledgeBases.getKnowledgeName() + ", 原因: " + e.getMessage(), e);
            }
        }

        if (CollectionUtils.isEmpty(fileReqs)) {
            String errorMsg = "没有有效的文件可以上报 - 知识库ids: " + kgKnowledgeBasesIds;
            log.error(errorMsg);
            throw new KnowledgeGraphException(errorMsg);
        }

        BatchSaveFilesEvt batchSaveFilesEvt = new BatchSaveFilesEvt();
        batchSaveFilesEvt.setFileReqList(fileReqs);

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-APP-ID", "706e60176b326035c658dfb3b00627c6");
            headers.set("X-APP-KEY", "b67701ddcfbab49ba1df7f5244df07b8");
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("fileReqList", batchSaveFilesEvt.getFileReqList());
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 优化：使用RestTemplateUtil复用连接池
            String requestBodyString = objectMapper.writeValueAsString(requestBody);
            log.info("准备调用集团批量保存接口 - 请求URL: {}, 请求头: {}, 请求体: {}",
                    knowledgeBaseConfig.getJt().getBatchSaveFilesUrl(), headers, requestBodyString);

            ResponseEntity<String> response = RestTemplateUtil.exchange(
                    knowledgeBaseConfig.getJt().getBatchSaveFilesUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                String body = response.getBody();
                log.info("集团接口调用成功 - 状态码: {}, 响应体: {}", response.getStatusCode(), body);

                ApiResponseVm apiResponse = objectMapper.readValue(body, ApiResponseVm.class);
                log.info("集团接口返回结果 - 消息: {}, 代码: {}, 数据: {}",
                        apiResponse.getMsg(), apiResponse.getCode(), apiResponse.getData());

                if (apiResponse.getCode() == 200) {
                    // 处理成功情况
                    for (KnowledgeBasesEntity knowledgeBases : knowledgeBasesEntities) {
                        knowledgeBases.setReportStatus(evt.getState());
                        knowledgeBases.setReportTime(new Date());
                        knowledgeBases.setReportBatchNo(createReportBatch(evt.getState(), getCurrentUser(), new Date()));
                        knowledgeBasesDMapper.updateById(knowledgeBases);
                    }
                    return true;
                } else {
                    String errorMsg = "集团接口返回非200状态 - 代码: " + apiResponse.getCode() + ", 消息: " + apiResponse.getMsg();
                    log.error(errorMsg);
                    throw new KnowledgeGraphException(errorMsg);
                }
            } else {
                String errorMsg = "集团接口调用失败 - 状态码: " + response.getStatusCode() + ", 响应体: " + response.getBody();
                log.error(errorMsg);
                throw new KnowledgeGraphException(errorMsg);
            }
        } catch (JsonProcessingException e) {
            String errorMsg = "JSON处理异常 - 错误详情: " + e.getMessage();
            log.error(errorMsg, e);
            throw new KnowledgeGraphException(errorMsg, e);
        } catch (Exception e) {
            String errorMsg = "调用集团文档接口发生未知异常 - 错误详情: " + e.getMessage();
            log.error(errorMsg, e);
            throw new KnowledgeGraphException(errorMsg, e);
        }
    }

    /**
     * 将逗号分隔的ID字符串转换为字典编码值列表
     *
     * @param ids 逗号分隔的ID字符串，可以是null、单个ID或多个ID
     * @return 字典编码值列表
     * @throws KnowledgeGraphException 当ID格式错误或字典查询失败时抛出
     */
    private List<String> convertIdsToCodeValues(String ids)
            throws KnowledgeGraphException {

        List<String> result = new ArrayList<>();
        // 如果输入为空或空白字符串，直接返回空列表
        if (ids == null || ids.trim().isEmpty()) {
            return result;
        }

        // 分割逗号分隔的字符串
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            String trimmedId = id.trim();
            // 跳过空白ID
            if (!trimmedId.isEmpty()) {
                try {
                    // 转换ID为Long并查询字典
                    Long dictionaryId = Long.parseLong(trimmedId);
                    DictionaryEntity dictionaryEntity = dictionaryMapper.selectById(dictionaryId);

                    // 验证查询结果
                    if (dictionaryEntity == null) {
                        throw new KnowledgeGraphException("字典ID[" + trimmedId + "]对应的记录不存在");
                    }
                    if (dictionaryEntity.getCodeValue() == null) {
                        throw new KnowledgeGraphException("字典ID[" + trimmedId + "]对应的编码值为空");
                    }

                    result.add(dictionaryEntity.getCodeValue());
                } catch (NumberFormatException e) {
                    // ID格式错误时抛出异常
                    throw new KnowledgeGraphException("非法的字典ID格式: " + trimmedId, e);
                }
            }
        }
        return result;
    }


    /**
     * 获取集团字典树http请求，get无参数请求，返回为Map形式（name作为key，code作为value）
     */
    private Map<String, String> getGroupDictTree() {

        RestTemplate restTemplate = new RestTemplate();

        // 创建请求头并添加 appid 和 appkey
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-APP-ID", "706e60176b326035c658dfb3b00627c6");
        headers.set("X-APP-KEY", "b67701ddcfbab49ba1df7f5244df07b8");

        // 将请求头封装到 HttpEntity 中（GET 请求无请求体，传 null）
        HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
        log.info("请求3c接口入参为: {}", requestEntity.toString());

        // 使用 exchange 方法发送 GET 请求，并接收响应
        ResponseEntity<String> response = restTemplate.exchange(
                knowledgeBaseConfig.getJt().getGet3CTreeUrl(),
                HttpMethod.GET,
                requestEntity,
                String.class
        );

        // 解析JSON响应
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> resultMap = new HashMap<>();

        log.info("返回的报文: {}", response.getBody().toString());

        try {
            JsonNode rootNode = objectMapper.readTree(response.getBody());
            JsonNode dataNode = rootNode.path("data");
            log.info("请求集团3c并成功解析返回数据: {}", dataNode.toString());
            if (dataNode.isArray()) {
                for (JsonNode typeNode : dataNode) {
                    JsonNode labelTrees = typeNode.path("labelTrees");
                    if (labelTrees.isArray()) {
                        processLabelTrees(labelTrees, resultMap);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("获取集团字典：{}", resultMap);
        return resultMap;
    }

    /**
     * 递归处理labelTrees节点，将name和code映射存入resultMap
     */
    private void processLabelTrees(JsonNode labelTrees, Map<String, String> resultMap) {
        for (JsonNode node : labelTrees) {
            // 添加当前节点的name和code映射
            String name = node.path("name").asText();
            String code = node.path("code").asText();
            if (!name.isEmpty() && !code.isEmpty()) {
                resultMap.put(name, code);
            }

            // 递归处理子节点
            JsonNode children = node.path("children");
            if (children != null && children.isArray()) {
                processLabelTrees(children, resultMap);
            }
        }
    }

    /**
     * 文件查重并存储结果到redis
     *
     * @param kgKnowledgeBasesId 当前知识库文件ID
     */
    @Override
    public void checkFileRepeatAndSaveToRedis(Long kgKnowledgeBasesId, boolean fromPage) {
        // 判断是否启用查重功能
        if (!knowledgeBaseConfig.getSimilarity().isCheck() || fileSimilarityClient == null) {
            log.info("文件查重功能未启用或服务不可用，跳过查重操作");
            return;
        }
        
        // 调用文件相似度服务进行查重
        try {
            fileSimilarityClient.checkFileSimilarity(kgKnowledgeBasesId, fromPage);
            log.info("文件查重请求已发送，知识库ID: {}", kgKnowledgeBasesId);
        } catch (Exception e) {
            log.error("调用文件查重服务失败", e);
        }
    }

    private void checkFilesRepeat(List<Long> kgKnowledgeBasesIds) {
        // 判断是否启用查重功能
        if (!knowledgeBaseConfig.getSimilarity().isCheck() || fileSimilarityClient == null) {
            log.info("文件查重功能未启用或服务不可用，跳过批量查重操作");
            return;
        }
        
        // 调用文件相似度服务进行批量查重
        try {
            fileSimilarityClient.batchCheckFileSimilarity(kgKnowledgeBasesIds);
            log.info("批量文件查重请求已发送，知识库ID数量: {}", kgKnowledgeBasesIds.size());
        } catch (Exception e) {
            log.error("调用批量文件查重服务失败", e);
        }
    }

    @Override
    public List<SimilarFileInfo> getFileSimilarityListFromRedis(Long kgKnowledgeBasesId) {
        // 判断是否启用查重功能
        if (!knowledgeBaseConfig.getSimilarity().isCheck() || fileSimilarityClient == null) {
            log.info("文件查重功能未启用或服务不可用，返回空列表");
            return new ArrayList<>();
        }
        
        // 调用文件相似度服务获取文件相似度列表
        try {
            return fileSimilarityClient.getFileSimilarityList(kgKnowledgeBasesId);
        } catch (Exception e) {
            log.error("获取文件相似度列表失败", e);
            return new ArrayList<>();
        }
    }

    private boolean importqw(KnowledgeBasesEntity knowledge, MultipartFile file) throws JsonProcessingException {

        if (file == null || knowledge == null) {
            return false;
        }

        try (InputStream inputStream = file.getInputStream()) {

            Workbook workbook = new XSSFWorkbook(inputStream);

            // 1. 智能识别问答对模板Sheet和问答对Sheet
            Sheet sceneSheet = null;
            Sheet qaSheet = null;

            // 遍历所有Sheet页
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet currentSheet = workbook.getSheetAt(i);
                Row firstRow = currentSheet.getRow(0);

                if (firstRow == null) {
                    continue; // 空Sheet跳过
                }

                // 检查是否包含"问题类别"列（问答对模板表特征）
                boolean isSceneSheet = containsColumn(firstRow, "应用场景");
                // 检查Sheet名称是否包含"问答对"
                boolean nameContainsQA = currentSheet.getSheetName().contains("问答对");

                if (isSceneSheet && !nameContainsQA) {
                    sceneSheet = currentSheet;
                } else if (nameContainsQA || (containsColumn(firstRow, "应用场景") && containsColumn(firstRow, "专业领域"))) {
                    qaSheet = currentSheet;
                }
            }

            // 2. 验证必须的Sheet是否存在
            if (sceneSheet == null) {
                throw new RuntimeException("未找到有效的问答对模板表（需包含'问题类别'列）");
            }

            // 3. 验证问答对模板表结构
            if (!validateSceneSheetStructure(sceneSheet)) {
                throw new RuntimeException("问答对模板表结构不符合要求");
            }

            // 4. 获取知识库实体

            // 5. 如果有问答对Sheet，验证并解析
            if (qaSheet != null) {
                if (!validateQASheetStructure(qaSheet)) {
                    throw new RuntimeException("问答对模板结构不符合要求");
                }

                // 解析问答对数据并填充到knowledge对象
                parseQAPairs(qaSheet, knowledge);

                // 检查是否解析到有效问答对
                if (knowledge.getQuestion() == null || knowledge.getQuestion().isEmpty() ||
                        knowledge.getAnswer() == null || knowledge.getAnswer().isEmpty()) {
                    throw new RuntimeException("问答对模板中没有有效数据");
                }
            }

            // 6. 验证问答对数据是否符合场景要求
            Map<String, List<String>> selectedValues = buildSelectedValues(knowledge);
            ExcelValidator validator = new ExcelValidator(sceneSheet);
            if (!validator.validateSelection(selectedValues)) {
                throw new RuntimeException("选择的值与问答对模板不匹配");
            }

            // 7. 设置其他必要属性
            knowledge.setCreatedTime(new Date());
            knowledge.setUpdatedTime(new Date());
            knowledge.setIsDeleted("0"); // 默认为未删除

            // 8. 保存到数据库（示例代码，根据实际需求修改）
            // knowledgeBasesMapper.insert(knowledge);

            return true;
        } catch (IOException e) {
            throw new RuntimeException("处理Excel文件失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public KnowledgeBasesEntity importqwkbs(HttpServletRequest request, MultipartFile multipartFile) throws IOException {

        String knowledgeBasesEntityString = request.getParameter("insertKnowledgeBases");

        if (multipartFile == null || knowledgeBasesEntityString == null) {
            return null;
        }

        // 文件上传到
        InsertKnowledgeBasesEvt evt = objectMapper.readValue(knowledgeBasesEntityString, InsertKnowledgeBasesEvt.class);

        if (evt.getKgBasesSpaceCId() == null) {
            throw new KnowledgeGraphException("请选择知识空间");
        }
        // 校验标题
        checkBases(evt);

        KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
        KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();

        // 替换操作词汇中的分号为逗号，以统一格式
        if (StringUtils.isNotBlank(evt.getOperativeWord())) {
            if (evt.getOperativeWord().contains(";")) {
                evt.setOperativeWord(evt.getOperativeWord().replace(";", ","));
            } else if (evt.getOperativeWord().contains("；")) {
                evt.setOperativeWord(evt.getOperativeWord().replace("；", ","));
            }
        }

        // 将事件对象属性复制到知识库实体对象
        BeanUtil.copyProperties(evt, knowledgeBases);

        // 添加excel校验
        boolean result = importExcelWithStrictValidation(knowledgeBases, multipartFile);
        // 校验失败
        if (!result) {
            return null;
        }
        // 设置通用的属性值
        knowledgeBases.setIsDeleted(CommonConstant.IS_DELETED);
        //knowledgeBases.setAuditStatus(CommonConstant.STATUS_PASS);

        // 判断是否为新生成的知识库
        if (ObjectUtils.isEmpty(evt.getKgKnowledgeBasesId())) {
            // 添加默认权限
            if (commonConfig.getPerSwitch()) {
                //添加默认权限
                dtPermissionConverter.initPermission(knowledgeBases, DataPermissionConstant.BASES, Long.valueOf(evt.getCategoryId()));
            }
            // 获取文件原始名称
            String fileName = multipartFile.getOriginalFilename();
            // 获取文件扩展名
            //String fileSuffix = FilenameUtils.getExtension(fileName);
            // 上传文件并获取服务器保存的文件路径
            FileInfo filePath = docService.uploadFile(multipartFile, CommonConstant.UPLOAD_BASES_TYPE);
            // 查找最后一个 "." 的位置
            int lastDotIndex = fileName.lastIndexOf('.');
            // 提取文件名部分（不含扩展名）
            String fileNameWithoutExt = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
            // 设置知识库状态为"新建"  直接默认待审核
            knowledgeBases.setState(CaseStateEnum.PENDING_AUDIT.getStateValue());
            knowledgeBases.setKnowledgeName(fileNameWithoutExt);
            knowledgeBases.setKnowledgeFilePath(filePath.getFilePath());
            String typePart = FileUtil.getTypePart(fileName).toLowerCase();
            knowledgeBases.setDocumentFormat(typePart);
            log.debug("文档：{}导入的格式是：{}", knowledgeBases.getKnowledgeName(), typePart);

            // 检查是否存在相同名称的知识库
            LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KnowledgeBasesEntity::getKnowledgeName, evt.getKnowledgeName());
            wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
            List<KnowledgeBasesEntity> knowledgeBasesList = knowledgeBasesDMapper.selectList(wrapper);

            if (ObjectUtils.isEmpty(knowledgeBasesList)) {
                knowledgeBases.setSearchNumber(0);
                try {
                    // 存储到PG数据库
                    knowledgeBases.setCreatedUserName(PtSecurityUtils.getUsername());
                    knowledgeBasesDMapper.insert(knowledgeBases);
                    // 存储到知识空间与知识关联关系表
                    KgBasesSpaceRelationC kgBasesSpaceRelationC = new KgBasesSpaceRelationC();
                    kgBasesSpaceRelationC.setKgBasesSpaceCId(evt.getKgBasesSpaceCId());
                    kgBasesSpaceRelationC.setKgKnowledgeBasesId(knowledgeBases.getKgKnowledgeBasesId());
                    kgBasesSpaceRelationC.setBaseType(KnowModelType.KNOWLEDGE_BASE.getType());
                    kgBasesSpaceRelationCMapper.insertKgBasesSpaceRelationC(kgBasesSpaceRelationC);

                    // 存储到ES
                    BeanUtil.copyProperties(knowledgeBases, knowledgeBasesIdx);
                    extracted(knowledgeBases, knowledgeBasesIdx);
                    knowledgeBasesIdx.setFullText();
                    elasticsearchRestTemplate.save(knowledgeBasesIdx);
                } catch (Exception e) {
                    if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                        log.error("es新增失败，异常信息：{}", e.getMessage());
                        throw new KnowledgeGraphException("新增失败:{}", e.getMessage());
                    }
                }
                //文件内容存到缓存
                savefileContentRedis(evt, knowledgeBases);
                //异步查重
                if (knowledgeBases.getKgKnowledgeBasesId() != null) {
                    checkFileRepeatAndSaveToRedis(knowledgeBases.getKgKnowledgeBasesId(), false);
                }
                return knowledgeBases;
            } else {
                throw new KnowledgeGraphException(ExceptionDefinition.BASES_TITLE_RET_ERROR.getMsg());
            }
        } else {
            return null;
        }
    }

    /**
     * 从Excel文件导入问答对到问答对表
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param spaceCId 空间ID
     * @param filePath 文件路径
     * @return 包含导入统计信息的Map，包括成功和失败数量
     */
    private Map<String, Integer> importQaPairsFromExcel(Long kgKnowledgeBasesId, Long spaceCId, String filePath) {
        Map<String, Integer> result = new HashMap<>();
        result.put("success", 0);
        result.put("fail", 0);
        
        try {
            // 创建临时文件用于存储下载的Excel
            File tempFile = File.createTempFile("qa_import_", ".xlsx");

            // 下载Excel文件到临时文件
            try (InputStream inputStream = ctdfsService.downloadStream(filePath);
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {

                if (inputStream == null) {
                    log.error("导入问答对Excel文件下载失败: {}", filePath);
                    result.put("fail", 1);
                    return result;
                }

                IOUtils.copy(inputStream, outputStream);
            }

            // 获取知识库信息以提取组织机构等信息
            KnowledgeBasesEntity knowledgeBase = knowledgeBasesDMapper.selectById(kgKnowledgeBasesId);
            String organization = knowledgeBase != null ? knowledgeBase.getInstitution() : null;
            String knowledgeOrigin = knowledgeBase != null ? knowledgeBase.getKnowledgeOrigin() : null;
            String lifeCycle = knowledgeBase != null ? knowledgeBase.getLifeCycle() : null;
            String author = knowledgeBase != null ? knowledgeBase.getAuthor() : null;
            Long region = knowledgeBase != null ? knowledgeBase.getRegion() : null;

            // 获取问答对服务并调用导入方法
            if (qaPairService != null) {
                List<KgQaPairD> importedQaPairs = qaPairService.importQaPairsFromKnowledgeBase(
                        kgKnowledgeBasesId,
                        tempFile.getAbsolutePath(),
                        organization,
                        knowledgeOrigin,
                        lifeCycle,
                        author,
                        region,spaceCId
                );

                if (importedQaPairs != null && !importedQaPairs.isEmpty()) {
                    int successCount = importedQaPairs.size();
                    log.info("成功从知识库ID={}导入{}个问答对", kgKnowledgeBasesId, successCount);
                    result.put("success", successCount);
                } else {
                    log.info("从知识库ID={}导入问答对失败或未找到有效问答对", kgKnowledgeBasesId);
                    result.put("fail", 1);
                }
            } else {
                log.error("无法获取问答对服务实例");
                result.put("fail", 1);
            }

            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        } catch (Exception e) {
            log.error("导入问答对到问答对表失败", e);
            result.put("fail", 1);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public KnowledgeBasesEntity importqwkbss(InsertKnowledgeBasesEvt evt) {
        if (evt.getKgBasesSpaceCId() == null) {
            throw new KnowledgeGraphException("请选择知识空间");
        }
        // 校验标题
        checkBases(evt);

        KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
        KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();

        // 替换操作词汇中的分号为逗号，以统一格式
        if (StringUtils.isNotBlank(evt.getOperativeWord())) {
            if (evt.getOperativeWord().contains(";")) {
                evt.setOperativeWord(evt.getOperativeWord().replace(";", ","));
            } else if (evt.getOperativeWord().contains("；")) {
                evt.setOperativeWord(evt.getOperativeWord().replace("；", ","));
            }
        }

        // 将事件对象属性复制到知识库实体对象
        BeanUtil.copyProperties(evt, knowledgeBases);

        boolean result = false;
        String filePath = evt.getKnowledgeFilePath();
        // 文件上传到
        try (InputStream inputStream = ctdfsService.downloadStream(filePath)) {
            String fileName = FileUtil.getFileName(filePath);
            if (inputStream == null) {
                throw new KnowledgeGraphException("文件下载失败，未获取到文件流");
            }
            byte[] fileBytes = IOUtils.toByteArray(inputStream);
            // 设置文件大小
            long fileSize = fileBytes.length;  // This gets the size in bytes
            knowledgeBases.setFilesize(fileSize);  // Assuming setFilesize takes a long parameter
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    MediaType.APPLICATION_OCTET_STREAM_VALUE,
                    fileBytes
            );
            // 添加excel校验
            result = importExcelWithStrictValidation(knowledgeBases, multipartFile);
        } catch (Exception e) {
            log.error("下载并解析文件内容失败", e);
            throw new KnowledgeGraphException("下载并解析文件内容失败: " + e.getMessage());
        }
        // 校验失败
        if (!result) {
            return null;
        }
        // 设置通用的属性值
        knowledgeBases.setIsDeleted(CommonConstant.IS_DELETED);
        //knowledgeBases.setAuditStatus(CommonConstant.STATUS_PASS);
        knowledgeBases.setCategoryId(Long.valueOf(knowledgeBaseConfig.getQwd().getCategoryId()));
        evt.setCategoryId(Integer.valueOf(knowledgeBaseConfig.getQwd().getCategoryId()));

        // 设置文档格式
        if (StringUtils.isNotBlank(evt.getKnowledgeFilePath())) {
            String fileName = FileUtil.getFileName(evt.getKnowledgeFilePath());
            String fileType = FileUtil.getTypePart(fileName);
            if (StringUtils.isNotBlank(fileType)) {
                knowledgeBases.setDocumentFormat(fileType.toLowerCase());
            }
        }
        
        // 导入统计结果
        Map<String, Integer> importStats = new HashMap<>();
        importStats.put("success", 0);
        importStats.put("fail", 0);
        
        // 判断是否为新生成的知识库
        if (ObjectUtils.isEmpty(evt.getKgKnowledgeBasesId())) {
            // 添加默认权限
            if (commonConfig.getPerSwitch()) {
                //添加默认权限
                if (evt.getCategoryId() != null) {
                    dtPermissionConverter.initPermission(knowledgeBases, DataPermissionConstant.BASES, Long.valueOf(evt.getCategoryId()));
                }
            }
            // 导入时直接设置为待审核状态
            knowledgeBases.setState(CaseStateEnum.PENDING_AUDIT.getStateValue());

            // 检查是否存在相同名称的知识库，如果重名则自动添加时间戳
            String finalKnowledgeName = evt.getKnowledgeName();
            LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KnowledgeBasesEntity::getKnowledgeName, evt.getKnowledgeName());
            wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
            List<KnowledgeBasesEntity> knowledgeBasesList = knowledgeBasesDMapper.selectList(wrapper);

            // 如果重名，自动添加时间戳
            if (!knowledgeBasesList.isEmpty()) {
                finalKnowledgeName = reportFormatUtil.generateTimestampedFileName(evt.getKnowledgeName());
                log.info("检测到问答对知识库重名，自动修改知识库名: {} -> {}", evt.getKnowledgeName(), finalKnowledgeName);
                knowledgeBases.setKnowledgeName(finalKnowledgeName);
            }

            // 总是执行保存逻辑
                knowledgeBases.setSearchNumber(0);
                try {
                    // 存储到PG数据库
                    knowledgeBases.setCreatedUserName(PtSecurityUtils.getUsername());
                    knowledgeBasesDMapper.insert(knowledgeBases);

                    // 存储到知识空间与知识关联关系表
                    KgBasesSpaceRelationC kgBasesSpaceRelationC = new KgBasesSpaceRelationC();
                    kgBasesSpaceRelationC.setKgBasesSpaceCId(evt.getKgBasesSpaceCId());
                    kgBasesSpaceRelationC.setKgKnowledgeBasesId(knowledgeBases.getKgKnowledgeBasesId());
                    kgBasesSpaceRelationC.setBaseType(KnowModelType.KNOWLEDGE_BASE.getType());
                    kgBasesSpaceRelationCMapper.insertKgBasesSpaceRelationC(kgBasesSpaceRelationC);

                    // 存储到ES
                    BeanUtil.copyProperties(knowledgeBases, knowledgeBasesIdx);
                    extracted(knowledgeBases, knowledgeBasesIdx);
                    knowledgeBasesIdx.setFullText();
                    elasticsearchRestTemplate.save(knowledgeBasesIdx);
                    
                } catch (Exception e) {
                    if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                        log.error("es新增失败，异常信息：{}", e.getMessage());
                        throw new KnowledgeGraphException("新增失败:{}", e.getMessage());
                    }
                }
                //文件内容存到缓存
                savefileContentRedis(evt, knowledgeBases);
                //异步查重
                if (knowledgeBases.getKgKnowledgeBasesId() != null) {
                    checkFileRepeatAndSaveToRedis(knowledgeBases.getKgKnowledgeBasesId(), false);
                }

                // 如果是问答对Excel，将问答对导入到问答对表中
                Map<String, Integer> qaPairStats = importQaPairsFromExcel(knowledgeBases.getKgKnowledgeBasesId(), evt.getKgBasesSpaceCId(), evt.getKnowledgeFilePath());
                // 合并统计结果
                importStats.put("success", qaPairStats.get("success"));
                importStats.put("fail", qaPairStats.get("fail"));
                
                // 将统计结果保存到知识库实体中，方便前端获取
                knowledgeBases.setImportError(String.format("导入成功: %d条, 导入失败: %d条",
                    importStats.get("success"), importStats.get("fail")));

                return knowledgeBases;
        } else {
            return null;
        }
    }

    @Override
    public HttpServletResponse downLoadCommonFile(HttpServletResponse response, String address, String modelName) {
        // "excel/template/运维手册模板.xlsx"
        org.springframework.core.io.Resource resource = new ClassPathResource(address);
        if (resource.exists()) {
            try {
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/vnd.ms-excel");
                String fileName = resource.getFilename();
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + java.net.URLEncoder.encode(fileName, "UTF-8"));
                response.flushBuffer();
                BufferedInputStream bis = new BufferedInputStream(resource.getInputStream());
                OutputStream os = response.getOutputStream();
                byte[] buffer = new byte[1024];
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
                bis.close();
                os.close(); // 关闭输出流
                return response;
            } catch (Exception e) {
                log.error("导出{}文件异常", modelName, e);
            }
        }
        return null;
    }

    /**
     * 检查行中是否包含指定列名
     */
    private boolean containsColumn(Row row, String columnName) {
        for (Cell cell : row) {
            if (cell.getStringCellValue().trim().equals(columnName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证问答对模板表结构
     * 必需列：应用场景、应用于场景、问题类别、专业领域、子专业
     */
    private boolean validateSceneSheetStructure(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return false;
        }

        String[] requiredColumns = {"有效期", "应用子场景", "流程领域", "适用范围", "子专业"};
        for (String column : requiredColumns) {
            if (!containsColumn(headerRow, column)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证问答对模板表结构
     * 必需列：问题ID、问答轮数、问题、答案
     */
    private boolean validateQASheetStructure(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return false;
        }

        String[] requiredColumns = {"问题ID", "问答轮数", "问题", "答案"};
        for (String column : requiredColumns) {
            if (!containsColumn(headerRow, column)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 从知识库实体中构建需要验证的字段值
     */
    private Map<String, List<String>> buildSelectedValues(KnowledgeBasesEntity knowledge) {
        Map<String, List<String>> selectedValues = new HashMap<>();

        // 应用场景验证
        if (knowledge.getApplicationScene() != null && !knowledge.getApplicationScene().isEmpty()) {
            selectedValues.put("应用场景", Arrays.asList(knowledge.getApplicationScene().split(",")));
        }

        // 专业领域验证
        if (knowledge.getMajor() != null && !knowledge.getMajor().isEmpty()) {
            selectedValues.put("专业领域", Arrays.asList(knowledge.getMajor().split(",")));
        }

        // 问题分类验证
        if (knowledge.getQuestionClassify() != null && !knowledge.getQuestionClassify().isEmpty()) {
            selectedValues.put("问题类别", Arrays.asList(knowledge.getQuestionClassify()));
        }

        return selectedValues;
    }

    /**
     * 解析问答对数据并填充到KnowledgeBasesEntity对象
     */
    private void parseQAPairs(Sheet sheet, KnowledgeBasesEntity knowledge) {
        // 获取表头索引映射
        Row headerRow = sheet.getRow(0);
        Map<String, Integer> headerMap = new HashMap<>();
        for (Cell cell : headerRow) {
            headerMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }

        // 检查必需列是否存在
        if (!headerMap.containsKey("问题") || !headerMap.containsKey("答案")) {
            return;
        }

        // 遍历数据行，只取第一组问答对（可根据需求修改为处理多组问答对）
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            // 获取问题和答案
            String question = getCellStringValue(row, headerMap.get("问题"));
            String answer = getCellStringValue(row, headerMap.get("答案"));

            // 忽略空问题或空答案的行
            if (question.isEmpty() || answer.isEmpty()) {
                continue;
            }

            // 设置问题和答案
            knowledge.setQuestion(question);
            knowledge.setAnswer(answer);

            // 只处理第一组有效的问答对
            break;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Row row, int columnIndex) {
        if (columnIndex < 0 || row == null) {
            return "";
        }
        Cell cell = row.getCell(columnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((int) cell.getNumericCellValue());
            default:
                return "";
        }
    }

    /**
     * 根据文档ID从服务器下载文件并解析内容，返回文件中的文本内容
     *
     * @param evt DownLoadDocEvt，包含kgDocId和docUrl
     * @return 文件内容字符串
     */
    @Override
    @Transactional(rollbackOn = {KnowledgeGraphException.class})
    public String downloadAndAnalyzeFileContent(DownLoadDocEvt evt) {
/*        if (evt == null || evt.getKgDocId() == null) {
            throw new KnowledgeGraphException("参数不能为空");
        }*/
 /*       DocumentEntity documentEntity = documentMapper.selectById(evt.getKgDocId());
        if (documentEntity == null) {
            throw new KnowledgeGraphException("未找到对应的文档信息");
        }*/
        String filePath = evt.getDocUrl();
        if (filePath == null) {
            filePath = evt.getDocUrl();
        }
        String fileName = "123.html";
        try (InputStream inputStream = ctdfsService.downloadStream(filePath)) {
            if (inputStream == null) {
                throw new KnowledgeGraphException("文件下载失败，未获取到文件流");
            }
            byte[] fileBytes = IOUtils.toByteArray(inputStream);
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    MediaType.APPLICATION_OCTET_STREAM_VALUE,
                    fileBytes
            );
            // 复用应急预案的文件内容解析逻辑
            return kgContiDService.analyzeFile(multipartFile);
        } catch (Exception e) {
            log.error("下载并解析文件内容失败", e);
            throw new KnowledgeGraphException("下载并解析文件内容失败: " + e.getMessage());
        }
    }


    /**
     * Excel 文件导入和验证的核心方法
     *
     * @param knowledge 知识库实体对象
     * @param file      上传的Excel文件
     * @return 是否导入成功
     */
    private boolean importExcelWithStrictValidation(KnowledgeBasesEntity knowledge, MultipartFile file) {
        if (file == null || knowledge == null) {
            log.error(" 导入失败：文件或知识库对象为空");
            return false;
        }

        try (InputStream inputStream = file.getInputStream()) {
            log.info(" 开始处理Excel文件: {}", file.getOriginalFilename());

            // 1. 读取Excel工作簿
            Workbook workbook = new XSSFWorkbook(inputStream);
            log.debug(" 成功读取Excel工作簿");

            // 2. 识别问答对模板Sheet
            Sheet sceneSheet = findSceneSheet(workbook);
            if (sceneSheet == null) {
                log.error(" 未找到有效的问答对模板表（需包含'应用场景'列）");
                throw new RuntimeException("未找到有效的问答对模板表（需包含'应用场景'列）");
            }
            log.info(" 找到问答对模板表: {}", sceneSheet.getSheetName());

            // 3. 验证问答对模板表结构
            if (!validateSceneSheetStructure(sceneSheet)) {
                log.error(" 问答对模板表结构不符合要求");
                throw new RuntimeException("问答对模板表结构不符合要求");
            }
            log.debug(" 问答对模板表结构验证通过");

            // 4. 严格验证用户选择的值与Excel内容的匹配性
            validateUserSelectionsWithExcel(sceneSheet, knowledge);

            // 5. 处理问答对Sheet(如果存在)
            Sheet qaSheet = findQASheet(workbook);
            if (qaSheet != null) {
                log.info(" 找到问答对表: {}", qaSheet.getSheetName());

                if (!validateQASheetStructure(qaSheet)) {
                    log.error(" 问答对模板结构不符合要求");
                    throw new RuntimeException("问答对模板结构不符合要求");
                }
                log.debug(" 问答对表结构验证通过");

                // 解析问答对数据
                parseQAPairs(qaSheet, knowledge);

                // 检查问答对数据有效性
                if (knowledge.getQuestion() == null || knowledge.getQuestion().isEmpty() ||
                        knowledge.getAnswer() == null || knowledge.getAnswer().isEmpty()) {
                    log.error(" 问答对模板中没有有效数据");
                    throw new RuntimeException("问答对模板中没有有效数据");
                }
                log.info(" 成功解析问答对数据");
            }

            // 6. 设置知识库时间属性
            knowledge.setCreatedTime(new Date());
            knowledge.setUpdatedTime(new Date());
            knowledge.setIsDeleted("0");  // 默认为未删除
            log.info(" 知识库实体属性设置完成");

            log.info("Excel 文件导入验证成功");
            return true;
        } catch (IOException e) {
            log.error(" 处理Excel文件失败：", e);
            throw new RuntimeException("处理Excel文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 查找问答对模板Sheet
     */
    private Sheet findSceneSheet(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet currentSheet = workbook.getSheetAt(i);
            Row firstRow = currentSheet.getRow(0);  // 表头行

            if (firstRow != null && containsColumn(firstRow, "应用场景") &&
                    !currentSheet.getSheetName().contains(" 问答对")) {
                return currentSheet;
            }
        }
        return null;
    }

    /**
     * 查找问答对Sheet
     */
    private Sheet findQASheet(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet currentSheet = workbook.getSheetAt(i);
            if (currentSheet.getSheetName().contains(" 问答对")) {
                return currentSheet;
            }
        }
        return null;
    }

    /**
     * 严格验证用户选择的值与Excel内容的匹配性
     */
    private void validateUserSelectionsWithExcel(Sheet sceneSheet, KnowledgeBasesEntity knowledge) {
        log.info(" 开始验证用户选择值与Excel内容的匹配性");

        // 1. 获取表头索引映射
        Row headerRow = sceneSheet.getRow(0);
        Map<String, Integer> headerMap = new HashMap<>();
        for (Cell cell : headerRow) {
            headerMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }
        log.debug(" 表头映射: {}", headerMap);

        // 2. 验证每个字段
        validateField(sceneSheet, headerMap, "应用子场景", knowledge.getApplicationScene());
        validateField(sceneSheet, headerMap, "子专业", knowledge.getMajor());
        // 设置流程领域的数据
        validateFlowSceneField(sceneSheet, headerMap, "流程领域", knowledge);
        validateField(sceneSheet, headerMap, "适用范围", knowledge.getPublicity());
        // 对有效期字段进行特殊处理
        validatePeriodValidityField(sceneSheet, headerMap, "有效期", knowledge);

        log.info(" 所有字段验证通过");
    }

    /**
     * 验证流程领域字段的匹配性，特殊处理
     * 从字典表中查询code_type为'C3'的记录，验证Excel中的值是否存在于字典中
     * 如果Excel中有多条数据且流程领域不同，则将多个值用逗号连接起来保存
     */
    private void validateFlowSceneField(Sheet sheet, Map<String, Integer> headerMap, String fieldName, KnowledgeBasesEntity knowledge) {
        if (!headerMap.containsKey(fieldName)) {
            log.error("Excel 中缺少必要的列: {}", fieldName);
            throw new RuntimeException("Excel中缺少必要的列: " + fieldName);
        }

        int columnIndex = headerMap.get(fieldName);
        log.info(" 开始验证流程领域字段");

        // 从字典表中查询code_type为'C3'的记录
        List<DictionaryEntity> dictList = dictionaryMapper.selectList(new LambdaQueryWrapper<DictionaryEntity>()
                .eq(DictionaryEntity::getCodeType, "C3"));
        
        // 收集Excel中的所有流程领域值
        Set<String> flowSceneValues = new HashSet<>();
        Set<String> matchedFlowSceneIds = new HashSet<>();
        
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            Cell cell = row.getCell(columnIndex);
            if (cell == null) continue;

            String cellValue = getCellStringValue(row, columnIndex);
            if (StringUtils.isNotBlank(cellValue)) {
                flowSceneValues.add(cellValue);
            }
        }
        
        // 如果Excel中没有流程领域值，则直接返回
        if (flowSceneValues.isEmpty()) {
            log.warn(" Excel中未找到流程领域值");
            return;
        }
        
        log.debug(" Excel中的流程领域值: {}", flowSceneValues);
        
        // 验证Excel中的值是否存在于字典中，并收集对应的ID
        for (String flowSceneValue : flowSceneValues) {
            boolean found = false;
            for (DictionaryEntity dict : dictList) {
                if (dict.getCodeName().equals(flowSceneValue)) {
                    matchedFlowSceneIds.add(dict.getBdpDirId().toString());
                    found = true;
                    log.info(" 流程领域匹配成功: {} -> {}", flowSceneValue, dict.getBdpDirId());
                    break;
                }
            }
            
            if (!found) {
                log.warn(" Excel中的流程领域值[{}]在字典中未找到对应项", flowSceneValue);
            }
        }
        
        // 将匹配到的ID用逗号连接起来保存到knowledge对象中
        if (!matchedFlowSceneIds.isEmpty()) {
            String flowSceneIds = String.join(",", matchedFlowSceneIds);
            knowledge.setFlowScene(flowSceneIds);
            log.info(" 设置流程领域IDs: {}", flowSceneIds);
        } else {
            log.warn(" 未找到匹配的流程领域ID");
        }
    }

    /**
     * 验证有效期字段的匹配性，特殊处理
     * 如果Excel中有填写有效期，则验证并设置为对应的字典ID；如果没填，则保留前端传入的默认值
     */
    private void validatePeriodValidityField(Sheet sheet, Map<String, Integer> headerMap, String fieldName, KnowledgeBasesEntity knowledge) {
        if (!headerMap.containsKey(fieldName)) {
            log.error("Excel 中缺少必要的列: {}", fieldName);
            throw new RuntimeException("Excel中缺少必要的列: " + fieldName);
        }

        int columnIndex = headerMap.get(fieldName);
        String defaultPeriodValidity = knowledge.getPeriodValidity(); // 保存前端传入的默认值
        log.debug(" 有效期默认值: {}", defaultPeriodValidity);

        // 检查Excel中是否有填写有效期
        boolean foundMatch = false;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            Cell cell = row.getCell(columnIndex);
            if (cell == null) continue;

            String cellValue = getCellStringValue(row, columnIndex);
            if (StringUtils.isNotBlank(cellValue)) {
                // 查找字典表中对应的ID
                List<DictionaryEntity> dictList = dictionaryMapper.selectList(new LambdaQueryWrapper<DictionaryEntity>().eq(DictionaryEntity::getCodeType, "periodValidity"));
                for (DictionaryEntity dict : dictList) {
                    if (dict.getCodeName().equals(cellValue)) {
                        knowledge.setPeriodValidity(dict.getBdpDirId().toString());
                        foundMatch = true;
                        log.info(" 有效期匹配成功: {} -> {}", cellValue, dict.getBdpDirId());
                        break;
                    }
                }
                
                if (!foundMatch) {
                    log.warn(" Excel中的有效期值[{}]在字典中未找到对应项，将使用默认值", cellValue);
                }
                break; // 只检查第一个非空值
            }
        }

        // 如果Excel中没有填写有效期或未找到匹配项，则保留默认值
        if (!foundMatch) {
            log.info(" 使用默认有效期值: {}", defaultPeriodValidity);
            knowledge.setPeriodValidity(defaultPeriodValidity);
        }
    }

    /**
     * 验证单个字段的匹配性
     */
    private void validateField(Sheet sheet, Map<String, Integer> headerMap, String fieldName, String selectedIdsStr) {
        if (!headerMap.containsKey(fieldName)) {
            log.error("Excel 中缺少必要的列: {}", fieldName);
            throw new RuntimeException("Excel中缺少必要的列: " + fieldName);
        }

        if (StringUtils.isBlank(selectedIdsStr)) {
            log.warn(" 用户未选择{}，跳过验证", fieldName);
            return;
        }

        log.info(" 开始验证字段: {}", fieldName);
        log.debug(" 用户选择值: {}", selectedIdsStr);

        // 1. 获取用户选择的所有字典项code_name
        List<String> userSelectedNames = new ArrayList<>();
        for (Long id : parseIds(selectedIdsStr)) {
            DictionaryEntity dict = dictionaryMapper.selectById(id);
            if (dict == null) {
                log.error(" 无效的字典ID: {} 在字段 {}", id, fieldName);
                throw new RuntimeException("无效的ID: " + id + " 在字段 " + fieldName);
            }
            userSelectedNames.add(dict.getCodeName());
            log.debug(" 字典ID {} 对应名称: {}", id, dict.getCodeName());
        }

        // 2. 获取Excel中该列的所有唯一值
        int colIndex = headerMap.get(fieldName);
        Set<String> excelValues = new HashSet<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                String value = getCellStringValue(row, colIndex).trim();
                if (!value.isEmpty()) {
                    excelValues.add(value);
                }
            }
        }
        log.debug("Excel 中{}列的有效值: {}", fieldName, excelValues);

        // 3. 验证每个用户选择的值是否存在于Excel中
        for (String selectedName : userSelectedNames) {
            if (!excelValues.contains(selectedName)) {
                log.error(" 选择的值 '{}' 在Excel的 {} 列中不存在", selectedName, fieldName);
                throw new RuntimeException("选择的值 '" + selectedName +
                        "' 在Excel的 " + fieldName + " 列中不存在");
            }
            log.debug(" 验证通过: {} 存在于Excel的{}列中", selectedName, fieldName);
        }
    }

    /**
     * 将逗号分隔的ID字符串转换为Long列表
     */
    private List<Long> parseIds(String idsStr) {
        if (StringUtils.isBlank(idsStr)) {
            return Collections.emptyList();
        }
        return Arrays.stream(idsStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 判断文档是否已经上报到集团
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 是否已上报
     */
    public boolean isDocumentReported(Long knowledgeBaseId) {
        try {
            KnowledgeBasesEntity knowledgeBase = knowledgeBasesDMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null) {
                return false;
            }
            
            // 检查上报状态，判断是否已上报集团
            String reportStatus = knowledgeBase.getReportStatus();
            return ReportStatusConstant.isReported(reportStatus);
            
        } catch (Exception e) {
            log.error("检查文档上报状态失败，知识库ID: {}", knowledgeBaseId, e);
            return false;
        }
    }

    /**
     * 自动上报知识库到集团
     * 
     * @param knowledgeBaseIds 知识库ID列表
     */
    private void autoReportToGroup(List<Long> knowledgeBaseIds) {
        try {
            ServiceResp<Map<String, Object>> response = knowledgeReportClient.batchReportKnowledgeBases(knowledgeBaseIds);

            // 非保存的状态由 上报服务统一修改状态
            if (response.isSuccess()) {
                log.info("自动上报集团成功，知识库ID列表: {}", knowledgeBaseIds);
                // 更新知识库的上报状态
                //updateReportStatus(knowledgeBaseIds, ReportStatusConstant.REPORTED, "自动上报成功");
            } else {
                log.error("自动上报集团失败，响应信息: {}", response.getHead().getRespMsg());
                //updateReportStatus(knowledgeBaseIds, ReportStatusConstant.REPORT_FAILED, "自动上报失败: " + response.getHead().getRespMsg());
            }
        } catch (Exception e) {
            log.error("自动上报集团异常，知识库ID列表: {}", knowledgeBaseIds, e);
            updateReportStatus(knowledgeBaseIds, ReportStatusConstant.REPORT_FAILED, "自动上报异常: " + e.getMessage());
        }
    }

    /**
     * 更新知识库的上报状态
     * 
     * @param knowledgeBaseIds 知识库ID列表
     * @param reportStatus 上报状态
     * @param reportDescription 上报描述
     */
    private void updateReportStatus(List<Long> knowledgeBaseIds, String reportStatus, String reportDescription) {
        try {
            Date reportTime = new Date();
            for (Long id : knowledgeBaseIds) {
                KnowledgeBasesEntity updateEntity = new KnowledgeBasesEntity();
                updateEntity.setKgKnowledgeBasesId(id);
                updateEntity.setReportStatus(reportStatus);
                updateEntity.setReportDescription(reportDescription);
                updateEntity.setReportTime(reportTime);
                updateEntity.setUpdatedTime(reportTime);
                updateEntity.setUpdatedUserName(PtSecurityUtils.getUsername());
                
                knowledgeBasesDMapper.updateById(updateEntity);
            }
            log.info("更新知识库上报状态成功，ID列表: {}, 状态: {}", knowledgeBaseIds, reportStatus);
        } catch (Exception e) {
            log.error("更新知识库上报状态失败，ID列表: {}", knowledgeBaseIds, e);
        }
    }

    /**
     * 检查并提示上报集团
     * 
     * @param knowledgeBaseId 知识库ID
     * @param documentFormat 文档格式
     * @return 提示信息
     */
    public String checkAndPromptReport(Long knowledgeBaseId, String documentFormat) {
        try {
            // 检查文档格式是否符合上报要求
            if (!reportFormatUtil.isFormatEligibleForReport(documentFormat)) {
                return reportFormatUtil.getFormatIneligibleMessage();
            }
            
            // 检查是否已经上报
            if (isDocumentReported(knowledgeBaseId)) {
                return "该文档已经上报集团";
            }
            
            return "该文档符合集团上报格式且未上报，是否需要上报集团？";
        } catch (Exception e) {
            log.error("检查上报状态失败，知识库ID: {}", knowledgeBaseId, e);
            return "检查上报状态失败";
        }
    }

    /**
     * 更新知识库的上报描述信息
     * 
     * @param knowledgeBaseId 知识库ID
     * @param reportDescription 上报描述
     */
    private void updateReportDescription(Long knowledgeBaseId, String reportDescription) {
        try {
            KnowledgeBasesEntity updateEntity = new KnowledgeBasesEntity();
            updateEntity.setKgKnowledgeBasesId(knowledgeBaseId);
            updateEntity.setReportDescription(reportDescription);
            updateEntity.setUpdatedTime(new Date());
            updateEntity.setUpdatedUserName(PtSecurityUtils.getUsername());
            
            int updateCount = knowledgeBasesDMapper.updateById(updateEntity);
            if (updateCount > 0) {
                log.info("更新知识库上报描述成功 - 知识库ID: {}, 描述: {}", knowledgeBaseId, reportDescription);
            } else {
                log.warn("更新知识库上报描述失败，影响行数为0 - 知识库ID: {}", knowledgeBaseId);
            }
        } catch (Exception e) {
            log.error("更新知识库上报描述异常 - 知识库ID: {}, 描述: {}", knowledgeBaseId, reportDescription, e);
        }
    }

    /**
     * 审核通过后检查并自动上报
     * 
     * @param knowledgeBaseIds 知识库ID列表
     */
    private void checkAndAutoReportAfterReviewApproval(List<Long> knowledgeBaseIds) {
        try {
            log.info("开始检查审核通过的知识库是否需要自动上报，数量: {}, ID列表: {}", knowledgeBaseIds.size(), knowledgeBaseIds);
            
            // 批量查询知识库信息
            List<KnowledgeBasesEntity> knowledgeBases = knowledgeBasesDMapper.selectBatchIds(knowledgeBaseIds);
            if (knowledgeBases == null || knowledgeBases.isEmpty()) {
                log.warn("未找到有效的知识库数据，跳过自动上报检查");
                return;
            }
            
            // 检查每个知识库的上报条件
            List<Long> readyToReportIds = new ArrayList<>();
            List<String> notReadyReasons = new ArrayList<>();
            
            for (KnowledgeBasesEntity entity : knowledgeBases) {
                Long knowledgeBaseId = entity.getKgKnowledgeBasesId();
                try {
                    // 1. 检查文档格式是否符合要求
                    String documentFormat = entity.getDocumentFormat();
                    if (!reportFormatUtil.isFormatEligibleForReport(documentFormat)) {
                        String reason = "知识库ID " + knowledgeBaseId + ": 文档格式不符合上报要求，当前格式: " + documentFormat;
                        notReadyReasons.add(reason);
                        updateReportDescription(knowledgeBaseId, reason);
                        log.info("知识库文档格式不符合上报要求 - 知识库ID: {}, 格式: {}", knowledgeBaseId, documentFormat);
                        continue;
                    }
                    
                    // 2. 检查查重率是否合格
                    java.math.BigDecimal checkRepeatResult = entity.getCheckRepeatResult();
                    if (checkRepeatResult == null) {
                        String reason = "知识库ID " + knowledgeBaseId + ": 查重率为空，请等待查重完成";
                        notReadyReasons.add(reason);
                        updateReportDescription(knowledgeBaseId, reason);
                        log.info("知识库查重率为空，等待查重完成 - 知识库ID: {}", knowledgeBaseId);
                        continue;
                    }
                    
                    // 查重率为-1表示正在查重中
                    if (checkRepeatResult.compareTo(java.math.BigDecimal.valueOf(-1)) == 0) {
                        String reason = "知识库ID " + knowledgeBaseId + ": 正在查重中，请等待查重完成";
                        notReadyReasons.add(reason);
                        updateReportDescription(knowledgeBaseId, reason);
                        log.info("知识库正在查重中，等待查重完成 - 知识库ID: {}", knowledgeBaseId);
                        continue;
                    }
                    
                    // 查重率不能超过0.9（90%）
                    if (checkRepeatResult.compareTo(java.math.BigDecimal.valueOf(0.9)) > 0) {
                        String reason = "知识库ID " + knowledgeBaseId + ": 查重率超过限制，当前查重率: " + checkRepeatResult + "，限制: 0.9";
                        notReadyReasons.add(reason);
                        updateReportDescription(knowledgeBaseId, reason);
                        log.info("知识库查重率超标 - 知识库ID: {}, 查重率: {}", knowledgeBaseId, checkRepeatResult);
                        continue;
                    }
                    
                    // 3. 所有条件都满足，可以上报
                    readyToReportIds.add(knowledgeBaseId);
                    log.info("知识库满足上报条件 - 知识库ID: {}, 文档格式: {}, 查重率: {}", 
                            knowledgeBaseId, documentFormat, checkRepeatResult);
                    
                } catch (Exception e) {
                    String reason = "知识库ID " + knowledgeBaseId + ": 检查上报条件异常: " + e.getMessage();
                    notReadyReasons.add(reason);
                    updateReportDescription(knowledgeBaseId, reason);
                    log.error("检查知识库上报条件异常 - 知识库ID: {}", knowledgeBaseId, e);
                }
            }
            
            // 记录检查结果
            log.info("审核通过后上报条件检查完成 - 总数: {}, 满足条件: {}, 不满足条件: {}", 
                    knowledgeBaseIds.size(), readyToReportIds.size(), notReadyReasons.size());
            
            if (!notReadyReasons.isEmpty()) {
                log.info("不满足上报条件的原因: {}", String.join("; ", notReadyReasons));
            }
            
            // 对满足条件的知识库进行批量自动上报
            if (!readyToReportIds.isEmpty()) {
                log.info("开始批量自动上报满足条件的知识库，数量: {}, ID列表: {}", readyToReportIds.size(), readyToReportIds);
                autoReportToGroup(readyToReportIds);
            } else {
                log.info("没有满足上报条件的知识库，跳过自动上报");
            }
            
        } catch (Exception e) {
            log.error("审核通过后检查上报条件失败", e);
        }
    }

    @Override
    @Transactional
    public ServiceResp updateKnowledgeBaseName(UpdateKnowledgeBaseNameEvt evt) {
        log.info("修改知识库名称，参数：{}", evt);

        try {
            // 参数校验
            if (evt.getKgKnowledgeBasesId() == null) {
                return ServiceResp.fail("知识库ID不能为空");
            }

            if (StringUtils.isBlank(evt.getSummary())) {
                return ServiceResp.fail("知识库名称不能为空");
            }

            // 查询知识库是否存在
            LambdaQueryWrapper<KnowledgeBasesEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeBasesEntity::getKgKnowledgeBasesId, evt.getKgKnowledgeBasesId())
                       .eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO)
                       .select(KnowledgeBasesEntity::getKgKnowledgeBasesId,
                              KnowledgeBasesEntity::getReportStatus,
                              KnowledgeBasesEntity::getSummary);

            KnowledgeBasesEntity existingEntity = knowledgeBasesDMapper.selectOne(queryWrapper);
            if (existingEntity == null) {
                return ServiceResp.fail("知识库不存在或已被删除");
            }

            // 检查上报状态是否为上报失败
            if (!ReportStatusConstant.REPORT_FAILED.equals(existingEntity.getReportStatus())) {
                return ServiceResp.fail("只有上报失败状态的知识库才能修改名称");
            }

            // 检查名称是否有变化
            if (evt.getSummary().equals(existingEntity.getSummary())) {
                return ServiceResp.success("知识库名称未发生变化");
            }

            // 更新知识库名称
            LambdaUpdateWrapper<KnowledgeBasesEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(KnowledgeBasesEntity::getKgKnowledgeBasesId, evt.getKgKnowledgeBasesId())
                        .eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO)
                        .set(KnowledgeBasesEntity::getSummary, evt.getSummary())
                        .set(KnowledgeBasesEntity::getUpdatedUserName, PtSecurityUtils.getUsername())
                        .set(KnowledgeBasesEntity::getUpdatedTime, new Date());

            int updateCount = knowledgeBasesDMapper.update(null, updateWrapper);

            if (updateCount > 0) {
                log.info("知识库名称修改成功，知识库ID：{}，新名称：{}", evt.getKgKnowledgeBasesId(), evt.getSummary());
                return ServiceResp.success("知识库名称修改成功");
            } else {
                return ServiceResp.fail("知识库名称修改失败");
            }

        } catch (Exception e) {
            log.error("修改知识库名称失败，知识库ID：{}", evt.getKgKnowledgeBasesId(), e);
            return ServiceResp.fail("修改知识库名称失败：" + e.getMessage());
        }
    }

}
