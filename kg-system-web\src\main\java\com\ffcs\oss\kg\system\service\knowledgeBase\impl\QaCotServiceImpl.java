package com.ffcs.oss.kg.system.service.knowledgeBase.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.FormatUtil;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.kg.common.cache.CtgRedisService;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.ExceptionDefinition;
import com.ffcs.oss.kg.common.core.exception.KnowledgeBasesException;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.common.core.util.ExcelUtil;
import com.ffcs.oss.kg.common.core.util.FileUtil;
import com.ffcs.oss.kg.data.converter.graph.DtPermissionConverter;
import com.ffcs.oss.kg.data.enums.*;
import com.ffcs.oss.kg.data.es.KnowledgeBasesIdx;
import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.data.model.evt.cot.FailedRecord;
import com.ffcs.oss.kg.data.model.evt.cot.SpaceRelation;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.BatchUpdateReportStatusEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.GetBasesEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.KgBasesAuditPersonDEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.BatchUpdateCotReportStatusEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.ExportQaCotsEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.GetQaCotsEvt;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.KgQaCotAuditDimensionD;
import com.ffcs.oss.kg.data.model.vm.bases.KgBasesAuditPersonDVm;
import com.ffcs.oss.kg.data.model.vm.know.MajorStatisticsVm;
import com.ffcs.oss.kg.data.model.vm.region.QueryRegionListVm;
import com.ffcs.oss.kg.data.model.vo.DashboardStatisticsVO;
import com.ffcs.oss.kg.data.rd.entity.DictionaryEntity;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesSpaceRelationC;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgKnowledgeFile;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.data.rd.mapper.DictionaryMapper;
import com.ffcs.oss.kg.data.rd.mapper.RegionMapper;
import com.ffcs.oss.kg.data.rd.mapper.cases.AuditorAllocationRuleConfigMapper;
import com.ffcs.oss.kg.data.rd.mapper.cot.KgQaCotAuditDimensionDMapper;
import com.ffcs.oss.kg.data.rd.mapper.cot.KgQaCotDMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.BasesAuditPersonMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.KgBasesSpaceRelationCMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.ModelKnowledgeBasesDMapper;
import com.ffcs.oss.kg.dfs.service.CtdfsService;
import com.ffcs.oss.kg.system.client.FileSimilarityClient;
import com.ffcs.oss.kg.system.config.CommonConfig;
import com.ffcs.oss.kg.system.config.KnowledgeBaseConfig;
import com.ffcs.oss.kg.system.config.KnowledgeConstant;
import com.ffcs.oss.kg.system.config.RegionConfig;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.events.cot.QaCotIdsEvt;
import com.ffcs.oss.kg.system.events.cot.QaCotInfoEvt;
import com.ffcs.oss.kg.system.events.qa.BatchUpdateQaStateEvt;
import com.ffcs.oss.kg.system.evt.knowledgeBases.InsertKnowledgeBasesEvt;
import com.ffcs.oss.kg.system.service.knowledgeBase.QaCotService;
import com.ffcs.oss.kg.system.utils.ReportFormatUtil;
import com.ffcs.oss.kg.system.vm.cot.QaCotInfoVm;
import com.ffcs.oss.kg.system.vm.cot.QaCotVm;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexedObjectInformation;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问答思维链服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
public class QaCotServiceImpl implements QaCotService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${cndids.tenant-code}")
    private String tenantCode;

    @Resource
    private KgQaCotDMapper kgQaCotDMapper;

    @Resource
    private RegionMapper regionMapper;

    @Resource
    private RegionConfig regionConfig;

    @Resource
    private KnowledgeBaseConfig knowledgeBaseConfig;
    
    // 添加ES相关字段
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private AuditorAllocationRuleConfigMapper auditorAllocationRuleConfigMapper;

    @Resource
    private BasesAuditPersonMapper basesAuditPersonMapper;

    @Resource
    private EsKnowSearchServiceImpl esKnowSearchService;
    
    @Resource
    private DictionaryMapper dictionaryMapper;
    
    @Resource
    private KgBasesSpaceRelationCMapper kgBasesSpaceRelationCMapper;
    
    @Resource
    private KgQaCotAuditDimensionDMapper kgQaCotAuditDimensionDMapper;

    @Resource
    private ModelKnowledgeBasesDMapper knowledgeBasesDMapper;

    @Resource
    private CtdfsService ctdfsService;

    @Resource
    private CtgRedisService ctgRedisService;

    @Resource
    private DtPermissionConverter dtPermissionConverter;

    @Resource
    private CommonConfig commonConfig;

    @Resource
    private FileSimilarityClient fileSimilarityClient;

    @Resource
    private ReportFormatUtil reportFormatUtil;

    @Resource
    private KgBasesSpaceRelationCMapper spaceRelationMapper;


    /**
     * 构建公共查询条件（包含权限处理逻辑）
     */
    private void buildCommonQueryConditions(GetQaCotsEvt evt) {
        // 设置用户信息
        if (StrUtil.isEmpty(evt.getBeforeOneUser())) {
            evt.setBeforeOneUser(PtSecurityUtils.getUsername()); // 临时设置，需要替换为实际获取用户的方法
        }

        // 处理问题关键词
        if (StrUtil.isNotEmpty(evt.getQuestionKeyword())) {
            evt.setQuestionKeyword(evt.getQuestionKeyword().trim());
        }

        // 处理区域信息
        if (CollUtil.isNotEmpty(evt.getCurrentUserList()) && CollUtil.isEmpty(evt.getRegionList())) {
            evt.setRegionList(evt.getCurrentUserList().stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList()));
        }

        // 处理审核规则
        String username = PtSecurityUtils.getUsername();

        //todo 暂时
        // username = "tianqq";
        handleAuditRules(evt, username);
    }

    /**
     * 使用与getQaCots方法相同的查询逻辑查询问答思维链数据
     * @param evt 查询条件
     * @param onlySelectId 是否只查询ID字段（用于提高性能）
     * @return 问答思维链实体列表
     */
    private List<KgQaCotD> queryQaCotsWithConditions(GetQaCotsEvt evt, boolean onlySelectId) {
        // 处理权限和公共查询条件
        evt.setCountTotal(true);
        buildCommonQueryConditions(evt);

        // 根据知识空间ID获取关联的问答思维链ID列表
        List<Long> kgQaCotIds = new ArrayList<>();
        if (evt.getKgBasesSpaceCId() != null) {
            kgQaCotIds = getQaCotIdsBySpaceId(evt.getKgBasesSpaceCId());
        }

        // 如果没有找到问答思维链，直接返回空列表
        if (kgQaCotIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 设置问答思维链ID列表
        evt.setKgQaCotIds(kgQaCotIds);

        if (StrUtil.isBlank(evt.getFullText())) {
            // 普通数据库查询
            return queryQaCotsFromDatabase(evt, onlySelectId);
        } else {
            // ES全文检索查询
            return queryQaCotsFromElasticsearch(evt, onlySelectId);
        }
    }

    /**
     * 从数据库查询问答思维链数据
     */
    private List<KgQaCotD> queryQaCotsFromDatabase(GetQaCotsEvt evt, boolean onlySelectId) {
        List<KgQaCotD> qaCotList = kgQaCotDMapper.selectQaCotListByHighLevel(evt);
        
        if (onlySelectId && CollUtil.isNotEmpty(qaCotList)) {
            // 如果只需要ID，则只保留ID字段的简化对象
            return qaCotList.stream().map(qa -> {
                KgQaCotD simpleQa = new KgQaCotD();
                simpleQa.setKgQaCotId(qa.getKgQaCotId());
                return simpleQa;
            }).collect(Collectors.toList());
        }
        
        return qaCotList;
    }

    /**
     * 从Elasticsearch查询问答思维链数据
     */
    private List<KgQaCotD> queryQaCotsFromElasticsearch(GetQaCotsEvt evt, boolean onlySelectId) {
        try {
            GetBasesEvt basesEvt = new GetBasesEvt();
            BeanUtil.copyProperties(evt, basesEvt);
            esKnowSearchService.getCasesOnFullTextWithoutPage(basesEvt);
            
            // 获取ES查询结果但不分页
            PageInfo<KnowledgeBasesEntity> casesOnFullTextWithPage = esKnowSearchService.getCasesOnFullTextWithPage(basesEvt);
            
            List<KgQaCotD> resultList = casesOnFullTextWithPage.getList().stream()
                    .map(this::convertToList)
                    .collect(Collectors.toList());
            
            if (onlySelectId && CollUtil.isNotEmpty(resultList)) {
                // 如果只需要ID，则只保留ID字段的简化对象
                return resultList.stream().map(qa -> {
                    KgQaCotD simpleQa = new KgQaCotD();
                    simpleQa.setKgQaCotId(qa.getKgQaCotId());
                    return simpleQa;
                }).collect(Collectors.toList());
            }
            
            return resultList;
        } catch (Exception e) {
            log.error("ES查询问答思维链失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public QaCotVm getQaCots(GetQaCotsEvt evt) {
        if (evt.getKgBasesSpaceCId() == null) {
            return null;
        }

        // 使用公共权限处理逻辑
        buildCommonQueryConditions(evt);

        // 根据知识空间ID获取关联的问答思维链ID列表
        List<Long> kgQaCotIds = new ArrayList<>();
        if (evt.getKgBasesSpaceCId() != null) {
            kgQaCotIds = getQaCotIdsBySpaceId(evt.getKgBasesSpaceCId());
        }

        // 如果没有找到问答思维链，直接返回空列表
        if (kgQaCotIds.isEmpty()) {
            QaCotVm qaCotVm = new QaCotVm();
            qaCotVm.setQaCotPageInfo(new PageInfo<>(new ArrayList<>()));
            qaCotVm.setAllMajorStatisticsList(new ArrayList<>());
            qaCotVm.setMajorTotal(0);
            return qaCotVm;
        }

        // 设置问答思维链ID列表
        evt.setKgQaCotIds(kgQaCotIds);

        // 查询问答思维链列表 - 使用默认分页参数
        int pageNo = evt.getPageNo(); // 默认第一页
        int pageSize = evt.getPageSize(); // 默认分页大小
        
        Page<Object> page = PageHelper.startPage(pageNo, pageSize);
        PageInfo<KgQaCotD> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNo);
        
        if (StrUtil.isBlank(evt.getFullText())) {
            pageInfo.setList(kgQaCotDMapper.selectQaCotListByHighLevel(evt));
            pageInfo.setTotal(page.getTotal());
        } else {
            GetBasesEvt basesEvt = new GetBasesEvt();
            BeanUtil.copyProperties(evt, basesEvt);
            esKnowSearchService.getCasesOnFullTextWithoutPage(basesEvt);
            PageInfo<KnowledgeBasesEntity> casesOnFullTextWithPage = esKnowSearchService.getCasesOnFullTextWithPage(basesEvt);
            pageInfo.setTotal(casesOnFullTextWithPage.getTotal());
            pageInfo.setList(casesOnFullTextWithPage.getList().stream().map(this::convertToList).collect(Collectors.toList()));
        }

        // 丰富区域和分类信息
        enrichRegionInfo(pageInfo.getList());

        // 构建返回结果
        QaCotVm qaCotVm = new QaCotVm();
        qaCotVm.setQaCotPageInfo(pageInfo);

        // 查询专业统计信息
        Integer majorTotal = kgQaCotDMapper.selectMajorStatistics(kgQaCotIds);
        List<MajorStatisticsVm> majorStatisticsList = kgQaCotDMapper.selectAllMajorStatistics(kgQaCotIds);

        qaCotVm.setAllMajorStatisticsList(majorStatisticsList);
        qaCotVm.setMajorTotal(majorTotal);

        return qaCotVm;
    }

    public KgQaCotD convertToList(KnowledgeBasesEntity knowledgeBasesEntity) {
        KgQaCotD kgQaCotD = new KgQaCotD();
        BeanUtil.copyProperties(knowledgeBasesEntity, kgQaCotD);
        kgQaCotD.setKgQaCotId(knowledgeBasesEntity.getKgKnowledgeBasesId());
        return kgQaCotD;
    }

    /**
     * 根据知识空间ID获取问答思维链ID列表
     *
     * @param kgBasesSpaceCId 知识空间ID
     * @return 问答思维链ID列表
     */
    private List<Long> getQaCotIdsBySpaceId(Long kgBasesSpaceCId) {
        if (kgBasesSpaceCId == null) {
            return new ArrayList<>();
        }
        
        // 使用关联表查询指定空间下的问答思维链ID列表
        LambdaQueryWrapper<KgBasesSpaceRelationC> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.eq(KgBasesSpaceRelationC::getKgBasesSpaceCId, kgBasesSpaceCId)
                .eq(KgBasesSpaceRelationC::getBaseType, KnowModelType.QA_COT.getType())
                .eq(KgBasesSpaceRelationC::getIsDeleted, false);
        
        List<KgBasesSpaceRelationC> relations = kgBasesSpaceRelationCMapper.selectList(relationQueryWrapper);
        
        // 提取问答思维链ID列表
        List<Long> qaCotIds = relations.stream()
                .map(KgBasesSpaceRelationC::getKgKnowledgeBasesId)
                .collect(Collectors.toList());
        
        if (qaCotIds.isEmpty()) {
            return qaCotIds;
        }
        
        // 过滤掉已删除的问答思维链
        LambdaQueryWrapper<KgQaCotD> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(KgQaCotD::getKgQaCotId, qaCotIds)
               .eq(KgQaCotD::getIsDeleted, "0");
        
        List<KgQaCotD> qaCots = kgQaCotDMapper.selectList(wrapper);
        return qaCots.stream()
                .map(KgQaCotD::getKgQaCotId)
                .collect(Collectors.toList());
    }

    /**
     * 处理审核规则
     *
     * @param evt      请求参数
     * @param username 用户名
     */
    private void handleAuditRules(GetQaCotsEvt evt, String username) {
        // TODO: 根据实际情况实现审核规则
        if (evt.getState() != null && ((evt.getWhetherAuditOrder() != null && evt.getState().contains(CaseStateEnum.PENDING_AUDIT.getStateValue()))
                || evt.getWhetherOnline() != null)) {
            KgAuditorAllocationRuleConfigD ruleConfig = auditorAllocationRuleConfigMapper.selectById(BigInteger.ONE);

            if (evt.getType() == null || (evt.getType() != null && !CommonConstant.YES.equals(evt.getType()))) {
                if (StringUtils.isNotBlank(username)) {
                    KgBasesAuditPersonDEvt personEvt = new KgBasesAuditPersonDEvt();
                    personEvt.setRealName(username);
                    List<KgBasesAuditPersonDVm> auditPersons = basesAuditPersonMapper.getBasesAuditPerson(personEvt);

                    if (CollUtil.isNotEmpty(auditPersons)) {
                        KgBasesAuditPersonDVm auditPerson = auditPersons.get(0);
                        //省级管理员不做限制
                        if (CommonConstant.NO_PASS.equals(auditPerson.getWhetherProvinceAdmin())) {
                            if (!CommonConstant.PASS.equals(auditPerson.getWhetherRegionAdmin())) {
                                //不是区域管理员，就需要通过分配规则过滤数据
                                switch (ruleConfig.getValue()) {
                                    case "1":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(auditPerson.getMajor().split(","))));
                                        break;
                                    case "2":
                                        evt.setStateRegion(auditPerson.getRegionList().stream().map(Long::valueOf).collect(Collectors.toList()));
                                        break;
                                    case "3":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(auditPerson.getMajor().split(","))));
                                        evt.setStateRegion(auditPerson.getRegionList().stream().map(Long::valueOf).collect(Collectors.toList()));
                                        break;
                                }
                            } else {
                                //是区域管理员，只需要过滤区域，不受专业限制
                                evt.setStateRegion(auditPerson.getRegionList().stream().map(Long::valueOf).collect(Collectors.toList()));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 丰富区域信息
     *
     * @param qaCotList 问答思维链列表
     */
    private void enrichRegionInfo(List<KgQaCotD> qaCotList) {
        if (CollectionUtil.isEmpty(qaCotList)) {
            return;
        }

        // 获取所有区域ID
        List<Long> regionIds = qaCotList.stream()
                .map(KgQaCotD::getRegion)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(regionIds)) {
            return;
        }

        // 查询区域信息
        List<QueryRegionListVm> regions = regionMapper.findRegionByIds(regionIds, regionConfig.getRegionSchema());
        Map<Long, String> regionMap = regions.stream()
                .collect(Collectors.toMap(
                        QueryRegionListVm::getRegionId,
                        QueryRegionListVm::getRegionName,
                        (v1, v2) -> v1
                ));

        // 设置区域名称
        qaCotList.forEach(qaCot -> {
            if (qaCot.getRegion() != null) {
                qaCot.setRegionName(regionMap.get(qaCot.getRegion()));
            }
        });
    }

    @Override
    public QaCotInfoVm getQaCotInfo(QaCotInfoEvt evt) {
        if (evt.getKgQaCotId() == null) {
            return null;
        }

        // 查询问答思维链详情
        KgQaCotD qaCot = kgQaCotDMapper.selectById(evt.getKgQaCotId());
        if (qaCot == null) {
            return null;
        }

        // 查询区域和分类信息
        List<KgQaCotD> qaCotList = new ArrayList<>();
        qaCotList.add(qaCot);
        enrichRegionInfo(qaCotList);

        // 构建返回结果
        QaCotInfoVm qaCotInfoVm = new QaCotInfoVm();
        qaCotInfoVm.setQaCot(qaCot);
        
        // 查询最新的审核评论记录
        KgQaCotAuditDimensionD latestAuditComment = getLatestAuditComment(evt.getKgQaCotId());
        qaCotInfoVm.setLatestAuditComment(latestAuditComment);

        return qaCotInfoVm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp addOrUpdateQaCot(KgQaCotD evt) {
        // 添加问答思维链
        if (evt.getKgQaCotId() == null) {
            // 校验必填字段
            ServiceResp validationResult = validateRequiredFields(evt);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 检查问题是否重复
            if (checkQaCotRepeat(evt.getQuestion())) {
                return ServiceResp.fail("问题已存在，请勿重复添加");
            }

            String username = PtSecurityUtils.getUsername();
            // 设置基本信息
            evt.setIsDeleted("0");
            evt.setCreatedUserName(username);
            evt.setCreatedTime(new Date());
            evt.setUpdatedUserName(username);
            evt.setUpdatedTime(new Date());
            evt.setSearchNumber(0L);
            evt.setClickCount(0L);
            evt.setTenantCode(tenantCode);
            evt.setAuditStatus(CommonConstant.STATUS_PASS);
            evt.setCategoryId(Objects.nonNull(evt.getCategoryId()) ? evt.getCategoryId() : FormatUtil.intParse(knowledgeBaseConfig.getQwd().getCategoryId()));
            evt.setState(CaseStateEnum.NEW.getStateValue());
            evt.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());
            
            // 设置上报相关字段的初始值
            if (evt.getReportStatus() == null) {
                evt.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
            }
            if (evt.getReportDescription() == null) {
                evt.setReportDescription(null);
            }
            if (evt.getReportTime() == null) {
                evt.setReportTime(null);
            }

            //todo 默认添加
            evt.setPermissionType(PermissionTypeEnum.PERMISSION_CONFIG.getCode());

            // 插入数据库
            kgQaCotDMapper.insert(evt);
            
            // 如果有关联的知识空间ID，创建关联关系
            if (evt.getKgBasesSpaceCId() != null) {
                createQaCotSpaceRelation(evt.getKgQaCotId(), evt.getKgBasesSpaceCId());
            }
            
            // 新增到ES
            saveQaCotToEs(evt);

            return ServiceResp.success("添加成功", evt.getKgQaCotId());
        }
        // 更新
        else {
            KgQaCotD original = kgQaCotDMapper.selectById(evt.getKgQaCotId());
            if (original == null) {
                return ServiceResp.fail("问答思维链不存在");
            }

            // 校验必填字段
            ServiceResp validationResult = validateRequiredFields(evt);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 如果问题内容发生变化，需要检查重复
            if (!original.getQuestion().equals(evt.getQuestion()) && checkQaCotRepeat(evt.getQuestion())) {
                return ServiceResp.fail("问题已存在，请勿重复添加");
            }

            // 设置更新信息
            evt.setUpdatedUserName(PtSecurityUtils.getUsername());
            evt.setUpdatedTime(new Date());

            // 更新数据库
            kgQaCotDMapper.updateById(evt);
            
            // 更新到ES
            updateQaCotToEs(evt);

            return ServiceResp.success("更新成功", evt.getKgQaCotId());
        }
    }

    @Override
    public ServiceResp addBatchQaCot(List<KgQaCotD> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return ServiceResp.fail("输入列表不能为空");
        }

        // 1. 初始化数据结构
        List<FailedRecord> failedList = new ArrayList<>();
        List<KgQaCotD> validRecords = new ArrayList<>();
        Set<String> questionSet = new HashSet<>(dataList.size());
        Map<String, KgQaCotD> existingQuestions = new HashMap<>();

        // 2. 第一轮遍历：基本校验和重复检查（内存级）
        for (int i = 0; i < dataList.size(); i++) {
            KgQaCotD evt = dataList.get(i);
            try {
                // 2.1 必填字段校验
                String validationError = validateRequiredFieldsForBatch(evt);
                if (validationError != null) {
                    failedList.add(new FailedRecord(i, evt, validationError));
                    continue;
                }

                // 2.2 内存级重复检查
                if (!questionSet.add(evt.getQuestion())) {
                    failedList.add(new FailedRecord(i, evt, "批量数据中问题重复"));
                    continue;
                }

                validRecords.add(evt);
            } catch (Exception e) {
                failedList.add(new FailedRecord(i, evt, "系统异常: " + e.getMessage()));
            }
        }

        // 3. 数据库级批量重复检查（单次查询）
        if (!validRecords.isEmpty()) {
            List<String> validQuestions = validRecords.stream()
                    .map(KgQaCotD::getQuestion)
                    .collect(Collectors.toList());

            // 单次数据库查询获取所有重复问题
            Map<String, KgQaCotD> dbDuplicates = kgQaCotDMapper.checkQuestionsRepeat(validQuestions);
            existingQuestions.putAll(dbDuplicates);

            // 3.1 移除数据库重复项
            Iterator<KgQaCotD> iterator = validRecords.iterator();
            while (iterator.hasNext()) {
                KgQaCotD record = iterator.next();
                if (existingQuestions.containsKey(record.getQuestion())) {
                    failedList.add(new FailedRecord(
                            dataList.indexOf(record),
                            record,
                            "问题已存在于数据库，ID: " + existingQuestions.get(record.getQuestion()).getKgQaCotId()
                    ));
                    iterator.remove();
                }
            }
        }

        // 4. 批量插入有效数据
        if (!validRecords.isEmpty()) {
            String username = PtSecurityUtils.getUsername();
            Date now = new Date();

            // 4.1 批量设置字段
            validRecords.forEach(evt -> {
                evt.setIsDeleted("0");
                evt.setCreatedUserName(username);
                evt.setCreatedTime(now);
                evt.setUpdatedUserName(username);
                evt.setUpdatedTime(now);
                evt.setSearchNumber(0L);
                evt.setClickCount(0L);
                evt.setTenantCode(tenantCode);
                evt.setAuditStatus(CommonConstant.STATUS_PASS);
                evt.setCategoryId(Objects.nonNull(evt.getCategoryId())
                        ? evt.getCategoryId()
                        : FormatUtil.intParse(knowledgeBaseConfig.getQwd().getCategoryId()));
                evt.setState(CaseStateEnum.NEW.getStateValue());
                evt.setPermissionType(PermissionTypeEnum.PERMISSION_CONFIG.getCode());

                // 上报字段
                evt.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
                evt.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());
                evt.setReportDescription(null);
                evt.setReportTime(null);
            });

            // 4.2 批量数据库插入（使用MyBatis批量模式）
            kgQaCotDMapper.batchInsert(validRecords);

            // 4.3 处理关联关系（批量）
            batchCreateQaCotSpaceRelations(validRecords, validRecords.get(0).getKgBasesSpaceCId(), username, now);

            // 4.4 批量写入ES
            batchSaveQaCotsToEs(validRecords);
        }

        // 5. 处理返回结果
        if (failedList.isEmpty()) {
            return ServiceResp.success("全部保存成功，共新增" + validRecords.size() + "条记录");
        } else {
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", validRecords.size());
            result.put("failedList", failedList);
            result.put("failedCount", failedList.size());
            return ServiceResp.success("部分保存成功", result);
        }
    }

    private void batchCreateQaCotSpaceRelations(List<KgQaCotD> qaCots, Long spaceId, String currentUser, Date currentTime) {
        if (qaCots.isEmpty()) {
            return;
        }

        try {
            List<KgBasesSpaceRelationC> relations = new ArrayList<>();

            for (KgQaCotD qaCot : qaCots) {
                if (qaCot.getKgQaCotId() != null) {
                    KgBasesSpaceRelationC relation = new KgBasesSpaceRelationC();
                    relation.setKgBasesSpaceCId(spaceId);
                    relation.setKgKnowledgeBasesId(qaCot.getKgQaCotId()); // 问答对ID作为关联ID
                    relation.setBaseType(KnowModelType.QA_COT.getType()); // 2代表问答对类型
                    relation.setIsDeleted(false);
                    relation.setCreatedUser(currentUser);
                    relation.setCreatedTime(currentTime);
                    relation.setUpdatedUser(currentUser);
                    relation.setUpdatedTime(currentTime);

                    relations.add(relation);
                }
            }

            // 真正的批量插入关联关系
            if (!relations.isEmpty()) {
                int batchSize = 500;
                int totalCount = 0;
                for (int i = 0; i < relations.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, relations.size());
                    List<KgBasesSpaceRelationC> batch = relations.subList(i, endIndex);

                    // 真正的批量插入，一次SQL插入多条记录
                    int insertCount = spaceRelationMapper.batchInsert(batch);
                    totalCount += insertCount;
                }
                log.info("批量创建问答思维链空间关联关系成功，数量：{}", totalCount);
            }
        } catch (Exception e) {
            log.error("批量创建问答思维链空间关联关系失败", e);
        }
    }

    /**
     * 批量保存问答对到ES索引
     */
    private void batchSaveQaCotsToEs(List<KgQaCotD> qaCotDS) {
        if (CollectionUtils.isEmpty(qaCotDS)) {
            return;
        }

        try {
            List<IndexQuery> indexQueries = new ArrayList<>();

            for (KgQaCotD qaCot : qaCotDS) {
                // 验证ID是否存在
                if (qaCot.getKgQaCotId() == null) {
                    log.warn("问答思维链ID为空，跳过ES索引写入，问题：{}", qaCot.getQuestion());
                    continue;
                }

                // 创建ES索引对象
                KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
                BeanUtils.copyProperties(qaCot, knowledgeBasesIdx);

                // 设置问答对特有字段
                knowledgeBasesIdx.setKnowledgeType(knowledgeBaseConfig.getQwd().getCategoryId());
                knowledgeBasesIdx.setCategoryId(FormatUtil.longParse(knowledgeBaseConfig.getQwd().getCategoryId()));
                knowledgeBasesIdx.setBasesType(KnowModelType.QA_COT.getType()); // 问答思维链类型
                knowledgeBasesIdx.setKgKnowledgeBasesId(qaCot.getKgQaPairId());
                knowledgeBasesIdx.setKnowledgeName(qaCot.getQuestion());
                knowledgeBasesIdx.setSummary(qaCot.getAnswer());
                knowledgeBasesIdx.setUpdatedTime(new Date());
                knowledgeBasesIdx.setChangeTime(new Date());

                // 设置全文检索字段
                knowledgeBasesIdx.setFullText();

                // 使用前缀区分不同来源的ID，避免重复
                String documentId = KnowModelType.QA_COT.getEsPrefix() + qaCot.getKgQaPairId();
                IndexQuery indexQuery = new IndexQueryBuilder()
                        .withId(documentId)
                        .withObject(knowledgeBasesIdx)
                        .build();

                indexQueries.add(indexQuery);
            }

            // 批量写入ES
            IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
            List<IndexedObjectInformation> results = elasticsearchRestTemplate.bulkIndex(indexQueries, indexCoordinates);

            log.info("批量保存问答思维链到ES索引成功，数量：{}", results.size());

        } catch (Exception ex) {
            if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                // ES写入失败不影响主流程，只记录日志
                log.error("批量保存问答思维链到ES索引失败", ex);
            }
        }
    }

    private String validateRequiredFieldsForBatch(KgQaCotD qaCot) {
        if (StringUtils.isBlank(qaCot.getInstitution())) return "组织机构不能为空";
        if (StringUtils.isBlank(qaCot.getAuthor())) return "作者名称不能为空";
        if (StringUtils.isBlank(qaCot.getMajor())) return "专业领域不能为空";
        if (StringUtils.isBlank(qaCot.getApplicationScene())) return "应用场景不能为空";
        if (StringUtils.isBlank(qaCot.getKnowledgeOrigin())) return "知识来源不能为空";
        if (StringUtils.isBlank(qaCot.getFlowScene())) return "流程领域不能为空";
        if (StringUtils.isBlank(qaCot.getPublicity())) return "公开范围不能为空";
        if (StringUtils.isBlank(qaCot.getPeriodValidity())) return "有效期不能为空";
        if (StringUtils.isBlank(qaCot.getLifeCycle())) return "生命周期不能为空";
        if (StringUtils.isBlank(qaCot.getQuestionClassify())) return "问题分类不能为空";
        if (StringUtils.isBlank(qaCot.getQuestion())) return "问题不能为空";
        if (StringUtils.isBlank(qaCot.getAnswer())) return "答案不能为空";
        if (Objects.isNull(qaCot.getRegion())) return "区域不能为空";
        if (Objects.isNull(qaCot.getKgBasesSpaceCId())) return "知识空间不能为空";
        if (StringUtils.isBlank(qaCot.getThoughtProcess())) return "思考过程不能为空";
        return null;
    }

    /**
     * 校验问答思维链必填字段
     *
     * @param qaCot 问答思维链实体
     * @return 校验结果
     */
    private ServiceResp validateRequiredFields(KgQaCotD qaCot) {
        if (StringUtils.isBlank(qaCot.getInstitution())) {
            return ServiceResp.fail("组织机构不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getAuthor())) {
            return ServiceResp.fail("作者名称不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getMajor())) {
            return ServiceResp.fail("专业领域不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getApplicationScene())) {
            return ServiceResp.fail("应用场景不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getKnowledgeOrigin())) {
            return ServiceResp.fail("知识来源不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getFlowScene())) {
            return ServiceResp.fail("流程领域不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getPublicity())) {
            return ServiceResp.fail("公开范围不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getPeriodValidity())) {
            return ServiceResp.fail("有效期不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getLifeCycle())) {
            return ServiceResp.fail("生命周期不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getQuestionClassify())) {
            return ServiceResp.fail("问题分类不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getQuestion())) {
            return ServiceResp.fail("问题不能为空");
        }
        
        if (StringUtils.isBlank(qaCot.getAnswer())) {
            return ServiceResp.fail("答案不能为空");
        }
        if (Objects.isNull(qaCot.getRegion())) {
            return ServiceResp.fail("区域不能为空");
        }

        if (Objects.isNull(qaCot.getKgBasesSpaceCId())) {
            ServiceResp.fail("知识空间不能为空");
        }

        if (StringUtils.isBlank(qaCot.getThoughtProcess())) {
            return ServiceResp.fail("思考过程不能为空");
        }


        
        return ServiceResp.success("校验成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp deleteQaCots(QaCotIdsEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答思维链ID不能为空");
        }

        for (Long id : evt.getIds()) {
            // 查询问答思维链状态
            KgQaCotD qaCot = kgQaCotDMapper.selectById(id);
            if (qaCot == null) {
                continue;
            }
            
            // 根据状态决定是物理删除还是逻辑删除
            if (CaseStateEnum.NEW.getStateValue().equals(qaCot.getState())) {
                // 草稿状态的问答思维链进行物理删除
                kgQaCotDMapper.deleteById(id);
                
                // 删除关联关系记录
                deleteQaCotSpaceRelation(id, true);
            } else {
                // 其他状态的问答思维链进行逻辑删除
                KgQaCotD updateQaCot = new KgQaCotD();
                updateQaCot.setKgQaCotId(id);
                updateQaCot.setIsDeleted("1"); // 标记为已删除
                updateQaCot.setUpdatedTime(new Date());
                updateQaCot.setUpdatedUserName(PtSecurityUtils.getUsername());
                kgQaCotDMapper.updateById(updateQaCot);
                
                // 逻辑删除关联关系记录
                deleteQaCotSpaceRelation(id, false);
            }
            
            // 从ES中删除
            deleteQaCotFromEs(id);
        }

        return ServiceResp.success("删除成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp submitQaCots(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答思维链ID不能为空");
        }

        // 更新数据库中的状态
        for (Long id : evt.getIds()) {
            KgQaCotD qaCot = new KgQaCotD();
            qaCot.setKgQaCotId(id);
            qaCot.setState(CaseStateEnum.PENDING_AUDIT.getStateValue()); // 待审核状态
            qaCot.setSubmitTime(new Date());
            qaCot.setUpdatedTime(new Date());
            qaCot.setUpdatedUserName(PtSecurityUtils.getUsername());
            kgQaCotDMapper.updateById(qaCot);
        }
        
        // 更新ES中的状态
        batchUpdateQaCotStateToEs(evt.getIds(), CaseStateEnum.PENDING_AUDIT.getStateValue());

        return ServiceResp.success("提交审核成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp approveQaCots(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答思维链ID不能为空");
        }

        // 更新数据库中的状态
        Date auditTime = new Date();
        for (Long id : evt.getIds()) {
            KgQaCotD qaCot = new KgQaCotD();
            qaCot.setKgQaCotId(id);
            qaCot.setState(evt.getState()); // 待发布
            qaCot.setAuditTime(auditTime);
            qaCot.setReviewer(PtSecurityUtils.getUsername());
            qaCot.setReleaseTime(auditTime);
            qaCot.setUpdatedTime(auditTime);
            qaCot.setUpdatedUserName(PtSecurityUtils.getUsername());
            kgQaCotDMapper.updateById(qaCot);

            Integer commentPass = evt.getState().equals(CaseStateEnum.TO_BE_RELEASED.getStateValue()) ? CommonConstant.PASS : CommonConstant.NO_PASS;
            
            // 保存审核评论
            saveAuditComment(id, evt.getComment(), String.valueOf(commentPass), auditTime);
        }
        
        // 更新ES中的状态
        batchUpdateQaCotStateToEs(evt.getIds(), evt.getState());

        return ServiceResp.success("审核通过成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp rejectQaCots(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答思维链ID不能为空");
        }

        // 更新数据库中的状态
        Date auditTime = new Date();
        for (Long id : evt.getIds()) {
            KgQaCotD qaCot = new KgQaCotD();
            qaCot.setKgQaCotId(id);
            qaCot.setState(CaseStateEnum.AUDIT_NOT_PASS.getStateValue()); // 已驳回状态
            qaCot.setAuditTime(auditTime);
            qaCot.setUpdatedTime(auditTime);
            qaCot.setUpdatedUserName(PtSecurityUtils.getUsername());
            kgQaCotDMapper.updateById(qaCot);
            
            // 保存审核评论
            saveAuditComment(id, evt.getComment(), String.valueOf(CommonConstant.NO_PASS), auditTime);
        }
        
        // 更新ES中的状态
        batchUpdateQaCotStateToEs(evt.getIds(), CaseStateEnum.AUDIT_NOT_PASS.getStateValue());

        return ServiceResp.success("审核驳回成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp offlineQaCots(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答思维链ID不能为空");
        }

        String state = evt.getState();
        String operationMsg = "";

        // 根据状态判断是上线还是下线操作
        boolean isOnline = CaseStateEnum.PUBLISHED.getStateValue().equals(state);
        boolean isOffline = CaseStateEnum.OFF_LINE.getStateValue().equals(state);

        if (!isOnline && !isOffline) {
            return ServiceResp.fail("无效的状态值，只支持上线或下线操作");
        }

        Date now = new Date();

        // 更新数据库中的状态
        for (Long id : evt.getIds()) {
            // 获取当前问答思维链信息
            KgQaCotD currentQaCot = kgQaCotDMapper.selectById(id);
            if (currentQaCot == null) {
                continue;
            }

            KgQaCotD qaCot = new KgQaCotD();
            qaCot.setKgQaCotId(id);
            qaCot.setState(state);
            qaCot.setUpdatedTime(now);
            qaCot.setUpdatedUserName(PtSecurityUtils.getUsername());

            // 处理下线操作
            if (isOffline) {
                // 如果是首次下线，设置标记
                if (currentQaCot.getWhetherFirstOffline() == null || currentQaCot.getWhetherFirstOffline() != 1) {
                    qaCot.setWhetherFirstOffline(1);
                }
                operationMsg = "下线成功";
            } 
            // 处理上线操作
            else if (isOnline) {
                // 如果已经进行过首次下线，则设置为二次上线状态
                if (currentQaCot.getWhetherFirstOffline() != null && currentQaCot.getWhetherFirstOffline() == 1) {
                    qaCot.setState(CaseStateEnum.SECOND_ONLINE.getStateValue());
                }
                qaCot.setReleaseTime(now); // 上线时设置发布时间
                operationMsg = "上线成功";
            }

            kgQaCotDMapper.updateById(qaCot);
        }
        
        // 更新ES中的状态
        batchUpdateQaCotStateToEs(evt.getIds(), state);

        return ServiceResp.success(operationMsg);
    }

    

    @Override
    public boolean checkQaCotRepeat(String question) {
        if (StringUtils.isEmpty(question)) {
            return false;
        }

        Integer count = kgQaCotDMapper.checkQuestionRepeat(question);
        return count != null && count > 0;
    }

    /**
     * 从知识库导入问答思维链
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param filePath           文件路径
     * @param organization       组织机构
     * @param knowledgeOrigin    知识来源
     * @param lifeCycle          生命周期
     * @return 导入的问答思维链列表
     */
    public List<KgQaCotD> importQaCotsFromKnowledgeBase(Long kgKnowledgeBasesId, String filePath, String organization, String knowledgeOrigin, String lifeCycle, String author, Long region, Long kgBasesSpaceCId) {
        List<KgQaCotD> result = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        try {
            // 校验必填参数
            if (Objects.isNull(region)) {
                errorMessages.add("区域不能为空");
            }
            if (Objects.isNull(kgBasesSpaceCId)) {
                errorMessages.add("知识空间不能为空");
            }
            if (StringUtils.isEmpty(organization)) {
                errorMessages.add("组织机构不能为空");
            }
            if (StringUtils.isEmpty(knowledgeOrigin)) {
                errorMessages.add("知识来源不能为空");
            }
            if (StringUtils.isEmpty(lifeCycle)) {
                errorMessages.add("生命周期不能为空");
            }
            if (StringUtils.isEmpty(author)) {
                errorMessages.add("作者名称不能为空");
            }
            
            if (CollectionUtil.isNotEmpty(errorMessages)) {
                String errorMsg = String.join("，", errorMessages);
                log.error("导入问答思维链参数校验失败: {}", errorMsg);
                throw new KnowledgeBasesException(errorMsg);
            }

            // 打开Excel文件
            File excelFile = new File(filePath);
            if (!excelFile.exists()) {
                log.error("导入问答思维链Excel文件不存在: {}", filePath);
                return result;
            }

            // 查询字典值映射
            Map<String, Map<String, String>> dictMaps = new HashMap<>();
            dictMaps.put("periodValidity", getDictionaryMap("periodValidity")); // 有效期
            dictMaps.put("C3", getDictionaryMap("C3")); // 流程领域
            dictMaps.put("question_category", getDictionaryMap("question_category")); // 问题类别
            dictMaps.put("C2", getDictionaryMap("C2")); // 专业领域
            dictMaps.put("C12", getDictionaryMap("C12")); // 应用场景
            dictMaps.put("C13", getDictionaryMap("C13")); // 适用范围

            Workbook workbook = new XSSFWorkbook(Files.newInputStream(excelFile.toPath()));

            // 2. 识别问答思维链模板Sheet
            Sheet qaSheet = findSceneSheet(workbook);
            if (qaSheet == null) {
                log.error(" 未找到有效的问答思维链模板表（需包含'应用场景'列）");
                throw new RuntimeException("未找到有效的问答思维链模板表（需包含'应用场景'列）");
            }
            log.info(" 找到问答思维链模板表: {}", qaSheet.getSheetName());
            if (qaSheet == null) {
                workbook.close();
                return result;
            }

            // 获取表头行
            Row headerRow = qaSheet.getRow(0);
            if (headerRow == null) {
                workbook.close();
                return result;
            }

            // 解析表头
            Map<String, Integer> headerMap = new HashMap<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                String headerValue = headerRow.getCell(i) != null ? headerRow.getCell(i).getStringCellValue() : "";
                headerMap.put(headerValue, i);
            }
            
            // 检查必填列是否存在
            checkRequiredColumns(headerMap);

            // 获取最后一行的索引
            int lastRowNum = qaSheet.getLastRowNum();
            int successCount = 0;
            int failCount = 0;

            // 从第2行开始解析数据（跳过表头）
            for (int i = 1; i <= lastRowNum; i++) {
                Row dataRow = qaSheet.getRow(i);
                if (dataRow == null) {
                    continue;
                }

                try {
                    // 获取基本字段值
                    String question = getCellStringValue(dataRow, headerMap.getOrDefault("问题", -1));
                    String answer = getCellStringValue(dataRow, headerMap.getOrDefault("答案", -1));
                    String thoughtProcess = getCellStringValue(dataRow, headerMap.getOrDefault("思考过程", -1));
                    String majorValue = getCellStringValue(dataRow, headerMap.getOrDefault("子专业", -1));
                    String appSceneValue = getCellStringValue(dataRow, headerMap.getOrDefault("应用子场景", -1));
                    String flowSceneValue = getCellStringValue(dataRow, headerMap.getOrDefault("流程领域", -1));
                    String scopeValue = getCellStringValue(dataRow, headerMap.getOrDefault("适用范围", -1));
                    String validityValue = getCellStringValue(dataRow, headerMap.getOrDefault("有效期", -1));
                    String questionTypeValue = getCellStringValue(dataRow, headerMap.getOrDefault("问题类别", -1));

                    // 校验必填字段
                    List<String> rowErrors = new ArrayList<>();
                    if (StringUtils.isEmpty(question)) rowErrors.add("问题");
                    if (StringUtils.isEmpty(answer)) rowErrors.add("答案");
                    if (StringUtils.isEmpty(majorValue)) rowErrors.add("子专业");
                    if (StringUtils.isEmpty(appSceneValue)) rowErrors.add("应用子场景");
                    if (StringUtils.isEmpty(flowSceneValue)) rowErrors.add("流程领域");
                    if (StringUtils.isEmpty(scopeValue)) rowErrors.add("适用范围");
                    if (StringUtils.isEmpty(validityValue)) rowErrors.add("有效期");
                    if (StringUtils.isEmpty(questionTypeValue)) rowErrors.add("问题类别");

                    if (CollectionUtil.isNotEmpty(rowErrors)) {
                        log.warn("第{}行数据缺少必填字段: {}, 已跳过", i, String.join(", ", rowErrors));
                        failCount++;
                        continue;
                    }

                    // 创建问答思维链对象
                    KgQaCotD qaCot = new KgQaCotD();

                    // 设置关联知识库ID
                    qaCot.setKgKnowledgeBasesId(kgKnowledgeBasesId);

                    // 设置组织机构（从参数传入）
                    qaCot.setInstitution(organization);

                    // 解析基本字段
                    qaCot.setAuthor(author);

                    // 从参数导入
                    qaCot.setRegion(region);

                    // 专业领域 - 需要转换字典值
                    qaCot.setMajor(convertDictValue(majorValue, dictMaps.get("C2")));

                    // 应用场景 - 需要转换字典值
                    qaCot.setApplicationScene(convertDictValue(appSceneValue, dictMaps.get("C12")));

                    // 知识来源（从参数传入）
                    qaCot.setKnowledgeOrigin(knowledgeOrigin);

                    // 流程领域 - 需要转换字典值
                    qaCot.setFlowScene(convertDictValue(flowSceneValue, dictMaps.get("C3")));

                    // 公开范围/适用范围 - 需要转换字典值
                    qaCot.setPublicity(convertDictValue(scopeValue, dictMaps.get("C13")));

                    // 有效期 - 需要转换字典值
                    qaCot.setPeriodValidity(convertDictValue(validityValue, dictMaps.get("periodValidity")));

                    // 生命周期（从参数传入）
                    qaCot.setLifeCycle(lifeCycle);

                    // 问题类别 - 需要转换字典值
                    qaCot.setQuestionClassify(convertDictValue(questionTypeValue, dictMaps.get("question_category")));

                    // 主要信息
                    qaCot.setQuestion(question);
                    qaCot.setAnswer(answer);
                    qaCot.setThoughtProcess(thoughtProcess);
                    qaCot.setAuditStatus(CommonConstant.STATUS_PASS);

                    // 设置基本信息
                    qaCot.setIsDeleted("0");
                    qaCot.setCreatedUserName(PtSecurityUtils.getUsername());
                    qaCot.setCreatedTime(new Date());
                    qaCot.setUpdatedUserName(PtSecurityUtils.getUsername());
                    qaCot.setUpdatedTime(new Date());
                    qaCot.setSearchNumber(0L);
                    qaCot.setClickCount(0L);
                    qaCot.setState(CaseStateEnum.PENDING_AUDIT.getStateValue()); // 导入时直接设置为待审核状态
                    qaCot.setTenantCode(tenantCode);
                    qaCot.setCategoryId(FormatUtil.intParse(knowledgeBaseConfig.getQwd().getCategoryId()));
                    
                    // 设置上报相关字段的初始值
                    qaCot.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
                    qaCot.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());
                    qaCot.setReportDescription(null);
                    qaCot.setReportTime(null);
                    qaCot.setPermissionType(PermissionTypeEnum.PERMISSION_CONFIG.getCode());

                    // 检查问题是否重复
                    if (checkQaCotRepeat(question)) {
                        log.warn("第{}行问答思维链中存在重复问题，已跳过: {}", i, question);
                        failCount++;
                        continue;
                    }

                    // 插入数据库
                    kgQaCotDMapper.insert(qaCot);

                    // 如果有知识空间ID，创建关联关系
                    if (kgBasesSpaceCId != null) {
                        createQaCotSpaceRelation(qaCot.getKgQaCotId(), kgBasesSpaceCId);
                    }

                    // 添加到结果列表
                    result.add(qaCot);

                    // 保存到ES索引
                    saveQaCotToEs(qaCot);
                    
                    successCount++;
                } catch (Exception e) {
                    log.error("处理第{}行数据时发生错误: {}", i, e.getMessage());
                    failCount++;
                }
            }
            
            log.info("导入问答思维链完成，成功: {}条，失败: {}条", successCount, failCount);
            workbook.close();
        } catch (Exception e) {
            log.error("导入问答思维链失败", e);
        }

        return result;
    }

    /**
     * 检查Excel模板中是否包含所有必填列
     *
     * @param headerMap 表头映射
     */
    private void checkRequiredColumns(Map<String, Integer> headerMap) {
        List<String> missingColumns = new ArrayList<>();
        String[] requiredColumns = {"问题", "答案", "子专业", "应用子场景", "流程领域", "适用范围", "有效期", "问题类别"};
        
        for (String column : requiredColumns) {
            if (!headerMap.containsKey(column)) {
                missingColumns.add(column);
            }
        }
        
        if (!missingColumns.isEmpty()) {
            String errorMsg = "Excel模板缺少必填列: " + String.join(", ", missingColumns);
            log.error(errorMsg);
            throw new KnowledgeBasesException(errorMsg);
        }
    }

    /**
     * 查找问答思维链模板Sheet
     */
    private Sheet findSceneSheet(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet currentSheet = workbook.getSheetAt(i);
            Row firstRow = currentSheet.getRow(0);  // 表头行

            if (firstRow != null && containsColumn(firstRow, "应用场景") &&
                    !currentSheet.getSheetName().contains(" 问答思维链")) {
                return currentSheet;
            }
        }
        return null;
    }
    private boolean containsColumn(Row row, String columnName) {
        for (Cell cell : row) {
            if (cell.getStringCellValue().trim().equals(columnName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取字典值映射
     *
     * @param codeType 字典类型
     * @return 字典值映射 (显示值 -> 代码值)
     */
    private Map<String, String> getDictionaryMap(String codeType) {
        try {
            // 查询字典表获取映射关系
            List<DictionaryEntity> dictList = dictionaryMapper.selectList(
                    new LambdaQueryWrapper<DictionaryEntity>().eq(DictionaryEntity::getCodeType, codeType));
            
            // 构建显示值到代码值的映射
            return dictList.stream()
                    .collect(Collectors.toMap(
                            DictionaryEntity::getCodeName,  // 显示值
                            entity -> FormatUtil.stringParse(entity.getBdpDirId()), // 代码值
                            (v1, v2) -> v1                  // 如果有重复的key，保留第一个
                    ));
        } catch (Exception e) {
            log.error("获取字典值映射失败, codeType={}", codeType, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 转换字典值（从显示值到代码值）
     *
     * @param displayValue 显示值
     * @param dictMap      字典映射
     * @return 代码值，如果没有找到映射则返回原值
     */
    private String convertDictValue(String displayValue, Map<String, String> dictMap) {
        if (StringUtils.isEmpty(displayValue) || dictMap == null || dictMap.isEmpty()) {
            return displayValue;
        }
        
        return dictMap.getOrDefault(displayValue, displayValue);
    }

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 新增问答思维链信息到ES
     * 
     * @param qaCot 问答思维链实体
     */
    private void saveQaCotToEs(KgQaCotD qaCot) {
        try {
            // 创建ES索引对象
            KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
            BeanUtil.copyProperties(qaCot, knowledgeBasesIdx);
            
            // 设置知识类型为问答思维链
            knowledgeBasesIdx.setKnowledgeType(knowledgeBaseConfig.getQwd().getCategoryId());
            knowledgeBasesIdx.setCategoryId(FormatUtil.longParse(knowledgeBaseConfig.getQwd().getCategoryId()));
            knowledgeBasesIdx.setBasesType(KnowModelType.QA_COT.getType());
            knowledgeBasesIdx.setKgKnowledgeBasesId(qaCot.getKgQaCotId());
            knowledgeBasesIdx.setKnowledgeName(qaCot.getQuestion());
            knowledgeBasesIdx.setSummary(qaCot.getAnswer());
            knowledgeBasesIdx.setUpdatedTime(new Date());
            knowledgeBasesIdx.setChangeTime(new Date());
            
            // 设置全文检索字段
            knowledgeBasesIdx.setFullText();
            
            // 直接保存到ES，使用前缀避免ID重复
            try {
                // 创建索引请求
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = KnowModelType.QA_COT.getEsPrefix() + qaCot.getKgQaCotId();
                IndexQuery indexQuery = new IndexQueryBuilder()
                    .withId(documentId)
                    .withObject(knowledgeBasesIdx)
                    .build();
                // 使用index方法指定文档ID
                elasticsearchRestTemplate.index(indexQuery, indexCoordinates);
            } catch (Exception ex) {
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("问答思维链ES保存失败:{}", ex.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("新增问答思维链到ES失败", e);
        }
    }
    
    /**
     * 将问答思维链信息更新到ES
     * 
     * @param qaCot 问答思维链实体
     */
    private void updateQaCotToEs(KgQaCotD qaCot) {
        try {
            // 创建ES索引对象
            KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
            BeanUtil.copyProperties(qaCot, knowledgeBasesIdx);
            
            // 设置知识类型为问答思维链
            knowledgeBasesIdx.setKnowledgeType(knowledgeBaseConfig.getQwd().getCategoryId());
            knowledgeBasesIdx.setBasesType(KnowModelType.QA_COT.getType());
            knowledgeBasesIdx.setKgKnowledgeBasesId(qaCot.getKgQaCotId());
            knowledgeBasesIdx.setKnowledgeName(qaCot.getQuestion());
            knowledgeBasesIdx.setSummary(qaCot.getAnswer());
            knowledgeBasesIdx.setUpdatedTime(new Date());
            knowledgeBasesIdx.setChangeTime(new Date());
            
            // 设置全文检索字段
            knowledgeBasesIdx.setFullText();
            
            // 转换为JSON并更新到ES
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
            String jsonString;
       
            try {
                jsonString = mapper.writeValueAsString(knowledgeBasesIdx);
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = KnowModelType.QA_COT.getEsPrefix() + qaCot.getKgQaCotId();
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(Document.parse(jsonString))
                        .build();
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            } catch (Exception ex) {
                // 如果更新失败，尝试保存
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("问答思维链ES更新失败:{}", ex.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("更新问答思维链到ES失败", e);
        }
    }
    
    /**
     * 从ES中删除问答思维链信息
     * 
     * @param qaCotId 问答思维链ID
     */
    private void deleteQaCotFromEs(Long qaCotId) {
        try {
            // 查询问答思维链状态
            KgQaCotD qaCot = kgQaCotDMapper.selectById(qaCotId);
            
            // 使用前缀区分不同来源的ID，避免重复
            String documentId = KnowModelType.QA_COT.getEsPrefix() + qaCotId;
            IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
            
            if (qaCot != null && CaseStateEnum.NEW.getStateValue().equals(qaCot.getState())) {
                // 草稿状态的问答思维链进行物理删除
                elasticsearchRestTemplate.delete(documentId, indexCoordinates);
            } else {
                // 其他状态的问答思维链进行逻辑删除，更新isDeleted字段
                Document document = Document.create();
                document.put("isDeleted", "1");
                document.put("updatedTime", LocalDateTime.now().format(formatter));
                
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(document)
                        .build();
                
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            }
        } catch (Exception ex) {
            if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                log.error("问答思维链ES删除失败:{}", ex.getMessage());
            }
        }
    }
    
    /**
     * 批量更新问答思维链状态到ES
     * 
     * @param ids 问答思维链ID列表
     * @param state 状态值
     */
    private void batchUpdateQaCotStateToEs(List<Long> ids, String state) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        
        for (Long id : ids) {
            try {
                Document document = Document.create();
                document.put("state", state);
                document.put("updatedTime", LocalDateTime.now().format(formatter));
                
                // 如果是上线或二次上线状态，设置发布时间
                if (CaseStateEnum.PUBLISHED.getStateValue().equals(state) ||
                    CaseStateEnum.SECOND_ONLINE.getStateValue().equals(state)) {
                    document.put("releaseTime", LocalDateTime.now().format(formatter));
                }
                
                // 如果是下线状态，设置是否首次下线
                if (CaseStateEnum.OFF_LINE.getStateValue().equals(state)) {
                    // 查询当前问答思维链状态
                    KgQaCotD qaCot = kgQaCotDMapper.selectById(id);
                    if (qaCot != null && (qaCot.getWhetherFirstOffline() == null || qaCot.getWhetherFirstOffline() != 1)) {
                        document.put("whetherFirstOffline", 1);
                    }
                }
                
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = KnowModelType.QA_COT.getEsPrefix() + id;
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(document)
                        .build();
                
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            } catch (Exception ex) {
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("操作ES失败,入参:{}", state, ex);
                }
            }
        }
    }
    
    /**
     * 创建问答思维链与知识空间的关联关系
     *
     * @param qaCotId 问答思维链ID
     * @param spaceCId 知识空间ID
     */
    private void createQaCotSpaceRelation(Long qaCotId, Long spaceCId) {
        try {
            // 先检查是否已存在关联关系
            LambdaQueryWrapper<KgBasesSpaceRelationC> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KgBasesSpaceRelationC::getKgKnowledgeBasesId, qaCotId)
                    .eq(KgBasesSpaceRelationC::getKgBasesSpaceCId, spaceCId)
                    .eq(KgBasesSpaceRelationC::getBaseType, KnowModelType.QA_COT.getType())
                    .eq(KgBasesSpaceRelationC::getIsDeleted, false);
            
            KgBasesSpaceRelationC existingRelation = kgBasesSpaceRelationCMapper.selectOne(queryWrapper);
            
            // 如果已存在关联关系，则不需要创建
            if (existingRelation != null) {
                return;
            }
            
            // 创建新的关联关系
            KgBasesSpaceRelationC relation = new KgBasesSpaceRelationC();
            relation.setKgKnowledgeBasesId(qaCotId);
            relation.setKgBasesSpaceCId(spaceCId);
            relation.setBaseType(KnowModelType.QA_COT.getType());
            relation.setIsDeleted(false);
            relation.setCreatedTime(new Date());
            relation.setCreatedUser(PtSecurityUtils.getUsername());
            relation.setUpdatedTime(new Date());
            relation.setUpdatedUser(PtSecurityUtils.getUsername());
            
            // 插入关联关系
            kgBasesSpaceRelationCMapper.insert(relation);
        } catch (Exception e) {
            log.error("创建问答思维链与知识空间关联关系失败", e);
        }
    }
    
    /**
     * 删除问答思维链与知识空间的关联关系
     *
     * @param qaCotId 问答思维链ID
     * @param isPhysicalDelete 是否物理删除
     */
    private void deleteQaCotSpaceRelation(Long qaCotId, boolean isPhysicalDelete) {
        try {
            // 查询问答思维链的所有关联关系
            LambdaQueryWrapper<KgBasesSpaceRelationC> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KgBasesSpaceRelationC::getKgKnowledgeBasesId, qaCotId)
                    .eq(KgBasesSpaceRelationC::getBaseType, KnowModelType.QA_COT.getType())
                    .eq(KgBasesSpaceRelationC::getIsDeleted, false);
            
            List<KgBasesSpaceRelationC> relations = kgBasesSpaceRelationCMapper.selectList(queryWrapper);
            
            if (relations.isEmpty()) {
                return;
            }
            
            // 根据删除类型处理关联关系
            if (isPhysicalDelete) {
                // 物理删除
                for (KgBasesSpaceRelationC relation : relations) {
                    kgBasesSpaceRelationCMapper.deleteById(relation.getId());
                }
            } else {
                // 逻辑删除
                for (KgBasesSpaceRelationC relation : relations) {
                    relation.setIsDeleted(true);
                    relation.setUpdatedTime(new Date());
                    relation.setUpdatedUser(PtSecurityUtils.getUsername());
                    kgBasesSpaceRelationCMapper.updateById(relation);
                }
            }
        } catch (Exception e) {
            log.error("删除问答思维链与知识空间关联关系失败", e);
        }
    }
    
    /**
     * 保存问答思维链审核评论
     *
     * @param qaCotId    问答思维链ID
     * @param comment     审核评论内容
     * @param whetherPass 是否通过审核(1:通过,0:不通过)
     * @param auditTime   审核时间
     */
    private void saveAuditComment(Long qaCotId, String comment, String whetherPass, Date auditTime) {
        if (qaCotId == null) {
            return;
        }
        
        try {
            // 创建审核评论记录
            KgQaCotAuditDimensionD auditDimension = new KgQaCotAuditDimensionD();
            auditDimension.setQaCotId(qaCotId);
            auditDimension.setComment(comment);
            auditDimension.setWhetherPass(whetherPass);
            
            // 查询当前最大排序号
            Integer maxOrderNum = kgQaCotAuditDimensionDMapper.selectMaxOrderNum(qaCotId);
            auditDimension.setOrderNum(maxOrderNum != null ? maxOrderNum + 1 : 1);
            
            // 设置创建和更新信息
            auditDimension.setCreatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setCreatedTime(auditTime);
            auditDimension.setUpdatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setUpdatedTime(auditTime);
            
            // 插入审核评论记录
            kgQaCotAuditDimensionDMapper.insert(auditDimension);
        } catch (Exception e) {
            log.error("保存问答思维链审核评论失败, qaCotId={}", qaCotId, e);
        }
    }
    
    /**
     * 获取问答思维链最新的审核评论记录
     *
     * @param qaCotId 问答思维链ID
     * @return 最新的审核评论记录
     */
    private KgQaCotAuditDimensionD getLatestAuditComment(Long qaCotId) {
        if (qaCotId == null) {
            return null;
        }
        
        try {
            // 查询最新的审核评论记录
            LambdaQueryWrapper<KgQaCotAuditDimensionD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KgQaCotAuditDimensionD::getQaCotId, qaCotId)
                    .orderByDesc(KgQaCotAuditDimensionD::getCreatedTime)
                    .last("LIMIT 1");

            return kgQaCotAuditDimensionDMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("获取问答思维链最新审核评论记录失败, qaCotId={}", qaCotId, e);
            return null;
        }
    }

    @Override
    public void exportQaCotsToExcel(ExportQaCotsEvt evt, HttpServletResponse response) {
        log.info("开始导出问答思维链，参数：{}", evt);

        try {
            // 获取要导出的问答思维链数据
            List<KgQaCotD> qaCotList = getQaCotsForExport(evt);

            if (CollectionUtil.isEmpty(qaCotList)) {
                log.warn("没有找到要导出的问答思维链数据");
                throw new KnowledgeBasesException("没有找到要导出的问答思维链数据");
            }

            // 转换数据并生成Excel
            generateExcelFile(qaCotList, evt.getFileName(), response);

            batchUpdateQaCotReportStatus(qaCotList.stream().map(KgQaCotD::getKgQaCotId).collect(Collectors.toList())
                    , null, ExportStatusEnum.EXPORTED.getCode(), "");

            log.info("问答思维链导出完成，共导出{}条数据", qaCotList.size());
        } catch (Exception e) {
            log.error("导出问答思维链失败", e);
            throw new KnowledgeBasesException("导出问答思维链失败：" + e.getMessage());
        }
    }

    /**
     * 根据导出条件获取问答思维链数据
     */
    private List<KgQaCotD> getQaCotsForExport(ExportQaCotsEvt evt) {
        if (ExportQaCotsEvt.EXPORT_TYPE_SELECTED == evt.getExportType()) {
            // 选择导出：根据ID列表查询
            if (CollUtil.isEmpty(evt.getQaCotIds())) {
                throw new KnowledgeBasesException("选择导出时问答思维链ID列表不能为空");
            }

            LambdaQueryWrapper<KgQaCotD> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(KgQaCotD::getKgQaCotId, evt.getQaCotIds())
                   .eq(KgQaCotD::getIsDeleted, "0")
                   .orderByDesc(KgQaCotD::getCreatedTime);

            return kgQaCotDMapper.selectList(wrapper);

        } else if (ExportQaCotsEvt.EXPORT_TYPE_ALL == evt.getExportType()) {
            // 全选导出：使用与getQaCots方法相同的查询逻辑
            if (evt.getQueryCondition() == null) {
                throw new KnowledgeBasesException("全选导出时查询条件不能为空");
            }

            GetQaCotsEvt queryEvt = evt.getQueryCondition();
            return queryQaCotsWithConditions(queryEvt, false);
        } else {
            throw new KnowledgeBasesException("不支持的导出类型：" + evt.getExportType());
        }
    }

    /**
     * 生成Excel文件
     */
    private void generateExcelFile(List<KgQaCotD> qaCotList, String fileName, HttpServletResponse response) throws IOException {
        Workbook workbook = null;
        try {
            // 读取模板文件
            ClassPathResource templateResource = new ClassPathResource("excel/template/问答思维链导出模板.xlsx");
            if (!templateResource.exists()) {
                throw new KnowledgeBasesException("问答思维链导出模板文件不存在");
            }

            // 打开模板文件
            workbook = new XSSFWorkbook(templateResource.getInputStream());

            // 获取第二个sheet页（索引为1）
            Sheet dataSheet = null;
            if (workbook.getNumberOfSheets() >= 2) {
                dataSheet = workbook.getSheetAt(1);
            } else {
                throw new KnowledgeBasesException("模板文件中没有找到第二个sheet页");
            }

            // 转换数据
            List<String[]> dataList = convertQaCotsToExcelData(qaCotList);

            // 从第二行开始写入数据（索引为1，因为第一行是表头）
            int rowIndex = 1;
            for (String[] rowData : dataList) {
                Row row = dataSheet.createRow(rowIndex++);
                for (int i = 0; i < rowData.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(rowData[i] != null ? rowData[i] : "");
                }
            }

            // 生成带时间戳的文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String finalFileName = (StringUtils.isNotBlank(fileName) ? fileName : "问答思维链导出数据") + "_" + timestamp + ".xlsx";

            // 使用浏览器下载
            ExcelUtil.setBrowser(response, null, finalFileName);

            // 写入响应流
            workbook.write(response.getOutputStream());

        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            throw new IOException("生成Excel文件失败：" + e.getMessage());
        } finally {
            // 确保workbook资源被正确关闭
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook失败", e);
                }
            }
        }
    }

    /**
     * 将问答思维链数据转换为Excel数据格式
     */
    private List<String[]> convertQaCotsToExcelData(List<KgQaCotD> qaCotList) {
        List<String[]> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (CollectionUtil.isEmpty(qaCotList)) {
            return dataList;
        }

        // 批量获取所有需要的字典数据，一次性查询提升性能
        Map<String, Map<String, String>> allDictionaryMaps = initAllQaCotDictionaryMaps();

        for (int i = 0; i < qaCotList.size(); i++) {
            KgQaCotD qaCot = qaCotList.get(i);
            // fixed 记得改长度
            String[] rowData = new String[14];

            // 序号
            rowData[0] = String.valueOf(i + 1);

            // 应用场景 - 获取父字典名称，多选时取第一个
            rowData[1] = getFirstParentDictName(qaCot.getApplicationScene(), allDictionaryMaps.get("C12_PARENT"));

            // 应用子场景 - 获取当前字典名称，多选时取第一个
            rowData[2] = getFirstDictName(qaCot.getApplicationScene(), allDictionaryMaps.get("C12"));

            // 问题类别 - 字典值转换
            rowData[3] = getFirstDictName(qaCot.getQuestionClassify(), allDictionaryMaps.get("question_category"));

            // 专业领域 - 获取父字典名称，多选时取第一个
            rowData[4] = getFirstParentDictName(qaCot.getMajor(), allDictionaryMaps.get("C2_PARENT"));

            // 子专业 - 获取当前字典名称，多选时取第一个
            rowData[5] = getFirstDictName(qaCot.getMajor(), allDictionaryMaps.get("C2"));

            // 问题ID
            rowData[6] = String.valueOf(qaCot.getKgQaCotId());

            // 问答轮数 - 暂时设为1，根据实际需求调整
            rowData[7] = "1";

            // 问题
            rowData[8] = StringUtils.isNotBlank(qaCot.getQuestion()) ? qaCot.getQuestion() : "";

            // 思考过程
            rowData[9] = StringUtils.isNotBlank(qaCot.getThoughtProcess()) ? qaCot.getThoughtProcess() : "";

            // 答案
            rowData[10] = StringUtils.isNotBlank(qaCot.getAnswer()) ? qaCot.getAnswer() : "";

            // 适用范围 - 多选时取第一个
            rowData[11] = getFirstDictName(qaCot.getPublicity(), allDictionaryMaps.get("C13"));

            // 流程领域 - 多选时取第一个
            rowData[12] = getFirstDictName(qaCot.getFlowScene(), allDictionaryMaps.get("C3"));

            // 有效期 - 字典值转换
            rowData[13] = getFirstDictName(qaCot.getPeriodValidity(), allDictionaryMaps.get("periodValidity"));

            dataList.add(rowData);
        }

        return dataList;
    }

    /**
     * 初始化问答思维链所有字典映射，一次性批量查询所有字典数据
     */
    private Map<String, Map<String, String>> initAllQaCotDictionaryMaps() {
        Map<String, Map<String, String>> allMaps = new HashMap<>();
        
        // 定义需要查询的字典类型
        List<String> dictTypes = Arrays.asList("C12", "C2", "C13", "C3", "periodValidity", "question_category");
        
        // 一次性查询所有字典数据
        List<DictionaryEntity> allDictList = dictionaryMapper.findByCodeTypeIn(dictTypes);
        
        if (CollectionUtil.isNotEmpty(allDictList)) {
            // 按字典类型分组
            Map<String, List<DictionaryEntity>> dictGroups = allDictList.stream()
                    .collect(Collectors.groupingBy(DictionaryEntity::getCodeType));
            
            // 处理每个字典类型
            for (Map.Entry<String, List<DictionaryEntity>> entry : dictGroups.entrySet()) {
                String dictType = entry.getKey();
                List<DictionaryEntity> dictList = entry.getValue();
                
                // 构建ID到名称的映射
                Map<String, String> idToNameMap = new HashMap<>();
                Map<String, String> idToParentNameMap = new HashMap<>();
                
                // 对于需要父子关系的字典类型，构建父字典映射
                if ("C12".equals(dictType) || "C2".equals(dictType)) {
                    // 先构建临时的Long类型ID到名称映射，用于查找父字典
                    Map<Long, String> tempIdToNameMap = new HashMap<>();
                    for (DictionaryEntity dict : dictList) {
                        tempIdToNameMap.put(dict.getBdpDirId(), dict.getCodeName());
                    }
                    
                    // 构建最终的字符串ID映射
                    for (DictionaryEntity dict : dictList) {
                        String dictId = String.valueOf(dict.getBdpDirId());
                        String dictName = dict.getCodeName();
                        
                        idToNameMap.put(dictId, dictName);
                        
                        // 如果有父ID，查找父字典名称
                        if (dict.getParentId() != null) {
                            String parentName = tempIdToNameMap.get(dict.getParentId());
                            if (StringUtils.isNotBlank(parentName)) {
                                idToParentNameMap.put(dictId, parentName);
                            }
                        }
                    }
                    
                    allMaps.put(dictType + "_PARENT", idToParentNameMap);
                } else {
                    // 对于不需要父子关系的字典类型，只构建普通映射
                    for (DictionaryEntity dict : dictList) {
                        String dictId = String.valueOf(dict.getBdpDirId());
                        String dictName = dict.getCodeName();
                        idToNameMap.put(dictId, dictName);
                    }
                }
                
                allMaps.put(dictType, idToNameMap);
            }
        }
        
        return allMaps;
    }

    /**
     * 获取第一个字典名称（处理多选情况）
     */
    private String getFirstDictName(String dictIds, Map<String, String> dictMap) {
        if (StringUtils.isBlank(dictIds) || dictMap == null) {
            return "";
        }
        
        // 处理多选情况，取第一个
        String firstId = dictIds.contains(",") ? dictIds.split(",")[0].trim() : dictIds.trim();
        return dictMap.getOrDefault(firstId, "");
    }

    /**
     * 获取第一个父字典名称（处理多选情况）
     */
    private String getFirstParentDictName(String dictIds, Map<String, String> parentDictMap) {
        if (StringUtils.isBlank(dictIds) || parentDictMap == null) {
            return "";
        }
        
        // 处理多选情况，取第一个
        String firstId = dictIds.contains(",") ? dictIds.split(",")[0].trim() : dictIds.trim();
        return parentDictMap.getOrDefault(firstId, "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp batchUpdateReportStatus(BatchUpdateCotReportStatusEvt evt) {
        log.info("批量修改问答思维链上报状态，参数：{}", evt);

        try {
            // 参数校验
            if (evt.getOperationType() == null) {
                return ServiceResp.fail("操作类型不能为空");
            }

            if (StringUtils.isBlank(evt.getReportStatus())) {
                return ServiceResp.fail("上报状态不能为空");
            }

            // 验证上报状态值是否有效
            if (!isValidReportStatus(evt.getReportStatus())) {
                return ServiceResp.fail("无效的上报状态值");
            }

            // 获取要修改的问答思维链ID列表
            List<Long> qaCotIds = getQaCotIdsForUpdate(evt);

            if (CollectionUtil.isEmpty(qaCotIds)) {
                return ServiceResp.fail("没有找到要修改的问答思维链数据");
            }

            // 批量更新上报状态
            int updateCount = batchUpdateQaCotReportStatus(qaCotIds, evt.getReportStatus(), evt.getExportStatus(), evt.getReportDescription());

            log.info("批量修改问答思维链上报状态完成，共修改{}条数据", updateCount);
            return ServiceResp.success("批量修改成功，共修改" + updateCount + "条数据");

        } catch (Exception e) {
            log.error("批量修改问答思维链上报状态失败", e);
            return ServiceResp.fail("批量修改失败：" + e.getMessage());
        }
    }

    private void checkBases(InsertKnowledgeBasesEvt evt) throws KnowledgeGraphException {
        //标题
        if (ObjectUtils.isEmpty(evt.getKnowledgeName())) {
            throw new KnowledgeGraphException(ExceptionDefinition.BASES_TITLE_NUMBER_LIMIT_ERROR);
        }
    }

    @Override
    public KnowledgeBasesEntity importQaCot(InsertKnowledgeBasesEvt evt) {
        if (evt.getKgBasesSpaceCId() == null) {
            throw new KnowledgeGraphException("请选择知识空间");
        }
        // 校验标题
        checkBases(evt);

        KnowledgeBasesEntity knowledgeBases = new KnowledgeBasesEntity();
        KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();

        // 替换操作词汇中的分号为逗号，以统一格式
        if (StringUtils.isNotBlank(evt.getOperativeWord())) {
            if (evt.getOperativeWord().contains(";")) {
                evt.setOperativeWord(evt.getOperativeWord().replace(";", ","));
            } else if (evt.getOperativeWord().contains("；")) {
                evt.setOperativeWord(evt.getOperativeWord().replace("；", ","));
            }
        }

        // 将事件对象属性复制到知识库实体对象
        BeanUtil.copyProperties(evt, knowledgeBases);

        boolean result = false;
        String filePath = evt.getKnowledgeFilePath();
        // 文件上传到
        try (InputStream inputStream = ctdfsService.downloadStream(filePath)) {
            String fileName = FileUtil.getFileName(filePath);
            if (inputStream == null) {
                throw new KnowledgeGraphException("文件下载失败，未获取到文件流");
            }
            byte[] fileBytes = IOUtils.toByteArray(inputStream);
            // 设置文件大小
            long fileSize = fileBytes.length;  // This gets the size in bytes
            knowledgeBases.setFilesize(fileSize);  // Assuming setFilesize takes a long parameter
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    MediaType.APPLICATION_OCTET_STREAM_VALUE,
                    fileBytes
            );
            // 添加excel校验
            result = importExcelWithStrictValidation(knowledgeBases, multipartFile);
        } catch (Exception e) {
            log.error("下载并解析文件内容失败", e);
            throw new KnowledgeGraphException("下载并解析文件内容失败: " + e.getMessage());
        }
        // 校验失败
        if (!result) {
            return null;
        }
        // 设置通用的属性值
        knowledgeBases.setIsDeleted(CommonConstant.IS_DELETED);
        //knowledgeBases.setAuditStatus(CommonConstant.STATUS_PASS);
        knowledgeBases.setCategoryId(Long.valueOf(knowledgeBaseConfig.getQwd().getCategoryId()));
        evt.setCategoryId(Integer.valueOf(knowledgeBaseConfig.getQwd().getCategoryId()));

        // 导入统计结果
        Map<String, Integer> importStats = new HashMap<>();
        importStats.put("success", 0);
        importStats.put("fail", 0);

        // 判断是否为新生成的知识库
        if (ObjectUtils.isEmpty(evt.getKgKnowledgeBasesId())) {
            // 添加默认权限
            if (commonConfig.getPerSwitch()) {
                //添加默认权限
                if (evt.getCategoryId() != null) {
                    dtPermissionConverter.initPermission(knowledgeBases, DataPermissionConstant.BASES, Long.valueOf(evt.getCategoryId()));
                }
            }
            // 导入时直接设置为待审核状态
            knowledgeBases.setState(CaseStateEnum.PENDING_AUDIT.getStateValue());
            // 设置文档格式
            if (StringUtils.isNotBlank(evt.getKnowledgeFilePath())) {
                String fileName = FileUtil.getFileName(evt.getKnowledgeFilePath());
                String fileType = FileUtil.getTypePart(fileName);
                if (StringUtils.isNotBlank(fileType)) {
                    knowledgeBases.setDocumentFormat(fileType.toLowerCase());
                }
            }


            // 检查是否存在相同名称的知识库，如果重名则自动添加时间戳
            String finalKnowledgeName = evt.getKnowledgeName();
            LambdaQueryWrapper<KnowledgeBasesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KnowledgeBasesEntity::getKnowledgeName, evt.getKnowledgeName());
            wrapper.eq(KnowledgeBasesEntity::getIsDeleted, CommonConstant.NO);
            List<KnowledgeBasesEntity> knowledgeBasesList = knowledgeBasesDMapper.selectList(wrapper);

            // 如果重名，自动添加时间戳
            if (!knowledgeBasesList.isEmpty()) {
                finalKnowledgeName = reportFormatUtil.generateTimestampedFileName(evt.getKnowledgeName());
                log.info("检测到问答思维链知识库重名，自动修改知识库名: {} -> {}", evt.getKnowledgeName(), finalKnowledgeName);
                knowledgeBases.setKnowledgeName(finalKnowledgeName);
            }

            // 总是执行保存逻辑
            knowledgeBases.setSearchNumber(0);
            try {
                // 存储到PG数据库
                knowledgeBases.setCreatedUserName(PtSecurityUtils.getUsername());
                knowledgeBasesDMapper.insert(knowledgeBases);

                // 存储到知识空间与知识关联关系表
                KgBasesSpaceRelationC kgBasesSpaceRelationC = new KgBasesSpaceRelationC();
                kgBasesSpaceRelationC.setKgBasesSpaceCId(evt.getKgBasesSpaceCId());
                kgBasesSpaceRelationC.setKgKnowledgeBasesId(knowledgeBases.getKgKnowledgeBasesId());
                kgBasesSpaceRelationC.setBaseType(KnowModelType.KNOWLEDGE_BASE.getType());
                kgBasesSpaceRelationCMapper.insertKgBasesSpaceRelationC(kgBasesSpaceRelationC);

                // 存储到ES
                BeanUtil.copyProperties(knowledgeBases, knowledgeBasesIdx);
                extracted(knowledgeBases, knowledgeBasesIdx);
                knowledgeBasesIdx.setFullText();
                elasticsearchRestTemplate.save(knowledgeBasesIdx);

            } catch (Exception e) {
                if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                    log.error("es新增失败，异常信息：{}", e.getMessage());
                    throw new KnowledgeGraphException("新增失败:{}", e.getMessage());
                }
            }
            //文件内容存到缓存
            savefileContentRedis(evt, knowledgeBases);
            //异步查重
            if (knowledgeBases.getKgKnowledgeBasesId() != null) {
                checkFileRepeatAndSaveToRedis(knowledgeBases.getKgKnowledgeBasesId(), false);
            }

            // 如果是问答思维链Excel，将问答思维链导入到问答思维链表中
            Map<String, Integer> qaPairStats = importQaPairsFromExcel(knowledgeBases.getKgKnowledgeBasesId(), evt.getKgBasesSpaceCId(), evt.getKnowledgeFilePath());
            // 合并统计结果
            importStats.put("success", qaPairStats.get("success"));
            importStats.put("fail", qaPairStats.get("fail"));

            // 将统计结果保存到知识库实体中，方便前端获取
            knowledgeBases.setImportError(String.format("导入成功: %d条, 导入失败: %d条",
                    importStats.get("success"), importStats.get("fail")));

            return knowledgeBases;
        } else {
            return null;
        }
    }

    @Override
    public DashboardStatisticsVO getDashboardStatistics() {
        DashboardStatisticsVO vo = new DashboardStatisticsVO();

        // 1. 今日活跃用户统计
        int todayActiveUsers = kgQaCotDMapper.countDistinctCreatedUsersToday();
        int yesterdayActiveUsers = kgQaCotDMapper.countDistinctCreatedUsersYesterday();
        vo.setTodayActiveUsers(todayActiveUsers);
        vo.setActiveUsersChange(todayActiveUsers - yesterdayActiveUsers);

        // 2. 今日语料总数统计
        int todayQaCount = kgQaCotDMapper.countCreatedToday();
        int yesterdayQaCount = kgQaCotDMapper.countCreatedYesterday();
        vo.setTodayQaCount(todayQaCount);
        vo.setQaCountChange(todayQaCount - yesterdayQaCount);

        // 3. 任务语料总数（不统计变化）
        vo.setTaskQaCount(0); // 默认0，根据实际情况设置

        return vo;
    }


    /**
     * excel 基础信息
     * @param
     */
    private void extracted(KnowledgeBasesEntity knowledgeBases, KnowledgeBasesIdx knowledgeBasesIdx) {
        knowledgeBasesIdx.setCreatedTime(knowledgeBases.getCreatedTime());
        knowledgeBasesIdx.setCreatedUserName(knowledgeBases.getCreatedUserName());
        knowledgeBasesIdx.setUpdatedTime(knowledgeBases.getUpdatedTime());
        knowledgeBasesIdx.setUpdatedTime(knowledgeBases.getUpdatedTime());
    }


    /**
     * 文件查重并存储结果到redis
     *
     * @param kgKnowledgeBasesId 当前知识库文件ID
     */
    public void checkFileRepeatAndSaveToRedis(Long kgKnowledgeBasesId, boolean fromPage) {
        // 判断是否启用查重功能
        if (!knowledgeBaseConfig.getSimilarity().isCheck() || fileSimilarityClient == null) {
            log.info("文件查重功能未启用或服务不可用，跳过查重操作");
            return;
        }

        // 调用文件相似度服务进行查重
        try {
            fileSimilarityClient.checkFileSimilarity(kgKnowledgeBasesId, fromPage);
            log.info("文件查重请求已发送，知识库ID: {}", kgKnowledgeBasesId);
        } catch (Exception e) {
            log.error("调用文件查重服务失败", e);
        }
    }

    private void savefileContentRedis(InsertKnowledgeBasesEvt evt, KnowledgeBasesEntity knowledgeBases) {
        if (StrUtil.isNotBlank(evt.getFileContent())) {
            KgKnowledgeFile kgKnowledgeFile = new KgKnowledgeFile();
            kgKnowledgeFile.setKgKnowledgeBasesId(knowledgeBases.getKgKnowledgeBasesId());
            kgKnowledgeFile.setFileContent(evt.getFileContent());
            kgKnowledgeFile.setOperativeWord(evt.getKnowledgeName());
            //获取文件的后缀
            int lastDotIndex = knowledgeBases.getKnowledgeFilePath().lastIndexOf('.');
            if (lastDotIndex != -1) {
                String fileExtension = knowledgeBases.getKnowledgeFilePath().substring(lastDotIndex + 1);
                kgKnowledgeFile.setFileSuffix(fileExtension);
            }
            String prefix = KnowledgeConstant.FILE_REDIS;
            prefix = prefix + knowledgeBases.getKgKnowledgeBasesId();
            ctgRedisService.set(prefix, JSONObject.toJSONString(kgKnowledgeFile));
            log.info("文件内容存到缓存知识库名称" + knowledgeBases.getKnowledgeName());
        }
    }

    /**
     * 从Excel文件导入问答思维链到问答思维链表
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param spaceCId 空间ID
     * @param filePath 文件路径
     * @return 包含导入统计信息的Map，包括成功和失败数量
     */
    private Map<String, Integer> importQaPairsFromExcel(Long kgKnowledgeBasesId, Long spaceCId, String filePath) {
        Map<String, Integer> result = new HashMap<>();
        result.put("success", 0);
        result.put("fail", 0);

        try {
            // 创建临时文件用于存储下载的Excel
            File tempFile = File.createTempFile("qa_cot_import_", ".xlsx");

            // 下载Excel文件到临时文件
            try (InputStream inputStream = ctdfsService.downloadStream(filePath);
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {

                if (inputStream == null) {
                    log.error("导入问答思维链Excel文件下载失败: {}", filePath);
                    result.put("fail", 1);
                    return result;
                }

                IOUtils.copy(inputStream, outputStream);
            }

            // 获取知识库信息以提取组织机构等信息
            KnowledgeBasesEntity knowledgeBase = knowledgeBasesDMapper.selectById(kgKnowledgeBasesId);
            String organization = knowledgeBase != null ? knowledgeBase.getInstitution() : null;
            String knowledgeOrigin = knowledgeBase != null ? knowledgeBase.getKnowledgeOrigin() : null;
            String lifeCycle = knowledgeBase != null ? knowledgeBase.getLifeCycle() : null;
            String author = knowledgeBase != null ? knowledgeBase.getAuthor() : null;
            Long region = knowledgeBase != null ? knowledgeBase.getRegion() : null;

            // 获取问答思维链服务并调用导入方法
            List<KgQaCotD> importedQaPairs = importQaCotsFromKnowledgeBase(
                    kgKnowledgeBasesId,
                    tempFile.getAbsolutePath(),
                    organization,
                    knowledgeOrigin,
                    lifeCycle,
                    author,
                    region,spaceCId
            );

            if (importedQaPairs != null && !importedQaPairs.isEmpty()) {
                int successCount = importedQaPairs.size();
                log.info("成功从知识库ID={}导入{}个问答思维链", kgKnowledgeBasesId, successCount);
                result.put("success", successCount);
            } else {
                log.info("从知识库ID={}导入问答思维链失败或未找到有效问答思维链", kgKnowledgeBasesId);
                result.put("fail", 1);
            }


            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        } catch (Exception e) {
            log.error("导入问答思维链到问答思维链表失败", e);
            result.put("fail", 1);
        }

        return result;
    }

    /**
     * Excel 文件导入和验证的核心方法
     *
     * @param knowledge 知识库实体对象
     * @param file      上传的Excel文件
     * @return 是否导入成功
     */
    private boolean importExcelWithStrictValidation(KnowledgeBasesEntity knowledge, MultipartFile file) {
        if (file == null || knowledge == null) {
            log.error(" 导入失败：文件或知识库对象为空");
            return false;
        }

        try (InputStream inputStream = file.getInputStream()) {
            log.info(" 开始处理Excel文件: {}", file.getOriginalFilename());

            // 1. 读取Excel工作簿
            Workbook workbook = new XSSFWorkbook(inputStream);
            log.debug(" 成功读取Excel工作簿");

            // 2. 识别问答思维链模板Sheet
            Sheet sceneSheet = findSceneSheet(workbook);
            if (sceneSheet == null) {
                log.error(" 未找到有效的问答思维链模板表（需包含'应用场景'列）");
                throw new RuntimeException("未找到有效的问答思维链模板表（需包含'应用场景'列）");
            }
            log.info(" 找到问答思维链模板表: {}", sceneSheet.getSheetName());

            // 3. 验证问答思维链模板表结构
            if (!validateSceneSheetStructure(sceneSheet)) {
                log.error(" 问答思维链模板表结构不符合要求");
                throw new RuntimeException("问答思维链模板表结构不符合要求");
            }
            log.debug(" 问答思维链模板表结构验证通过");

            // 4. 严格验证用户选择的值与Excel内容的匹配性
            validateUserSelectionsWithExcel(sceneSheet, knowledge);

            // 5. 处理问答思维链Sheet(如果存在)
            Sheet qaSheet = findQASheet(workbook);
            if (qaSheet != null) {
                log.info(" 找到问答思维链表: {}", qaSheet.getSheetName());

                if (!validateQASheetStructure(qaSheet)) {
                    log.error(" 问答思维链模板结构不符合要求");
                    throw new RuntimeException("问答思维链模板结构不符合要求");
                }
                log.debug(" 问答思维链表结构验证通过");

                // 解析问答思维链数据
                parseQAPairs(qaSheet, knowledge);

                // 检查问答思维链数据有效性
                if (knowledge.getQuestion() == null || knowledge.getQuestion().isEmpty() ||
                        knowledge.getAnswer() == null || knowledge.getAnswer().isEmpty()) {
                    log.error(" 问答思维链模板中没有有效数据");
                    throw new RuntimeException("问答思维链模板中没有有效数据");
                }
                log.info(" 成功解析问答思维链数据");
            }

            // 6. 设置知识库时间属性
            knowledge.setCreatedTime(new Date());
            knowledge.setUpdatedTime(new Date());
            knowledge.setIsDeleted("0");  // 默认为未删除
            log.info(" 知识库实体属性设置完成");

            log.info("Excel 文件导入验证成功");
            return true;
        } catch (IOException e) {
            log.error(" 处理Excel文件失败：", e);
            throw new RuntimeException("处理Excel文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 查找问答思维链Sheet
     */
    private Sheet findQASheet(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet currentSheet = workbook.getSheetAt(i);
            if (currentSheet.getSheetName().contains(" CoT模板")) {
                return currentSheet;
            }
        }
        return null;
    }

    /**
     * 解析问答思维链数据并填充到KnowledgeBasesEntity对象
     */
    private void parseQAPairs(Sheet sheet, KnowledgeBasesEntity knowledge) {
        // 获取表头索引映射
        Row headerRow = sheet.getRow(0);
        Map<String, Integer> headerMap = new HashMap<>();
        for (Cell cell : headerRow) {
            headerMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }

        // 检查必需列是否存在
        if (!headerMap.containsKey("问题") || !headerMap.containsKey("答案")) {
            return;
        }

        // 遍历数据行，只取第一组问答思维链（可根据需求修改为处理多组问答思维链）
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            // 获取问题和答案
            String question = getCellStringValue(row, headerMap.get("问题"));
            String answer = getCellStringValue(row, headerMap.get("答案"));

            // 忽略空问题或空答案的行
            if (question.isEmpty() || answer.isEmpty()) {
                continue;
            }

            // 设置问题和答案
            knowledge.setQuestion(question);
            knowledge.setAnswer(answer);

            // 只处理第一组有效的问答思维链
            break;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Row row, int columnIndex) {
        if (columnIndex < 0 || row == null) {
            return "";
        }
        Cell cell = row.getCell(columnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((int) cell.getNumericCellValue());
            default:
                return "";
        }
    }

    /**
     * 验证问答思维链模板表结构
     * 必需列：应用场景、应用于场景、问题类别、专业领域、子专业
     */
    private boolean validateSceneSheetStructure(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return false;
        }

        String[] requiredColumns = {"有效期", "应用子场景", "流程领域", "适用范围", "子专业"};
        for (String column : requiredColumns) {
            if (!containsColumn(headerRow, column)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 严格验证用户选择的值与Excel内容的匹配性
     */
    private void validateUserSelectionsWithExcel(Sheet sceneSheet, KnowledgeBasesEntity knowledge) {
        log.info(" 开始验证用户选择值与Excel内容的匹配性");

        // 1. 获取表头索引映射
        Row headerRow = sceneSheet.getRow(0);
        Map<String, Integer> headerMap = new HashMap<>();
        for (Cell cell : headerRow) {
            headerMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
        }
        log.debug(" 表头映射: {}", headerMap);

        // 2. 验证每个字段
        validateField(sceneSheet, headerMap, "应用子场景", knowledge.getApplicationScene());
        validateField(sceneSheet, headerMap, "子专业", knowledge.getMajor());
        // 设置流程领域的数据
        validateFlowSceneField(sceneSheet, headerMap, "流程领域", knowledge);
        validateField(sceneSheet, headerMap, "适用范围", knowledge.getPublicity());
        // 对有效期字段进行特殊处理
        validatePeriodValidityField(sceneSheet, headerMap, "有效期", knowledge);

        log.info(" 所有字段验证通过");
    }

    /**
     * 验证问答思维链模板表结构
     * 必需列：问题ID、问答轮数、问题、答案
     */
    private boolean validateQASheetStructure(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return false;
        }

        String[] requiredColumns = {"问题ID", "问答轮数", "问题", "答案"};
        for (String column : requiredColumns) {
            if (!containsColumn(headerRow, column)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证流程领域字段的匹配性，特殊处理
     * 从字典表中查询code_type为'C3'的记录，验证Excel中的值是否存在于字典中
     * 如果Excel中有多条数据且流程领域不同，则将多个值用逗号连接起来保存
     */
    private void validateFlowSceneField(Sheet sheet, Map<String, Integer> headerMap, String fieldName, KnowledgeBasesEntity knowledge) {
        if (!headerMap.containsKey(fieldName)) {
            log.error("Excel 中缺少必要的列: {}", fieldName);
            throw new RuntimeException("Excel中缺少必要的列: " + fieldName);
        }

        int columnIndex = headerMap.get(fieldName);
        log.info(" 开始验证流程领域字段");

        // 从字典表中查询code_type为'C3'的记录
        List<DictionaryEntity> dictList = dictionaryMapper.selectList(new LambdaQueryWrapper<DictionaryEntity>()
                .eq(DictionaryEntity::getCodeType, "C3"));

        // 收集Excel中的所有流程领域值
        Set<String> flowSceneValues = new HashSet<>();
        Set<String> matchedFlowSceneIds = new HashSet<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            Cell cell = row.getCell(columnIndex);
            if (cell == null) continue;

            String cellValue = getCellStringValue(row, columnIndex);
            if (StringUtils.isNotBlank(cellValue)) {
                flowSceneValues.add(cellValue);
            }
        }

        // 如果Excel中没有流程领域值，则直接返回
        if (flowSceneValues.isEmpty()) {
            log.warn(" Excel中未找到流程领域值");
            return;
        }

        log.debug(" Excel中的流程领域值: {}", flowSceneValues);

        // 验证Excel中的值是否存在于字典中，并收集对应的ID
        for (String flowSceneValue : flowSceneValues) {
            boolean found = false;
            for (DictionaryEntity dict : dictList) {
                if (dict.getCodeName().equals(flowSceneValue)) {
                    matchedFlowSceneIds.add(dict.getBdpDirId().toString());
                    found = true;
                    log.info(" 流程领域匹配成功: {} -> {}", flowSceneValue, dict.getBdpDirId());
                    break;
                }
            }

            if (!found) {
                log.warn(" Excel中的流程领域值[{}]在字典中未找到对应项", flowSceneValue);
            }
        }

        // 将匹配到的ID用逗号连接起来保存到knowledge对象中
        if (!matchedFlowSceneIds.isEmpty()) {
            String flowSceneIds = String.join(",", matchedFlowSceneIds);
            knowledge.setFlowScene(flowSceneIds);
            log.info(" 设置流程领域IDs: {}", flowSceneIds);
        } else {
            log.warn(" 未找到匹配的流程领域ID");
        }
    }

    /**
     * 验证有效期字段的匹配性，特殊处理
     * 如果Excel中有填写有效期，则验证并设置为对应的字典ID；如果没填，则保留前端传入的默认值
     */
    private void validatePeriodValidityField(Sheet sheet, Map<String, Integer> headerMap, String fieldName, KnowledgeBasesEntity knowledge) {
        if (!headerMap.containsKey(fieldName)) {
            log.error("Excel 中缺少必要的列: {}", fieldName);
            throw new RuntimeException("Excel中缺少必要的列: " + fieldName);
        }

        int columnIndex = headerMap.get(fieldName);
        String defaultPeriodValidity = knowledge.getPeriodValidity(); // 保存前端传入的默认值
        log.debug(" 有效期默认值: {}", defaultPeriodValidity);

        // 检查Excel中是否有填写有效期
        boolean foundMatch = false;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            Cell cell = row.getCell(columnIndex);
            if (cell == null) continue;

            String cellValue = getCellStringValue(row, columnIndex);
            if (StringUtils.isNotBlank(cellValue)) {
                // 查找字典表中对应的ID
                List<DictionaryEntity> dictList = dictionaryMapper.selectList(new LambdaQueryWrapper<DictionaryEntity>().eq(DictionaryEntity::getCodeType, "periodValidity"));
                for (DictionaryEntity dict : dictList) {
                    if (dict.getCodeName().equals(cellValue)) {
                        knowledge.setPeriodValidity(dict.getBdpDirId().toString());
                        foundMatch = true;
                        log.info(" 有效期匹配成功: {} -> {}", cellValue, dict.getBdpDirId());
                        break;
                    }
                }

                if (!foundMatch) {
                    log.warn(" Excel中的有效期值[{}]在字典中未找到对应项，将使用默认值", cellValue);
                }
                break; // 只检查第一个非空值
            }
        }

        // 如果Excel中没有填写有效期或未找到匹配项，则保留默认值
        if (!foundMatch) {
            log.info(" 使用默认有效期值: {}", defaultPeriodValidity);
            knowledge.setPeriodValidity(defaultPeriodValidity);
        }
    }

    /**
     * 验证单个字段的匹配性
     */
    private void validateField(Sheet sheet, Map<String, Integer> headerMap, String fieldName, String selectedIdsStr) {
        if (!headerMap.containsKey(fieldName)) {
            log.error("Excel 中缺少必要的列: {}", fieldName);
            throw new RuntimeException("Excel中缺少必要的列: " + fieldName);
        }

        if (StringUtils.isBlank(selectedIdsStr)) {
            log.warn(" 用户未选择{}，跳过验证", fieldName);
            return;
        }

        log.info(" 开始验证字段: {}", fieldName);
        log.debug(" 用户选择值: {}", selectedIdsStr);

        // 1. 获取用户选择的所有字典项code_name
        List<String> userSelectedNames = new ArrayList<>();
        for (Long id : parseIds(selectedIdsStr)) {
            DictionaryEntity dict = dictionaryMapper.selectById(id);
            if (dict == null) {
                log.error(" 无效的字典ID: {} 在字段 {}", id, fieldName);
                throw new RuntimeException("无效的ID: " + id + " 在字段 " + fieldName);
            }
            userSelectedNames.add(dict.getCodeName());
            log.debug(" 字典ID {} 对应名称: {}", id, dict.getCodeName());
        }

        // 2. 获取Excel中该列的所有唯一值
        int colIndex = headerMap.get(fieldName);
        Set<String> excelValues = new HashSet<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                String value = getCellStringValue(row, colIndex).trim();
                if (!value.isEmpty()) {
                    excelValues.add(value);
                }
            }
        }
        log.debug("Excel 中{}列的有效值: {}", fieldName, excelValues);

        // 3. 验证每个用户选择的值是否存在于Excel中
        for (String selectedName : userSelectedNames) {
            if (!excelValues.contains(selectedName)) {
                log.error(" 选择的值 '{}' 在Excel的 {} 列中不存在", selectedName, fieldName);
                throw new RuntimeException("选择的值 '" + selectedName +
                        "' 在Excel的 " + fieldName + " 列中不存在");
            }
            log.debug(" 验证通过: {} 存在于Excel的{}列中", selectedName, fieldName);
        }
    }

    /**
     * 将逗号分隔的ID字符串转换为Long列表
     */
    private List<Long> parseIds(String idsStr) {
        if (StringUtils.isBlank(idsStr)) {
            return Collections.emptyList();
        }
        return Arrays.stream(idsStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }


    /**
     * 根据操作类型获取要修改的问答思维链ID列表
     */
    private List<Long> getQaCotIdsForUpdate(BatchUpdateCotReportStatusEvt evt) {
        if (BatchUpdateReportStatusEvt.OPERATION_TYPE_SELECTED == evt.getOperationType()) {
            // 选择修改：直接使用传入的ID列表
            if (CollUtil.isEmpty(evt.getQaCotIds())) {
                throw new KnowledgeBasesException("选择修改时问答思维链ID列表不能为空");
            }
            return evt.getQaCotIds();

        } else if (BatchUpdateReportStatusEvt.OPERATION_TYPE_ALL == evt.getOperationType()) {
            // 全选修改：使用与getQaCots方法相同的查询逻辑
            if (evt.getQueryCondition() == null) {
                throw new KnowledgeBasesException("全选修改时查询条件不能为空");
            }

            GetQaCotsEvt queryEvt = evt.getQueryCondition();
            List<KgQaCotD> qaCotList = queryQaCotsWithConditions(queryEvt, true);
            return qaCotList.stream()
                    .map(KgQaCotD::getKgQaCotId)
                    .collect(Collectors.toList());
        } else {
            throw new KnowledgeBasesException("不支持的操作类型：" + evt.getOperationType());
        }
    }

    /**
     * 验证上报状态值是否有效
     */
    private boolean isValidReportStatus(String reportStatus) {
        return ReportStatusEnum.NOT_REPORT.getCode().equals(reportStatus) ||
               ReportStatusEnum.REPORTED.getCode().equals(reportStatus) ||
               ReportStatusEnum.REPORT_FAILED.getCode().equals(reportStatus);
    }

    /**
     * 批量更新问答思维链上报状态
     */
    private int batchUpdateQaCotReportStatus(List<Long> qaCotIds, String reportStatus, String exportStatus, String reportDescription) {
        if (CollectionUtil.isEmpty(qaCotIds)) {
            return 0;
        }

        Date currentTime = new Date();
        String currentUser = PtSecurityUtils.getUsername();

        // 使用MyBatis-Plus的批量更新
        LambdaUpdateWrapper<KgQaCotD> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(KgQaCotD::getKgQaCotId, qaCotIds)
                .eq(KgQaCotD::getIsDeleted, "0")
                .set(StringUtils.isNotBlank(reportStatus), KgQaCotD::getReportStatus, reportStatus)
                .set(StringUtils.isNotBlank(exportStatus), KgQaCotD::getExportStatus, exportStatus)
                .set(KgQaCotD::getReportDescription, reportDescription)
                .set(KgQaCotD::getReportTime, currentTime)
                .set(KgQaCotD::getUpdatedUserName, currentUser)
                .set(KgQaCotD::getUpdatedTime, currentTime);

        return kgQaCotDMapper.update(null, updateWrapper);
    }
}