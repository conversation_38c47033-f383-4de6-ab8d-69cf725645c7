package com.ffcs.oss.kg.system.service.knowledgeBase.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.FormatUtil;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeBasesException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.common.core.util.ExcelUtil;
import com.ffcs.oss.kg.data.enums.CaseStateEnum;
import com.ffcs.oss.kg.data.enums.ExportStatusEnum;
import com.ffcs.oss.kg.data.enums.KnowModelType;
import com.ffcs.oss.kg.data.enums.ReportStatusEnum;
import com.ffcs.oss.kg.data.es.KnowledgeBasesIdx;
import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.*;
import com.ffcs.oss.kg.data.model.vm.bases.KgBasesAuditPersonDVm;
import com.ffcs.oss.kg.data.model.vm.know.MajorStatisticsVm;
import com.ffcs.oss.kg.data.model.vm.region.QueryRegionListVm;
import com.ffcs.oss.kg.data.rd.entity.DictionaryEntity;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KgBasesSpaceRelationC;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.KnowledgeBasesEntity;
import com.ffcs.oss.kg.data.rd.entity.qa.KgQaPairAuditDimensionD;
import com.ffcs.oss.kg.data.rd.mapper.DictionaryMapper;
import com.ffcs.oss.kg.data.rd.mapper.KgQaPairDMapper;
import com.ffcs.oss.kg.data.rd.mapper.RegionMapper;
import com.ffcs.oss.kg.data.rd.mapper.cases.AuditorAllocationRuleConfigMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.BasesAuditPersonMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.KgBasesSpaceRelationCMapper;
import com.ffcs.oss.kg.data.rd.mapper.qa.KgQaPairAuditDimensionDMapper;
import com.ffcs.oss.kg.system.config.KnowledgeBaseConfig;
import com.ffcs.oss.kg.system.config.RegionConfig;
import com.ffcs.oss.kg.system.events.qa.BatchUpdateQaStateEvt;
import com.ffcs.oss.kg.system.events.qa.QaPairIdsEvt;
import com.ffcs.oss.kg.system.events.qa.QaPairInfoEvt;
import com.ffcs.oss.kg.system.service.knowledgeBase.QaPairService;
import com.ffcs.oss.kg.system.vm.qa.QaPairInfoVm;
import com.ffcs.oss.kg.system.vm.qa.QaPairVm;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问答对服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
public class QaPairServiceImpl implements QaPairService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${cndids.tenant-code}")
    private String tenantCode;

    @Resource
    private KgQaPairDMapper kgQaPairDMapper;

    @Resource
    private RegionMapper regionMapper;

    @Resource
    private RegionConfig regionConfig;

    @Resource
    private KnowledgeBaseConfig knowledgeBaseConfig;
    
    // 添加ES相关字段
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private AuditorAllocationRuleConfigMapper auditorAllocationRuleConfigMapper;

    @Resource
    private BasesAuditPersonMapper basesAuditPersonMapper;

    @Resource
    private EsKnowSearchServiceImpl esKnowSearchService;
    
    @Resource
    private DictionaryMapper dictionaryMapper;
    
    @Resource
    private KgBasesSpaceRelationCMapper kgBasesSpaceRelationCMapper;
    
    @Resource
    private KgQaPairAuditDimensionDMapper kgQaPairAuditDimensionDMapper;
    
    // 问答对知识类型常量
    private static final String QA_BASE_TYPE = "2";

    /**
     * 构建公共查询条件（包含权限处理逻辑）
     */
    private void buildCommonQueryConditions(GetQaPairsEvt evt) {
        // 设置用户信息
        if (StrUtil.isEmpty(evt.getBeforeOneUser())) {
            evt.setBeforeOneUser(PtSecurityUtils.getUsername()); // 临时设置，需要替换为实际获取用户的方法
        }

        // 处理问题关键词
        if (StrUtil.isNotEmpty(evt.getQuestionKeyword())) {
            evt.setQuestionKeyword(evt.getQuestionKeyword().trim());
        }

        // 处理区域信息
        if (CollUtil.isNotEmpty(evt.getCurrentUserList()) && CollUtil.isEmpty(evt.getRegionList())) {
            evt.setRegionList(evt.getCurrentUserList().stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList()));
        }

        // 处理审核规则
        String username = PtSecurityUtils.getUsername();

        //todo 暂时
        // username = "tianqq";
        handleAuditRules(evt, username);
    }

    /**
     * 使用与getQaPairs方法相同的查询逻辑查询问答对数据
     * @param evt 查询条件
     * @param onlySelectId 是否只查询ID字段（用于提高性能）
     * @return 问答对实体列表
     */
    private List<KgQaPairD> queryQaPairsWithConditions(GetQaPairsEvt evt, boolean onlySelectId) {
        // 处理权限和公共查询条件
        evt.setCountTotal(true);
        buildCommonQueryConditions(evt);

        // 根据知识空间ID获取关联的问答对ID列表
        List<Long> kgQaPairIds = new ArrayList<>();
        if (evt.getKgBasesSpaceCId() != null) {
            kgQaPairIds = getQaPairIdsBySpaceId(evt.getKgBasesSpaceCId());
        }

        // 如果没有找到问答对，直接返回空列表
        if (kgQaPairIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 设置问答对ID列表
        evt.setKgQaPairIds(kgQaPairIds);

        if (StrUtil.isBlank(evt.getFullText())) {
            // 普通数据库查询
            return queryQaPairsFromDatabase(evt, onlySelectId);
        } else {
            // ES全文检索查询
            return queryQaPairsFromElasticsearch(evt, onlySelectId);
        }
    }

    /**
     * 从数据库查询问答对数据
     */
    private List<KgQaPairD> queryQaPairsFromDatabase(GetQaPairsEvt evt, boolean onlySelectId) {
        List<KgQaPairD> qaPairList = kgQaPairDMapper.selectQaPairListByHighLevel(evt);
        
        if (onlySelectId && CollUtil.isNotEmpty(qaPairList)) {
            // 如果只需要ID，则只保留ID字段的简化对象
            return qaPairList.stream().map(qa -> {
                KgQaPairD simpleQa = new KgQaPairD();
                simpleQa.setKgQaPairId(qa.getKgQaPairId());
                return simpleQa;
            }).collect(Collectors.toList());
        }
        
        return qaPairList;
    }

    /**
     * 从Elasticsearch查询问答对数据
     */
    private List<KgQaPairD> queryQaPairsFromElasticsearch(GetQaPairsEvt evt, boolean onlySelectId) {
        try {
            GetBasesEvt basesEvt = new GetBasesEvt();
            BeanUtil.copyProperties(evt, basesEvt);
            esKnowSearchService.getCasesOnFullTextWithoutPage(basesEvt);
            
            // 获取ES查询结果但不分页
            PageInfo<KnowledgeBasesEntity> casesOnFullTextWithPage = esKnowSearchService.getCasesOnFullTextWithPage(basesEvt);
            
            List<KgQaPairD> resultList = casesOnFullTextWithPage.getList().stream()
                    .map(this::convertToList)
                    .collect(Collectors.toList());
            
            if (onlySelectId && CollUtil.isNotEmpty(resultList)) {
                // 如果只需要ID，则只保留ID字段的简化对象
                return resultList.stream().map(qa -> {
                    KgQaPairD simpleQa = new KgQaPairD();
                    simpleQa.setKgQaPairId(qa.getKgQaPairId());
                    return simpleQa;
                }).collect(Collectors.toList());
            }
            
            return resultList;
        } catch (Exception e) {
            log.error("ES查询问答对失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public QaPairVm getQaPairs(GetQaPairsEvt evt) {
        if (evt.getKgBasesSpaceCId() == null) {
            return null;
        }

        // 使用公共权限处理逻辑
        buildCommonQueryConditions(evt);

        // 根据知识空间ID获取关联的问答对ID列表
        List<Long> kgQaPairIds = new ArrayList<>();
        if (evt.getKgBasesSpaceCId() != null) {
            kgQaPairIds = getQaPairIdsBySpaceId(evt.getKgBasesSpaceCId());
        }

        // 如果没有找到问答对，直接返回空列表
        if (kgQaPairIds.isEmpty()) {
            QaPairVm qaPairVm = new QaPairVm();
            qaPairVm.setQaPairPageInfo(new PageInfo<>(new ArrayList<>()));
            qaPairVm.setAllMajorStatisticsList(new ArrayList<>());
            qaPairVm.setMajorTotal(0);
            return qaPairVm;
        }

        // 设置问答对ID列表
        evt.setKgQaPairIds(kgQaPairIds);

        // 查询问答对列表 - 使用默认分页参数
        int pageNo = evt.getPageNo(); // 默认第一页
        int pageSize = evt.getPageSize(); // 默认分页大小
        
        Page<Object> page = PageHelper.startPage(pageNo, pageSize);
        PageInfo<KgQaPairD> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setPageNum(pageNo);
        
        if (StrUtil.isBlank(evt.getFullText())) {
            pageInfo.setList(kgQaPairDMapper.selectQaPairListByHighLevel(evt));
            pageInfo.setTotal(page.getTotal());
        } else {
            GetBasesEvt basesEvt = new GetBasesEvt();
            BeanUtil.copyProperties(evt, basesEvt);
            esKnowSearchService.getCasesOnFullTextWithoutPage(basesEvt);
            PageInfo<KnowledgeBasesEntity> casesOnFullTextWithPage = esKnowSearchService.getCasesOnFullTextWithPage(basesEvt);
            pageInfo.setTotal(casesOnFullTextWithPage.getTotal());
            pageInfo.setList(casesOnFullTextWithPage.getList().stream().map(this::convertToList).collect(Collectors.toList()));
        }

        // 丰富区域和分类信息
        enrichRegionInfo(pageInfo.getList());

        // 构建返回结果
        QaPairVm qaPairVm = new QaPairVm();
        qaPairVm.setQaPairPageInfo(pageInfo);

        // 查询专业统计信息
        Integer majorTotal = kgQaPairDMapper.selectMajorStatistics(kgQaPairIds);
        List<MajorStatisticsVm> majorStatisticsList = kgQaPairDMapper.selectAllMajorStatistics(kgQaPairIds);

        qaPairVm.setAllMajorStatisticsList(majorStatisticsList);
        qaPairVm.setMajorTotal(majorTotal);

        return qaPairVm;
    }

    public KgQaPairD convertToList(KnowledgeBasesEntity knowledgeBasesEntity) {
        KgQaPairD kgQaPairD = new KgQaPairD();
        BeanUtil.copyProperties(knowledgeBasesEntity, kgQaPairD);
        kgQaPairD.setKgQaPairId(knowledgeBasesEntity.getKgKnowledgeBasesId());
        return kgQaPairD;
    }

    /**
     * 根据知识空间ID获取问答对ID列表
     *
     * @param kgBasesSpaceCId 知识空间ID
     * @return 问答对ID列表
     */
    private List<Long> getQaPairIdsBySpaceId(Long kgBasesSpaceCId) {
        if (kgBasesSpaceCId == null) {
            return new ArrayList<>();
        }
        
        // 使用关联表查询指定空间下的问答对ID列表
        QueryWrapper<KgBasesSpaceRelationC> relationQueryWrapper = new QueryWrapper<>();
        relationQueryWrapper.eq("kg_bases_space_c_id", kgBasesSpaceCId)
                .eq("base_type", KnowModelType.QA_PAIR.getType())
                .eq("is_deleted", false);
        
        List<KgBasesSpaceRelationC> relations = kgBasesSpaceRelationCMapper.selectList(relationQueryWrapper);
        
        // 提取问答对ID列表
        List<Long> qaPairIds = relations.stream()
                .map(KgBasesSpaceRelationC::getKgKnowledgeBasesId)
                .collect(Collectors.toList());
        
        if (qaPairIds.isEmpty()) {
            return qaPairIds;
        }
        
        // 过滤掉已删除的问答对
        LambdaQueryWrapper<KgQaPairD> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(KgQaPairD::getKgQaPairId, qaPairIds)
               .eq(KgQaPairD::getIsDeleted, "0");
        
        List<KgQaPairD> qaPairs = kgQaPairDMapper.selectList(wrapper);
        return qaPairs.stream()
                .map(KgQaPairD::getKgQaPairId)
                .collect(Collectors.toList());
    }

    /**
     * 处理审核规则
     *
     * @param evt      请求参数
     * @param username 用户名
     */
    private void handleAuditRules(GetQaPairsEvt evt, String username) {
        // TODO: 根据实际情况实现审核规则
        if (evt.getState() != null && ((evt.getWhetherAuditOrder() != null && evt.getState().contains(CaseStateEnum.PENDING_AUDIT.getStateValue()))
                || evt.getWhetherOnline() != null)) {
            KgAuditorAllocationRuleConfigD ruleConfig = auditorAllocationRuleConfigMapper.selectById(BigInteger.ONE);

            if (evt.getType() == null || (evt.getType() != null && !CommonConstant.YES.equals(evt.getType()))) {
                if (StringUtils.isNotBlank(username)) {
                    KgBasesAuditPersonDEvt personEvt = new KgBasesAuditPersonDEvt();
                    personEvt.setRealName(username);
                    List<KgBasesAuditPersonDVm> auditPersons = basesAuditPersonMapper.getBasesAuditPerson(personEvt);

                    if (CollUtil.isNotEmpty(auditPersons)) {
                        KgBasesAuditPersonDVm auditPerson = auditPersons.get(0);
                        //省级管理员不做限制
                        if (CommonConstant.NO_PASS.equals(auditPerson.getWhetherProvinceAdmin())) {
                            if (!CommonConstant.PASS.equals(auditPerson.getWhetherRegionAdmin())) {
                                //不是区域管理员，就需要通过分配规则过滤数据
                                switch (ruleConfig.getValue()) {
                                    case "1":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(auditPerson.getMajor().split(","))));
                                        break;
                                    case "2":
                                        evt.setStateRegion(auditPerson.getRegionList().stream().map(Long::valueOf).collect(Collectors.toList()));
                                        break;
                                    case "3":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(auditPerson.getMajor().split(","))));
                                        evt.setStateRegion(auditPerson.getRegionList().stream().map(Long::valueOf).collect(Collectors.toList()));
                                        break;
                                }
                            } else {
                                //是区域管理员，只需要过滤区域，不受专业限制
                                evt.setStateRegion(auditPerson.getRegionList().stream().map(Long::valueOf).collect(Collectors.toList()));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 丰富区域信息
     *
     * @param qaPairList 问答对列表
     */
    private void enrichRegionInfo(List<KgQaPairD> qaPairList) {
        if (CollectionUtil.isEmpty(qaPairList)) {
            return;
        }

        // 获取所有区域ID
        List<Long> regionIds = qaPairList.stream()
                .map(KgQaPairD::getRegion)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(regionIds)) {
            return;
        }

        // 查询区域信息
        List<QueryRegionListVm> regions = regionMapper.findRegionByIds(regionIds, regionConfig.getRegionSchema());
        Map<Long, String> regionMap = regions.stream()
                .collect(Collectors.toMap(
                        QueryRegionListVm::getRegionId,
                        QueryRegionListVm::getRegionName,
                        (v1, v2) -> v1
                ));

        // 设置区域名称
        qaPairList.forEach(qaPair -> {
            if (qaPair.getRegion() != null) {
                qaPair.setRegionName(regionMap.get(qaPair.getRegion()));
            }
        });
    }

    @Override
    public QaPairInfoVm getQaPairInfo(QaPairInfoEvt evt) {
        if (evt.getKgQaPairId() == null) {
            return null;
        }

        // 查询问答对详情
        KgQaPairD qaPair = kgQaPairDMapper.selectById(evt.getKgQaPairId());
        if (qaPair == null) {
            return null;
        }

        // 查询区域和分类信息
        List<KgQaPairD> qaPairList = new ArrayList<>();
        qaPairList.add(qaPair);
        enrichRegionInfo(qaPairList);

        // 构建返回结果
        QaPairInfoVm qaPairInfoVm = new QaPairInfoVm();
        qaPairInfoVm.setQaPair(qaPair);
        
        // 查询最新的审核评论记录
        KgQaPairAuditDimensionD latestAuditComment = getLatestAuditComment(evt.getKgQaPairId());
        qaPairInfoVm.setLatestAuditComment(latestAuditComment);

        return qaPairInfoVm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp addOrUpdateQaPair(KgQaPairD evt) {
        // 添加问答对
        if (evt.getKgQaPairId() == null) {
            // 校验必填字段
            ServiceResp validationResult = validateRequiredFields(evt);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 检查问题是否重复
            if (checkQaPairRepeat(evt.getQuestion())) {
                return ServiceResp.fail("问题已存在，请勿重复添加");
            }

            String username = PtSecurityUtils.getUsername();
            // 设置基本信息
            evt.setIsDeleted("0");
            evt.setCreatedUserName(username);
            evt.setCreatedTime(new Date());
            evt.setUpdatedUserName(username);
            evt.setUpdatedTime(new Date());
            evt.setSearchNumber(0L);
            evt.setClickCount(0L);
            evt.setTenantCode(tenantCode);
            evt.setAuditStatus(CommonConstant.STATUS_PASS);
            evt.setCategoryId(Objects.nonNull(evt.getCategoryId()) ? evt.getCategoryId() : FormatUtil.intParse(knowledgeBaseConfig.getQwd().getCategoryId()));
            evt.setState(CaseStateEnum.NEW.getStateValue());
            evt.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());
            
            // 设置上报相关字段的初始值
            if (evt.getReportStatus() == null) {
                evt.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
            }
            if (evt.getReportDescription() == null) {
                evt.setReportDescription(null);
            }
            if (evt.getReportTime() == null) {
                evt.setReportTime(null);
            }
            // 插入数据库
            kgQaPairDMapper.insert(evt);
            
            // 如果有关联的知识空间ID，创建关联关系
            if (evt.getKgBasesSpaceCId() != null) {
                createQaPairSpaceRelation(evt.getKgQaPairId(), evt.getKgBasesSpaceCId());
            }
            
            // 新增到ES
            saveQaPairToEs(evt);

            return ServiceResp.success("添加成功", evt.getKgQaPairId());
        }
        // 更新
        else {
            KgQaPairD original = kgQaPairDMapper.selectById(evt.getKgQaPairId());
            if (original == null) {
                return ServiceResp.fail("问答对不存在");
            }

            // 校验必填字段
            ServiceResp validationResult = validateRequiredFields(evt);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // 如果问题内容发生变化，需要检查重复
            if (!original.getQuestion().equals(evt.getQuestion()) && checkQaPairRepeat(evt.getQuestion())) {
                return ServiceResp.fail("问题已存在，请勿重复添加");
            }

            // 设置更新信息
            evt.setUpdatedUserName(PtSecurityUtils.getUsername());
            evt.setUpdatedTime(new Date());

            // 更新数据库
            kgQaPairDMapper.updateById(evt);
            
            // 更新到ES
            updateQaPairToEs(evt);

            return ServiceResp.success("更新成功", evt.getKgQaPairId());
        }
    }

    /**
     * 校验问答对必填字段
     *
     * @param qaPair 问答对实体
     * @return 校验结果
     */
    private ServiceResp validateRequiredFields(KgQaPairD qaPair) {
        if (StringUtils.isEmpty(qaPair.getInstitution())) {
            return ServiceResp.fail("组织机构不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getAuthor())) {
            return ServiceResp.fail("作者名称不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getMajor())) {
            return ServiceResp.fail("专业领域不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getApplicationScene())) {
            return ServiceResp.fail("应用场景不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getKnowledgeOrigin())) {
            return ServiceResp.fail("知识来源不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getFlowScene())) {
            return ServiceResp.fail("流程领域不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getPublicity())) {
            return ServiceResp.fail("公开范围不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getPeriodValidity())) {
            return ServiceResp.fail("有效期不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getLifeCycle())) {
            return ServiceResp.fail("生命周期不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getQuestionClassify())) {
            return ServiceResp.fail("问题分类不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getQuestion())) {
            return ServiceResp.fail("问题不能为空");
        }
        
        if (StringUtils.isEmpty(qaPair.getAnswer())) {
            return ServiceResp.fail("答案不能为空");
        }
        if (Objects.isNull(qaPair.getRegion())) {
            return ServiceResp.fail("区域不能为空");
        }

        if (Objects.isNull(qaPair.getKgBasesSpaceCId())) {
            ServiceResp.fail("知识空间不能为空");
        }
        
        return ServiceResp.success("校验成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp deleteQaPairs(QaPairIdsEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答对ID不能为空");
        }

        for (Long id : evt.getIds()) {
            // 查询问答对状态
            KgQaPairD qaPair = kgQaPairDMapper.selectById(id);
            if (qaPair == null) {
                continue;
            }
            
            // 根据状态决定是物理删除还是逻辑删除
            if (CaseStateEnum.NEW.getStateValue().equals(qaPair.getState())) {
                // 草稿状态的问答对进行物理删除
                kgQaPairDMapper.deleteById(id);
                
                // 删除关联关系记录
                deleteQaPairSpaceRelation(id, true);
            } else {
                // 其他状态的问答对进行逻辑删除
                KgQaPairD updateQaPair = new KgQaPairD();
                updateQaPair.setKgQaPairId(id);
                updateQaPair.setIsDeleted("1"); // 标记为已删除
                updateQaPair.setUpdatedTime(new Date());
                updateQaPair.setUpdatedUserName(PtSecurityUtils.getUsername());
                kgQaPairDMapper.updateById(updateQaPair);
                
                // 逻辑删除关联关系记录
                deleteQaPairSpaceRelation(id, false);
            }
            
            // 从ES中删除
            deleteQaPairFromEs(id);
        }

        return ServiceResp.success("删除成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp submitQaPairs(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答对ID不能为空");
        }

        // 更新数据库中的状态
        for (Long id : evt.getIds()) {
            KgQaPairD qaPair = new KgQaPairD();
            qaPair.setKgQaPairId(id);
            qaPair.setState(CaseStateEnum.PENDING_AUDIT.getStateValue()); // 待审核状态
            qaPair.setSubmitTime(new Date());
            qaPair.setUpdatedTime(new Date());
            qaPair.setUpdatedUserName(PtSecurityUtils.getUsername());
            kgQaPairDMapper.updateById(qaPair);
        }
        
        // 更新ES中的状态
        batchUpdateQaPairStateToEs(evt.getIds(), CaseStateEnum.PENDING_AUDIT.getStateValue());

        return ServiceResp.success("提交审核成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp approveQaPairs(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答对ID不能为空");
        }

        // 更新数据库中的状态
        Date auditTime = new Date();
        for (Long id : evt.getIds()) {
            KgQaPairD qaPair = new KgQaPairD();
            qaPair.setKgQaPairId(id);
            qaPair.setState(evt.getState()); // 待发布
            qaPair.setAuditTime(auditTime);
            qaPair.setReviewer(PtSecurityUtils.getUsername());
            qaPair.setReleaseTime(auditTime);
            qaPair.setUpdatedTime(auditTime);
            qaPair.setUpdatedUserName(PtSecurityUtils.getUsername());
            kgQaPairDMapper.updateById(qaPair);

            Integer commentPass = evt.getState().equals(CaseStateEnum.TO_BE_RELEASED.getStateValue()) ? CommonConstant.PASS : CommonConstant.NO_PASS;
            
            // 保存审核评论
            saveAuditComment(id, evt.getComment(), String.valueOf(commentPass), auditTime);
        }
        
        // 更新ES中的状态
        batchUpdateQaPairStateToEs(evt.getIds(), evt.getState());

        return ServiceResp.success("审核通过成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp rejectQaPairs(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答对ID不能为空");
        }

        // 更新数据库中的状态
        Date auditTime = new Date();
        for (Long id : evt.getIds()) {
            KgQaPairD qaPair = new KgQaPairD();
            qaPair.setKgQaPairId(id);
            qaPair.setState(CaseStateEnum.AUDIT_NOT_PASS.getStateValue()); // 已驳回状态
            qaPair.setAuditTime(auditTime);
            qaPair.setUpdatedTime(auditTime);
            qaPair.setUpdatedUserName(PtSecurityUtils.getUsername());
            kgQaPairDMapper.updateById(qaPair);
            
            // 保存审核评论
            saveAuditComment(id, evt.getComment(), String.valueOf(CommonConstant.NO_PASS), auditTime);
        }
        
        // 更新ES中的状态
        batchUpdateQaPairStateToEs(evt.getIds(), CaseStateEnum.AUDIT_NOT_PASS.getStateValue());

        return ServiceResp.success("审核驳回成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp offlineQaPairs(BatchUpdateQaStateEvt evt) {
        if (CollectionUtil.isEmpty(evt.getIds())) {
            return ServiceResp.fail("问答对ID不能为空");
        }

        String state = evt.getState();
        String operationMsg = "";

        // 根据状态判断是上线还是下线操作
        boolean isOnline = CaseStateEnum.PUBLISHED.getStateValue().equals(state);
        boolean isOffline = CaseStateEnum.OFF_LINE.getStateValue().equals(state);

        if (!isOnline && !isOffline) {
            return ServiceResp.fail("无效的状态值，只支持上线或下线操作");
        }

        Date now = new Date();

        // 更新数据库中的状态
        for (Long id : evt.getIds()) {
            // 获取当前问答对信息
            KgQaPairD currentQaPair = kgQaPairDMapper.selectById(id);
            if (currentQaPair == null) {
                continue;
            }

            KgQaPairD qaPair = new KgQaPairD();
            qaPair.setKgQaPairId(id);
            qaPair.setState(state);
            qaPair.setUpdatedTime(now);
            qaPair.setUpdatedUserName(PtSecurityUtils.getUsername());

            // 处理下线操作
            if (isOffline) {
                // 如果是首次下线，设置标记
                if (currentQaPair.getWhetherFirstOffline() == null || currentQaPair.getWhetherFirstOffline() != 1) {
                    qaPair.setWhetherFirstOffline(1);
                }
                operationMsg = "下线成功";
            } 
            // 处理上线操作
            else if (isOnline) {
                // 如果已经进行过首次下线，则设置为二次上线状态
                if (currentQaPair.getWhetherFirstOffline() != null && currentQaPair.getWhetherFirstOffline() == 1) {
                    qaPair.setState(CaseStateEnum.SECOND_ONLINE.getStateValue());
                }
                qaPair.setReleaseTime(now); // 上线时设置发布时间
                operationMsg = "上线成功";
            }

            kgQaPairDMapper.updateById(qaPair);
        }
        
        // 更新ES中的状态
        batchUpdateQaPairStateToEs(evt.getIds(), state);

        return ServiceResp.success(operationMsg);
    }


    /**
     * 获取单元格字符串值
     *
     * @param row         行
     * @param columnIndex 列索引
     * @return 字符串值
     */
    private String getCellStringValue(Row row, int columnIndex) {
        if (columnIndex < 0 || row.getCell(columnIndex) == null) {
            return "";
        }

        try {
            return row.getCell(columnIndex).getStringCellValue();
        } catch (Exception e) {
            try {
                return String.valueOf(row.getCell(columnIndex).getNumericCellValue());
            } catch (Exception ex) {
                return "";
            }
        }
    }

    @Override
    public boolean checkQaPairRepeat(String question) {
        if (StringUtils.isEmpty(question)) {
            return false;
        }

        Integer count = kgQaPairDMapper.checkQuestionRepeat(question);
        return count != null && count > 0;
    }

    /**
     * 从知识库导入问答对
     *
     * @param kgKnowledgeBasesId 知识库ID
     * @param filePath           文件路径
     * @param organization       组织机构
     * @param knowledgeOrigin    知识来源
     * @param lifeCycle          生命周期
     * @return 导入的问答对列表
     */
    public List<KgQaPairD> importQaPairsFromKnowledgeBase(Long kgKnowledgeBasesId, String filePath, String organization, String knowledgeOrigin, String lifeCycle, String author, Long region, Long kgBasesSpaceCId) {
        List<KgQaPairD> result = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        try {
            // 校验必填参数
            if (Objects.isNull(region)) {
                errorMessages.add("区域不能为空");
            }
            if (Objects.isNull(kgBasesSpaceCId)) {
                errorMessages.add("知识空间不能为空");
            }
            if (StringUtils.isEmpty(organization)) {
                errorMessages.add("组织机构不能为空");
            }
            if (StringUtils.isEmpty(knowledgeOrigin)) {
                errorMessages.add("知识来源不能为空");
            }
            if (StringUtils.isEmpty(lifeCycle)) {
                errorMessages.add("生命周期不能为空");
            }
            if (StringUtils.isEmpty(author)) {
                errorMessages.add("作者名称不能为空");
            }
            
            if (CollectionUtil.isNotEmpty(errorMessages)) {
                String errorMsg = String.join("，", errorMessages);
                log.error("导入问答对参数校验失败: {}", errorMsg);
                throw new KnowledgeBasesException(errorMsg);
            }

            // 打开Excel文件
            File excelFile = new File(filePath);
            if (!excelFile.exists()) {
                log.error("导入问答对Excel文件不存在: {}", filePath);
                return result;
            }

            // 查询字典值映射
            Map<String, Map<String, String>> dictMaps = new HashMap<>();
            dictMaps.put("periodValidity", getDictionaryMap("periodValidity")); // 有效期
            dictMaps.put("C3", getDictionaryMap("C3")); // 流程领域
            dictMaps.put("question_category", getDictionaryMap("question_category")); // 问题类别
            dictMaps.put("C2", getDictionaryMap("C2")); // 专业领域
            dictMaps.put("C12", getDictionaryMap("C12")); // 应用场景
            dictMaps.put("C13", getDictionaryMap("C13")); // 适用范围

            Workbook workbook = new XSSFWorkbook(Files.newInputStream(excelFile.toPath()));

            // 2. 识别问答对模板Sheet
            Sheet qaSheet = findSceneSheet(workbook);
            if (qaSheet == null) {
                log.error(" 未找到有效的问答对模板表（需包含'应用场景'列）");
                throw new RuntimeException("未找到有效的问答对模板表（需包含'应用场景'列）");
            }
            log.info(" 找到问答对模板表: {}", qaSheet.getSheetName());
            if (qaSheet == null) {
                workbook.close();
                return result;
            }

            // 获取表头行
            Row headerRow = qaSheet.getRow(0);
            if (headerRow == null) {
                workbook.close();
                return result;
            }

            // 解析表头
            Map<String, Integer> headerMap = new HashMap<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                String headerValue = headerRow.getCell(i) != null ? headerRow.getCell(i).getStringCellValue() : "";
                headerMap.put(headerValue, i);
            }
            
            // 检查必填列是否存在
            checkRequiredColumns(headerMap);

            // 获取最后一行的索引
            int lastRowNum = qaSheet.getLastRowNum();
            int successCount = 0;
            int failCount = 0;

            // 从第2行开始解析数据（跳过表头）
            for (int i = 1; i <= lastRowNum; i++) {
                Row dataRow = qaSheet.getRow(i);
                if (dataRow == null) {
                    continue;
                }

                try {
                    // 获取基本字段值
                    String question = getCellStringValue(dataRow, headerMap.getOrDefault("问题", -1));
                    String answer = getCellStringValue(dataRow, headerMap.getOrDefault("答案", -1));
                    String majorValue = getCellStringValue(dataRow, headerMap.getOrDefault("子专业", -1));
                    String appSceneValue = getCellStringValue(dataRow, headerMap.getOrDefault("应用子场景", -1));
                    String flowSceneValue = getCellStringValue(dataRow, headerMap.getOrDefault("流程领域", -1));
                    String scopeValue = getCellStringValue(dataRow, headerMap.getOrDefault("适用范围", -1));
                    String validityValue = getCellStringValue(dataRow, headerMap.getOrDefault("有效期", -1));
                    String questionTypeValue = getCellStringValue(dataRow, headerMap.getOrDefault("问题类别", -1));

                    // 校验必填字段
                    List<String> rowErrors = new ArrayList<>();
                    if (StringUtils.isEmpty(question)) rowErrors.add("问题");
                    if (StringUtils.isEmpty(answer)) rowErrors.add("答案");
                    if (StringUtils.isEmpty(majorValue)) rowErrors.add("子专业");
                    if (StringUtils.isEmpty(appSceneValue)) rowErrors.add("应用子场景");
                    if (StringUtils.isEmpty(flowSceneValue)) rowErrors.add("流程领域");
                    if (StringUtils.isEmpty(scopeValue)) rowErrors.add("适用范围");
                    if (StringUtils.isEmpty(validityValue)) rowErrors.add("有效期");
                    if (StringUtils.isEmpty(questionTypeValue)) rowErrors.add("问题类别");

                    if (CollectionUtil.isNotEmpty(rowErrors)) {
                        log.warn("第{}行数据缺少必填字段: {}, 已跳过", i, String.join(", ", rowErrors));
                        failCount++;
                        continue;
                    }

                    // 创建问答对对象
                    KgQaPairD qaPair = new KgQaPairD();

                    // 设置关联知识库ID
                    qaPair.setKgKnowledgeBasesId(kgKnowledgeBasesId);

                    // 设置组织机构（从参数传入）
                    qaPair.setInstitution(organization);

                    // 解析基本字段
                    qaPair.setAuthor(author);

                    // 从参数导入
                    qaPair.setRegion(region);

                    // 专业领域 - 需要转换字典值
                    qaPair.setMajor(convertDictValue(majorValue, dictMaps.get("C2")));

                    // 应用场景 - 需要转换字典值
                    qaPair.setApplicationScene(convertDictValue(appSceneValue, dictMaps.get("C12")));

                    // 知识来源（从参数传入）
                    qaPair.setKnowledgeOrigin(knowledgeOrigin);

                    // 流程领域 - 需要转换字典值
                    qaPair.setFlowScene(convertDictValue(flowSceneValue, dictMaps.get("C3")));

                    // 公开范围/适用范围 - 需要转换字典值
                    qaPair.setPublicity(convertDictValue(scopeValue, dictMaps.get("C13")));

                    // 有效期 - 需要转换字典值
                    qaPair.setPeriodValidity(convertDictValue(validityValue, dictMaps.get("periodValidity")));

                    // 生命周期（从参数传入）
                    qaPair.setLifeCycle(lifeCycle);

                    // 问题类别 - 需要转换字典值
                    qaPair.setQuestionClassify(convertDictValue(questionTypeValue, dictMaps.get("question_category")));

                    qaPair.setQuestion(question);
                    qaPair.setAnswer(answer);
                    qaPair.setAuditStatus(CommonConstant.STATUS_PASS);

                    // 设置基本信息
                    qaPair.setIsDeleted("0");
                    qaPair.setCreatedUserName(PtSecurityUtils.getUsername());
                    qaPair.setCreatedTime(new Date());
                    qaPair.setUpdatedUserName(PtSecurityUtils.getUsername());
                    qaPair.setUpdatedTime(new Date());
                    qaPair.setSearchNumber(0L);
                    qaPair.setClickCount(0L);
                    qaPair.setState(CaseStateEnum.PENDING_AUDIT.getStateValue()); // 导入时直接设置为待审核状态
                    qaPair.setTenantCode(tenantCode);
                    qaPair.setCategoryId(FormatUtil.intParse(knowledgeBaseConfig.getQwd().getCategoryId()));
                    
                    // 设置上报相关字段的初始值
                    qaPair.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
                    qaPair.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());
                    qaPair.setReportDescription(null);
                    qaPair.setReportTime(null);

                    // 检查问题是否重复
                    if (checkQaPairRepeat(question)) {
                        log.warn("第{}行问答对中存在重复问题，已跳过: {}", i, question);
                        failCount++;
                        continue;
                    }

                    // 插入数据库
                    kgQaPairDMapper.insert(qaPair);

                    // 如果有知识空间ID，创建关联关系
                    if (kgBasesSpaceCId != null) {
                        createQaPairSpaceRelation(qaPair.getKgQaPairId(), kgBasesSpaceCId);
                    }

                    // 添加到结果列表
                    result.add(qaPair);

                    // 保存到ES索引
                    saveQaPairToEs(qaPair);
                    
                    successCount++;
                } catch (Exception e) {
                    log.error("处理第{}行数据时发生错误: {}", i, e.getMessage());
                    failCount++;
                }
            }
            
            log.info("导入问答对完成，成功: {}条，失败: {}条", successCount, failCount);
            workbook.close();
        } catch (Exception e) {
            log.error("导入问答对失败", e);
        }

        return result;
    }

    /**
     * 检查Excel模板中是否包含所有必填列
     *
     * @param headerMap 表头映射
     */
    private void checkRequiredColumns(Map<String, Integer> headerMap) {
        List<String> missingColumns = new ArrayList<>();
        String[] requiredColumns = {"问题", "答案", "子专业", "应用子场景", "流程领域", "适用范围", "有效期", "问题类别"};
        
        for (String column : requiredColumns) {
            if (!headerMap.containsKey(column)) {
                missingColumns.add(column);
            }
        }
        
        if (!missingColumns.isEmpty()) {
            String errorMsg = "Excel模板缺少必填列: " + String.join(", ", missingColumns);
            log.error(errorMsg);
            throw new KnowledgeBasesException(errorMsg);
        }
    }

    /**
     * 查找问答对模板Sheet
     */
    private Sheet findSceneSheet(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet currentSheet = workbook.getSheetAt(i);
            Row firstRow = currentSheet.getRow(0);  // 表头行

            if (firstRow != null && containsColumn(firstRow, "应用场景") &&
                    !currentSheet.getSheetName().contains(" 问答对")) {
                return currentSheet;
            }
        }
        return null;
    }
    private boolean containsColumn(Row row, String columnName) {
        for (Cell cell : row) {
            if (cell.getStringCellValue().trim().equals(columnName)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取字典值映射
     *
     * @param codeType 字典类型
     * @return 字典值映射 (显示值 -> 代码值)
     */
    private Map<String, String> getDictionaryMap(String codeType) {
        try {
            // 查询字典表获取映射关系
            List<DictionaryEntity> dictList = dictionaryMapper.selectList(
                    new LambdaQueryWrapper<DictionaryEntity>().eq(DictionaryEntity::getCodeType, codeType));
            
            // 构建显示值到代码值的映射
            return dictList.stream()
                    .collect(Collectors.toMap(
                            DictionaryEntity::getCodeName,  // 显示值
                            entity -> FormatUtil.stringParse(entity.getBdpDirId()), // 代码值
                            (v1, v2) -> v1                  // 如果有重复的key，保留第一个
                    ));
        } catch (Exception e) {
            log.error("获取字典值映射失败, codeType={}", codeType, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 转换字典值（从显示值到代码值）
     *
     * @param displayValue 显示值
     * @param dictMap      字典映射
     * @return 代码值，如果没有找到映射则返回原值
     */
    private String convertDictValue(String displayValue, Map<String, String> dictMap) {
        if (StringUtils.isEmpty(displayValue) || dictMap == null || dictMap.isEmpty()) {
            return displayValue;
        }
        
        return dictMap.getOrDefault(displayValue, displayValue);
    }

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 新增问答对信息到ES
     * 
     * @param qaPair 问答对实体
     */
    private void saveQaPairToEs(KgQaPairD qaPair) {
        try {
            // 创建ES索引对象
            KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
            BeanUtil.copyProperties(qaPair, knowledgeBasesIdx);
            
            // 设置知识类型为问答对
            knowledgeBasesIdx.setKnowledgeType(knowledgeBaseConfig.getQwd().getCategoryId());
            knowledgeBasesIdx.setCategoryId(FormatUtil.longParse(knowledgeBaseConfig.getQwd().getCategoryId()));
            knowledgeBasesIdx.setBasesType(QA_BASE_TYPE);
            knowledgeBasesIdx.setKgKnowledgeBasesId(qaPair.getKgQaPairId());
            knowledgeBasesIdx.setKnowledgeName(qaPair.getQuestion());
            knowledgeBasesIdx.setSummary(qaPair.getAnswer());
            knowledgeBasesIdx.setUpdatedTime(new Date());
            knowledgeBasesIdx.setChangeTime(new Date());
            
            // 设置全文检索字段
            knowledgeBasesIdx.setFullText();
            
            // 直接保存到ES，使用前缀避免ID重复
            try {
                // 创建索引请求
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "qa_" + qaPair.getKgQaPairId();
                IndexQuery indexQuery = new IndexQueryBuilder()
                    .withId(documentId)
                    .withObject(knowledgeBasesIdx)
                    .build();
                // 使用index方法指定文档ID
                elasticsearchRestTemplate.index(indexQuery, indexCoordinates);
            } catch (Exception ex) {
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("问答对ES保存失败:{}", ex.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("新增问答对到ES失败", e);
        }
    }
    
    /**
     * 将问答对信息更新到ES
     * 
     * @param qaPair 问答对实体
     */
    private void updateQaPairToEs(KgQaPairD qaPair) {
        try {
            // 创建ES索引对象
            KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
            BeanUtil.copyProperties(qaPair, knowledgeBasesIdx);
            
            // 设置知识类型为问答对
            knowledgeBasesIdx.setKnowledgeType(knowledgeBaseConfig.getQwd().getCategoryId());
            knowledgeBasesIdx.setBasesType(QA_BASE_TYPE);
            knowledgeBasesIdx.setKgKnowledgeBasesId(qaPair.getKgQaPairId());
            knowledgeBasesIdx.setKnowledgeName(qaPair.getQuestion());
            knowledgeBasesIdx.setSummary(qaPair.getAnswer());
            knowledgeBasesIdx.setUpdatedTime(new Date());
            knowledgeBasesIdx.setChangeTime(new Date());
            
            // 设置全文检索字段
            knowledgeBasesIdx.setFullText();
            
            // 转换为JSON并更新到ES
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
            String jsonString;
       
            try {
                jsonString = mapper.writeValueAsString(knowledgeBasesIdx);
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "qa_" + qaPair.getKgQaPairId();
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(Document.parse(jsonString))
                        .build();
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            } catch (Exception ex) {
                // 如果更新失败，尝试保存
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("问答对ES更新失败:{}", ex.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("更新问答对到ES失败", e);
        }
    }
    
    /**
     * 从ES中删除问答对信息
     * 
     * @param qaPairId 问答对ID
     */
    private void deleteQaPairFromEs(Long qaPairId) {
        try {
            // 查询问答对状态
            KgQaPairD qaPair = kgQaPairDMapper.selectById(qaPairId);
            
            // 使用前缀区分不同来源的ID，避免重复
            String documentId = "qa_" + qaPairId;
            IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
            
            if (qaPair != null && CaseStateEnum.NEW.getStateValue().equals(qaPair.getState())) {
                // 草稿状态的问答对进行物理删除
                elasticsearchRestTemplate.delete(documentId, indexCoordinates);
            } else {
                // 其他状态的问答对进行逻辑删除，更新isDeleted字段
                Document document = Document.create();
                document.put("isDeleted", "1");
                document.put("updatedTime", LocalDateTime.now().format(formatter));
                
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(document)
                        .build();
                
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            }
        } catch (Exception ex) {
            if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                log.error("问答对ES删除失败:{}", ex.getMessage());
            }
        }
    }
    
    /**
     * 批量更新问答对状态到ES
     * 
     * @param ids 问答对ID列表
     * @param state 状态值
     */
    private void batchUpdateQaPairStateToEs(List<Long> ids, String state) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        
        for (Long id : ids) {
            try {
                Document document = Document.create();
                document.put("state", state);
                document.put("updatedTime", LocalDateTime.now().format(formatter));
                
                // 如果是上线或二次上线状态，设置发布时间
                if (CaseStateEnum.PUBLISHED.getStateValue().equals(state) ||
                    CaseStateEnum.SECOND_ONLINE.getStateValue().equals(state)) {
                    document.put("releaseTime", LocalDateTime.now().format(formatter));
                }
                
                // 如果是下线状态，设置是否首次下线
                if (CaseStateEnum.OFF_LINE.getStateValue().equals(state)) {
                    // 查询当前问答对状态
                    KgQaPairD qaPair = kgQaPairDMapper.selectById(id);
                    if (qaPair != null && (qaPair.getWhetherFirstOffline() == null || qaPair.getWhetherFirstOffline() != 1)) {
                        document.put("whetherFirstOffline", 1);
                    }
                }
                
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "qa_" + id;
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(document)
                        .build();
                
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);
            } catch (Exception ex) {
                if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                    log.error("操作ES失败,入参:{}", state, ex);
                }
            }
        }
    }
    
    /**
     * 创建问答对与知识空间的关联关系
     *
     * @param qaPairId 问答对ID
     * @param spaceCId 知识空间ID
     */
    private void createQaPairSpaceRelation(Long qaPairId, Long spaceCId) {
        try {
            // 先检查是否已存在关联关系
            QueryWrapper<KgBasesSpaceRelationC> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kg_knowledge_bases_id", qaPairId)
                    .eq("kg_bases_space_c_id", spaceCId)
                    .eq("base_type", KnowModelType.QA_PAIR.getType())
                    .eq("is_deleted", false);
            
            KgBasesSpaceRelationC existingRelation = kgBasesSpaceRelationCMapper.selectOne(queryWrapper);
            
            // 如果已存在关联关系，则不需要创建
            if (existingRelation != null) {
                return;
            }
            
            // 创建新的关联关系
            KgBasesSpaceRelationC relation = new KgBasesSpaceRelationC();
            relation.setKgKnowledgeBasesId(qaPairId);
            relation.setKgBasesSpaceCId(spaceCId);
            relation.setBaseType(KnowModelType.QA_PAIR.getType());
            relation.setIsDeleted(false);
            relation.setCreatedTime(new Date());
            relation.setCreatedUser(PtSecurityUtils.getUsername());
            relation.setUpdatedTime(new Date());
            relation.setUpdatedUser(PtSecurityUtils.getUsername());
            
            // 插入关联关系
            kgBasesSpaceRelationCMapper.insert(relation);
        } catch (Exception e) {
            log.error("创建问答对与知识空间关联关系失败", e);
        }
    }
    
    /**
     * 删除问答对与知识空间的关联关系
     *
     * @param qaPairId 问答对ID
     * @param isPhysicalDelete 是否物理删除
     */
    private void deleteQaPairSpaceRelation(Long qaPairId, boolean isPhysicalDelete) {
        try {
            // 查询问答对的所有关联关系
            QueryWrapper<KgBasesSpaceRelationC> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("kg_knowledge_bases_id", qaPairId)
                    .eq("base_type", KnowModelType.QA_PAIR.getType())
                    .eq("is_deleted", false);
            
            List<KgBasesSpaceRelationC> relations = kgBasesSpaceRelationCMapper.selectList(queryWrapper);
            
            if (relations.isEmpty()) {
                return;
            }
            
            // 根据删除类型处理关联关系
            if (isPhysicalDelete) {
                // 物理删除
                for (KgBasesSpaceRelationC relation : relations) {
                    kgBasesSpaceRelationCMapper.deleteById(relation.getId());
                }
            } else {
                // 逻辑删除
                for (KgBasesSpaceRelationC relation : relations) {
                    relation.setIsDeleted(true);
                    relation.setUpdatedTime(new Date());
                    relation.setUpdatedUser(PtSecurityUtils.getUsername());
                    kgBasesSpaceRelationCMapper.updateById(relation);
                }
            }
        } catch (Exception e) {
            log.error("删除问答对与知识空间关联关系失败", e);
        }
    }
    
    /**
     * 保存问答对审核评论
     *
     * @param qaPairId    问答对ID
     * @param comment     审核评论内容
     * @param whetherPass 是否通过审核(1:通过,0:不通过)
     * @param auditTime   审核时间
     */
    private void saveAuditComment(Long qaPairId, String comment, String whetherPass, Date auditTime) {
        if (qaPairId == null) {
            return;
        }
        
        try {
            // 创建审核评论记录
            KgQaPairAuditDimensionD auditDimension = new KgQaPairAuditDimensionD();
            auditDimension.setQaPairId(qaPairId);
            auditDimension.setComment(comment);
            auditDimension.setWhetherPass(whetherPass);
            
            // 查询当前最大排序号
            Integer maxOrderNum = kgQaPairAuditDimensionDMapper.selectMaxOrderNum(qaPairId);
            auditDimension.setOrderNum(maxOrderNum != null ? maxOrderNum + 1 : 1);
            
            // 设置创建和更新信息
            auditDimension.setCreatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setCreatedTime(auditTime);
            auditDimension.setUpdatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setUpdatedTime(auditTime);
            
            // 插入审核评论记录
            kgQaPairAuditDimensionDMapper.insert(auditDimension);
        } catch (Exception e) {
            log.error("保存问答对审核评论失败, qaPairId={}", qaPairId, e);
        }
    }
    
    /**
     * 获取问答对最新的审核评论记录
     *
     * @param qaPairId 问答对ID
     * @return 最新的审核评论记录
     */
    private KgQaPairAuditDimensionD getLatestAuditComment(Long qaPairId) {
        if (qaPairId == null) {
            return null;
        }
        
        try {
            // 查询最新的审核评论记录
            QueryWrapper<KgQaPairAuditDimensionD> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("qa_pair_id", qaPairId)
                    .orderByDesc("created_time")
                    .last("LIMIT 1");

            return kgQaPairAuditDimensionDMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("获取问答对最新审核评论记录失败, qaPairId={}", qaPairId, e);
            return null;
        }
    }

    @Override
    public void exportQaPairsToExcel(ExportQaPairsEvt evt, HttpServletResponse response) {
        log.info("开始导出问答对，参数：{}", evt);

        try {
            // 获取要导出的问答对数据
            List<KgQaPairD> qaPairList = getQaPairsForExport(evt);

            if (CollectionUtil.isEmpty(qaPairList)) {
                log.warn("没有找到要导出的问答对数据");
                throw new KnowledgeBasesException("没有找到要导出的问答对数据");
            }

            // 转换数据并生成Excel
            generateExcelFile(qaPairList, evt.getFileName(), response);

            batchUpdateQaPairReportStatus(qaPairList.stream().map(KgQaPairD::getKgQaPairId).collect(Collectors.toList())
                    , null, ExportStatusEnum.EXPORTED.getCode(), "");

            log.info("问答对导出完成，共导出{}条数据", qaPairList.size());
        } catch (Exception e) {
            log.error("导出问答对失败", e);
            throw new KnowledgeBasesException("导出问答对失败：" + e.getMessage());
        }
    }

    /**
     * 根据导出条件获取问答对数据
     */
    private List<KgQaPairD> getQaPairsForExport(ExportQaPairsEvt evt) {
        if (ExportQaPairsEvt.EXPORT_TYPE_SELECTED == evt.getExportType()) {
            // 选择导出：根据ID列表查询
            if (CollUtil.isEmpty(evt.getQaPairIds())) {
                throw new KnowledgeBasesException("选择导出时问答对ID列表不能为空");
            }

            LambdaQueryWrapper<KgQaPairD> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(KgQaPairD::getKgQaPairId, evt.getQaPairIds())
                   .eq(KgQaPairD::getIsDeleted, "0")
                   .orderByDesc(KgQaPairD::getCreatedTime);

            return kgQaPairDMapper.selectList(wrapper);

        } else if (ExportQaPairsEvt.EXPORT_TYPE_ALL == evt.getExportType()) {
            // 全选导出：使用与getQaPairs方法相同的查询逻辑
            if (evt.getQueryCondition() == null) {
                throw new KnowledgeBasesException("全选导出时查询条件不能为空");
            }

            GetQaPairsEvt queryEvt = evt.getQueryCondition();
            return queryQaPairsWithConditions(queryEvt, false);
        } else {
            throw new KnowledgeBasesException("不支持的导出类型：" + evt.getExportType());
        }
    }

    /**
     * 生成Excel文件
     */
    private void generateExcelFile(List<KgQaPairD> qaPairList, String fileName, HttpServletResponse response) throws IOException {
        Workbook workbook = null;
        try {
            // 读取模板文件
            ClassPathResource templateResource = new ClassPathResource("excel/template/问答对导出模板.xlsx");
            if (!templateResource.exists()) {
                throw new KnowledgeBasesException("问答对导出模板文件不存在");
            }

            // 打开模板文件
            workbook = new XSSFWorkbook(templateResource.getInputStream());

            // 获取第二个sheet页（索引为1）
            Sheet dataSheet = null;
            if (workbook.getNumberOfSheets() >= 2) {
                dataSheet = workbook.getSheetAt(1);
            } else {
                throw new KnowledgeBasesException("模板文件中没有找到第二个sheet页");
            }

            // 转换数据
            List<String[]> dataList = convertQaPairsToExcelData(qaPairList);

            // 从第二行开始写入数据（索引为1，因为第一行是表头）
            int rowIndex = 1;
            for (String[] rowData : dataList) {
                Row row = dataSheet.createRow(rowIndex++);
                for (int i = 0; i < rowData.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(rowData[i] != null ? rowData[i] : "");
                }
            }

            // 生成带时间戳的文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String finalFileName = (StringUtils.isNotBlank(fileName) ? fileName : "问答对导出数据") + "_" + timestamp + ".xlsx";

            // 使用浏览器下载
            ExcelUtil.setBrowser(response, null, finalFileName);

            // 写入响应流
            workbook.write(response.getOutputStream());

        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            throw new IOException("生成Excel文件失败：" + e.getMessage());
        } finally {
            // 确保workbook资源被正确关闭
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook失败", e);
                }
            }
        }
    }

    /**
     * 将问答对数据转换为Excel数据格式
     */
    private List<String[]> convertQaPairsToExcelData(List<KgQaPairD> qaPairList) {
        List<String[]> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (CollectionUtil.isEmpty(qaPairList)) {
            return dataList;
        }

        // 批量获取所有需要的字典数据，一次性查询提升性能
        Map<String, Map<String, String>> allDictionaryMaps = initAllQaPairDictionaryMaps();

        for (int i = 0; i < qaPairList.size(); i++) {
            KgQaPairD qaPair = qaPairList.get(i);
            String[] rowData = new String[13];

            // 序号
            rowData[0] = String.valueOf(i + 1);

            // 应用场景 - 获取父字典名称，多选时取第一个
            rowData[1] = getFirstParentDictName(qaPair.getApplicationScene(), allDictionaryMaps.get("C12_PARENT"));

            // 应用子场景 - 获取当前字典名称，多选时取第一个
            rowData[2] = getFirstDictName(qaPair.getApplicationScene(), allDictionaryMaps.get("C12"));

            // 问题类别 - 字典值转换
            rowData[3] = getFirstDictName(qaPair.getQuestionClassify(), allDictionaryMaps.get("question_category"));

            // 专业领域 - 获取父字典名称，多选时取第一个
            rowData[4] = getFirstParentDictName(qaPair.getMajor(), allDictionaryMaps.get("C2_PARENT"));

            // 子专业 - 获取当前字典名称，多选时取第一个
            rowData[5] = getFirstDictName(qaPair.getMajor(), allDictionaryMaps.get("C2"));

            // 问题ID
            rowData[6] = String.valueOf(qaPair.getKgQaPairId());

            // 问答轮数 - 暂时设为1，根据实际需求调整
            rowData[7] = "1";

            // 问题
            rowData[8] = StringUtils.isNotBlank(qaPair.getQuestion()) ? qaPair.getQuestion() : "";

            // 答案
            rowData[9] = StringUtils.isNotBlank(qaPair.getAnswer()) ? qaPair.getAnswer() : "";

            // 适用范围 - 多选时取第一个
            rowData[10] = getFirstDictName(qaPair.getPublicity(), allDictionaryMaps.get("C13"));

            // 流程领域 - 多选时取第一个
            rowData[11] = getFirstDictName(qaPair.getFlowScene(), allDictionaryMaps.get("C3"));

            // 有效期 - 字典值转换
            rowData[12] = getFirstDictName(qaPair.getPeriodValidity(), allDictionaryMaps.get("periodValidity"));

            dataList.add(rowData);
        }

        return dataList;
    }

    /**
     * 初始化问答对所有字典映射，一次性批量查询所有字典数据
     */
    private Map<String, Map<String, String>> initAllQaPairDictionaryMaps() {
        Map<String, Map<String, String>> allMaps = new HashMap<>();
        
        // 定义需要查询的字典类型
        List<String> dictTypes = Arrays.asList("C12", "C2", "C13", "C3", "periodValidity", "question_category");
        
        // 一次性查询所有字典数据
        List<DictionaryEntity> allDictList = dictionaryMapper.findByCodeTypeIn(dictTypes);
        
        if (CollectionUtil.isNotEmpty(allDictList)) {
            // 按字典类型分组
            Map<String, List<DictionaryEntity>> dictGroups = allDictList.stream()
                    .collect(Collectors.groupingBy(DictionaryEntity::getCodeType));
            
            // 处理每个字典类型
            for (Map.Entry<String, List<DictionaryEntity>> entry : dictGroups.entrySet()) {
                String dictType = entry.getKey();
                List<DictionaryEntity> dictList = entry.getValue();
                
                // 构建ID到名称的映射
                Map<String, String> idToNameMap = new HashMap<>();
                Map<String, String> idToParentNameMap = new HashMap<>();
                
                // 对于需要父子关系的字典类型，构建父字典映射
                if ("C12".equals(dictType) || "C2".equals(dictType)) {
                    // 先构建临时的Long类型ID到名称映射，用于查找父字典
                    Map<Long, String> tempIdToNameMap = new HashMap<>();
                    for (DictionaryEntity dict : dictList) {
                        tempIdToNameMap.put(dict.getBdpDirId(), dict.getCodeName());
                    }
                    
                    // 构建最终的字符串ID映射
                    for (DictionaryEntity dict : dictList) {
                        String dictId = String.valueOf(dict.getBdpDirId());
                        String dictName = dict.getCodeName();
                        
                        idToNameMap.put(dictId, dictName);
                        
                        // 如果有父ID，查找父字典名称
                        if (dict.getParentId() != null) {
                            String parentName = tempIdToNameMap.get(dict.getParentId());
                            if (StringUtils.isNotBlank(parentName)) {
                                idToParentNameMap.put(dictId, parentName);
                            }
                        }
                    }
                    
                    allMaps.put(dictType + "_PARENT", idToParentNameMap);
                } else {
                    // 对于不需要父子关系的字典类型，只构建普通映射
                    for (DictionaryEntity dict : dictList) {
                        String dictId = String.valueOf(dict.getBdpDirId());
                        String dictName = dict.getCodeName();
                        idToNameMap.put(dictId, dictName);
                    }
                }
                
                allMaps.put(dictType, idToNameMap);
            }
        }
        
        return allMaps;
    }

    /**
     * 获取第一个字典名称（处理多选情况）
     */
    private String getFirstDictName(String dictIds, Map<String, String> dictMap) {
        if (StringUtils.isBlank(dictIds) || dictMap == null) {
            return "";
        }
        
        // 处理多选情况，取第一个
        String firstId = dictIds.contains(",") ? dictIds.split(",")[0].trim() : dictIds.trim();
        return dictMap.getOrDefault(firstId, "");
    }

    /**
     * 获取第一个父字典名称（处理多选情况）
     */
    private String getFirstParentDictName(String dictIds, Map<String, String> parentDictMap) {
        if (StringUtils.isBlank(dictIds) || parentDictMap == null) {
            return "";
        }
        
        // 处理多选情况，取第一个
        String firstId = dictIds.contains(",") ? dictIds.split(",")[0].trim() : dictIds.trim();
        return parentDictMap.getOrDefault(firstId, "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp batchUpdateReportStatus(BatchUpdateReportStatusEvt evt) {
        log.info("批量修改问答对上报状态，参数：{}", evt);

        try {
            // 参数校验
            if (evt.getOperationType() == null) {
                return ServiceResp.fail("操作类型不能为空");
            }

            if (StringUtils.isBlank(evt.getReportStatus())) {
                return ServiceResp.fail("上报状态不能为空");
            }

            // 验证上报状态值是否有效
            if (!isValidReportStatus(evt.getReportStatus())) {
                return ServiceResp.fail("无效的上报状态值");
            }

            // 获取要修改的问答对ID列表
            List<Long> qaPairIds = getQaPairIdsForUpdate(evt);

            if (CollectionUtil.isEmpty(qaPairIds)) {
                return ServiceResp.fail("没有找到要修改的问答对数据");
            }

            // 批量更新上报状态
            int updateCount = batchUpdateQaPairReportStatus(qaPairIds, evt.getReportStatus(), evt.getExportStatus(), evt.getReportDescription());

            log.info("批量修改问答对上报状态完成，共修改{}条数据", updateCount);
            return ServiceResp.success("批量修改成功，共修改" + updateCount + "条数据");

        } catch (Exception e) {
            log.error("批量修改问答对上报状态失败", e);
            return ServiceResp.fail("批量修改失败：" + e.getMessage());
        }
    }

    /**
     * 根据操作类型获取要修改的问答对ID列表
     */
    private List<Long> getQaPairIdsForUpdate(BatchUpdateReportStatusEvt evt) {
        if (BatchUpdateReportStatusEvt.OPERATION_TYPE_SELECTED == evt.getOperationType()) {
            // 选择修改：直接使用传入的ID列表
            if (CollUtil.isEmpty(evt.getQaPairIds())) {
                throw new KnowledgeBasesException("选择修改时问答对ID列表不能为空");
            }
            return evt.getQaPairIds();

        } else if (BatchUpdateReportStatusEvt.OPERATION_TYPE_ALL == evt.getOperationType()) {
            // 全选修改：使用与getQaPairs方法相同的查询逻辑
            if (evt.getQueryCondition() == null) {
                throw new KnowledgeBasesException("全选修改时查询条件不能为空");
            }

            GetQaPairsEvt queryEvt = evt.getQueryCondition();
            List<KgQaPairD> qaPairList = queryQaPairsWithConditions(queryEvt, true);
            return qaPairList.stream()
                    .map(KgQaPairD::getKgQaPairId)
                    .collect(Collectors.toList());
        } else {
            throw new KnowledgeBasesException("不支持的操作类型：" + evt.getOperationType());
        }
    }

    /**
     * 验证上报状态值是否有效
     */
    private boolean isValidReportStatus(String reportStatus) {
        return ReportStatusEnum.NOT_REPORT.getCode().equals(reportStatus) ||
               ReportStatusEnum.REPORTED.getCode().equals(reportStatus) ||
               ReportStatusEnum.REPORT_FAILED.getCode().equals(reportStatus);
    }

    /**
     * 批量更新问答对上报状态
     */
    private int batchUpdateQaPairReportStatus(List<Long> qaPairIds, String reportStatus, String exportStatus, String reportDescription) {
        if (CollectionUtil.isEmpty(qaPairIds)) {
            return 0;
        }

        Date currentTime = new Date();
        String currentUser = PtSecurityUtils.getUsername();

        // 使用MyBatis-Plus的批量更新
        LambdaUpdateWrapper<KgQaPairD> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(KgQaPairD::getKgQaPairId, qaPairIds)
                    .eq(KgQaPairD::getIsDeleted, "0")
                    .set(StringUtils.isNotBlank(reportStatus), KgQaPairD::getReportStatus, reportStatus)
                    .set(StringUtils.isNotBlank(exportStatus), KgQaPairD::getExportStatus, exportStatus)
                    .set(KgQaPairD::getReportDescription, reportDescription)
                    .set(KgQaPairD::getReportTime, currentTime)
                    .set(KgQaPairD::getUpdatedUserName, currentUser)
                    .set(KgQaPairD::getUpdatedTime, currentTime);

        return kgQaPairDMapper.update(null, updateWrapper);
    }
}