package com.ffcs.oss.kg.system.service.knowledgeBases.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.FormatUtil;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeBasesException;
import com.ffcs.oss.kg.common.core.exception.KnowledgeGraphException;
import com.ffcs.oss.kg.common.core.mvc.PageInfo;
import com.ffcs.oss.kg.common.core.util.JsonUtil;
import com.ffcs.oss.kg.data.converter.graph.DtPermissionConverter;
import com.ffcs.oss.kg.data.enums.CaseStateEnum;
import com.ffcs.oss.kg.data.enums.ExportStatusEnum;
import com.ffcs.oss.kg.data.enums.KnowModelType;
import com.ffcs.oss.kg.data.enums.ReportStatusEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.AiCallTypeEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.StatusMappingUtil;
import com.ffcs.oss.kg.data.enums.knowledgeBases.ConfigTypeEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.DataSourceEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.ProcessStatusEnum;
import com.ffcs.oss.kg.data.enums.knowledgeBases.RetrievalMethodEnum;
import com.ffcs.oss.kg.data.es.KnowledgeBasesIdx;
import com.ffcs.oss.kg.data.model.dto.knowledgeBases.*;
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.KgBasesSpaceEvt;
import com.ffcs.oss.kg.data.model.ragflow.RagFlowDatasetCreateRequest;
import com.ffcs.oss.kg.data.model.ragflow.RagFlowDatasetUpdateRequest;
import com.ffcs.oss.kg.data.model.ragflow.RagFlowParserConfigBuilder;
import com.ffcs.oss.kg.data.model.vo.knowledgeBases.*;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.*;
import com.ffcs.oss.kg.data.rd.mapper.KgQaPairDMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.*;
import com.ffcs.oss.kg.system.client.RagFlowClient;
import com.ffcs.oss.kg.system.config.KnowledgeBaseConfig;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.service.knowledgeBases.AiCallLogService;
import com.ffcs.oss.kg.system.service.knowledgeBases.KnowledgeSpaceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexedObjectInformation;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识空间服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class KnowledgeSpaceServiceImpl implements KnowledgeSpaceService {

    private final KgBasesSpaceCMapper spaceMapper;
    private final RagFlowClient ragFlowClient;
    private final KgBasesSpaceConfigCMapper spaceConfigMapper;
    private final KgBasesSpaceRelationCMapper spaceRelationMapper;
    private final KgBasesDocumentProcessInfoCMapper documentProcessMapper;
    private final KgBasesDocumentSliceDMapper documentSliceMapper;
    private final KgBasesRecallTestDMapper recallTestMapper;
    private final KgBasesUserRecallConfigCMapper userRecallConfigMapper;
    private final KgBasesPdfParserConfigCMapper pdfParserConfigMapper;
    private final KgBasesPreprocessConfigCMapper preprocessConfigMapper;
    private final KgBasesSliceConfigCMapper sliceConfigMapper;
    private final KgBasesEmbeddingConfigCMapper embeddingConfigMapper;
    private final KgBasesRetrievalConfigCMapper retrievalConfigMapper;
    private final KgBasesRerankConfigCMapper rerankConfigMapper;
    private final KgBasesParamSettingsCMapper paramSettingsMapper;
    private final ModelKnowledgeBasesDMapper knowledgeBasesMapper;
    private final ObjectMapper objectMapper;
    private final KgBasesRecallTestHistoryDMapper recallTestHistoryMapper;
    private final KgBasesRecallTestResultDMapper recallTestResultMapper;
    private final KgQaPairDMapper qaPairMapper;
    private final ProfessionalTermMapper professionalTermMapper;
    private final KnowledgeBaseConfig knowledgeBaseConfig;
    private final ElasticsearchRestTemplate elasticsearchRestTemplate;
    private final DtPermissionConverter dtPermissionConverter;
    private final AiCallLogService aiCallLogService;

    @Override
    public KgBasesSpaceBaseInfoVO getBasesSpaceBaseInfo(KgBasesSpaceBaseInfoQueryDTO queryDTO) {
        log.info("获取知识空间基础信息，参数：{}", queryDTO);
        
        // 查询知识空间
        KgBasesSpaceC spaceEntity = spaceMapper.selectById(queryDTO.getSpaceId());
        if (spaceEntity == null) {
            throw new KnowledgeBasesException("知识空间不存在");
        }
        
        // 转换为VO
        KgBasesSpaceBaseInfoVO baseInfoVO = new KgBasesSpaceBaseInfoVO();
        BeanUtils.copyProperties(spaceEntity, baseInfoVO);
        
        log.info("查询知识空间基础信息成功，空间ID：{}", queryDTO.getSpaceId());
        return baseInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBasesSpace(KgBasesSpaceEvt evt) {
        log.info("创建知识空间，参数：{}", evt);
        
        try {
            // 校验知识空间
            validateBasesSpace(evt.getBasesSpaceName(), evt.getBasesSpaceCode());
            
            // 创建知识空间实体
            KgBasesSpaceC entity = buildSpaceEntity(evt, true);
            spaceMapper.insert(entity);
            
            // 更新空间配置 todo 新建默认不创建空间
            //updateSpaceConfigInternal(entity, evt);
            
            log.info("创建知识空间成功，ID：{}", entity.getKgBasesSpaceCId());
            return entity.getKgBasesSpaceCId();
            
        } catch (KnowledgeGraphException e) {
            log.error("创建知识空间业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建知识空间系统异常", e);
            throw new KnowledgeGraphException("创建知识空间失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateBasesSpace(KgBasesSpaceEvt evt) {
        log.info("更新知识空间，参数：{}", evt);
        
        try {
            // 查询并校验知识空间是否存在
            KgBasesSpaceC entity = validateSpaceExists(evt.getKgBasesSpaceCId());
            
            // 校验名称是否重复（排除自身）
            validateSpaceNameNotDuplicate(evt.getBasesSpaceName(), entity.getBasesSpaceName());
            
            // 更新实体属性
            updateSpaceEntityProperties(entity, evt);
            spaceMapper.updateById(entity);
            
            // 更新空间配置
            updateSpaceConfigInternal(entity, evt);
            
            log.info("更新知识空间成功，ID：{}", entity.getKgBasesSpaceCId());
            return entity.getKgBasesSpaceCId();
            
        } catch (KnowledgeGraphException e) {
            log.error("更新知识空间业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新知识空间系统异常", e);
            throw new KnowledgeGraphException("更新知识空间失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteBasesSpace(List<Long> ids) {
        log.info("删除知识空间，IDs：{}", ids);
        
        try {
            if (CollectionUtils.isEmpty(ids)) {
                throw new KnowledgeGraphException("删除ID列表不能为空");
            }
            
            // 批量校验知识空间存在性和关联关系
            Map<Long, KgBasesSpaceC> spaceMap = batchValidateSpacesForDeletion(ids);
            
            // 批量删除RagFlow空间
            batchDeleteRagFlowSpaces(spaceMap.values());
            
            // 批量逻辑删除知识空间
            batchLogicalDeleteSpaces(ids);
            
            log.info("删除知识空间成功，数量：{}", ids.size());
            return 1L;
            
        } catch (KnowledgeGraphException e) {
            log.error("删除知识空间业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("删除知识空间系统异常", e);
            throw new KnowledgeGraphException("删除知识空间失败：" + e.getMessage());
        }
    }

    /**
     * 构建知识空间实体
     */
    private KgBasesSpaceC buildSpaceEntity(KgBasesSpaceEvt evt, boolean isCreate) {
        KgBasesSpaceC entity = new KgBasesSpaceC();
        BeanUtils.copyProperties(evt, entity);
        
        String currentUser = PtSecurityUtils.getUsername();
        LocalDateTime now = LocalDateTime.now();
        
        if (isCreate) {
            entity.setCreatedUser(currentUser);
            entity.setCreatedTime(now);
            entity.setIsDeleted(false);
        }
        
        entity.setUpdatedUser(currentUser);
        entity.setUpdatedTime(now);
        
        return entity;
    }

    /**
     * 更新知识空间实体属性
     */
    private void updateSpaceEntityProperties(KgBasesSpaceC entity, KgBasesSpaceEvt evt) {
        BeanUtils.copyProperties(evt, entity, "kgBasesSpaceCId", "createdUser", "createdTime", "isDeleted");
        
        entity.setUpdatedUser(PtSecurityUtils.getUsername());
        entity.setUpdatedTime(LocalDateTime.now());
    }

    /**
     * 更新空间配置的内部方法
     */
    private void updateSpaceConfigInternal(KgBasesSpaceC entity, KgBasesSpaceEvt evt) {
        KgBasesRelConfigDTO config = new KgBasesRelConfigDTO();
        BeanUtils.copyProperties(evt, config);
        config.setRelationId(entity.getKgBasesSpaceCId());
        config.setConfigType(ConfigTypeEnum.SPACE.getCode());
        config.setSpaceId(entity.getKgBasesSpaceCId());
        
        this.updateSpaceConfig(config);
    }

    /**
     * 校验知识空间存在性
     */
    private KgBasesSpaceC validateSpaceExists(Long spaceId) {
        KgBasesSpaceC entity = spaceMapper.selectById(spaceId);
        if (entity == null || entity.getIsDeleted()) {
            throw new KnowledgeGraphException("知识空间不存在，ID：" + spaceId);
        }
        return entity;
    }

    /**
     * 校验知识空间名称不重复（排除自身）
     */
    private void validateSpaceNameNotDuplicate(String newName, String currentName) {
        if (!Objects.equals(currentName, newName) && spaceMapper.existsByName(newName)) {
            throw new KnowledgeGraphException("知识空间名称已存在：" + newName);
        }
    }

    /**
     * 批量校验知识空间删除条件
     * 优化：一次查询获取所有数据，避免N+1问题
     */
    private Map<Long, KgBasesSpaceC> batchValidateSpacesForDeletion(List<Long> ids) {
        // 批量查询知识空间
        List<KgBasesSpaceC> spaces = spaceMapper.selectByIds(ids);
        Map<Long, KgBasesSpaceC> spaceMap = spaces.stream()
                .collect(Collectors.toMap(KgBasesSpaceC::getKgBasesSpaceCId, space -> space));
        
        // 校验每个空间的存在性和关联关系
        for (Long id : ids) {
            KgBasesSpaceC space = spaceMap.get(id);
            if (space == null || space.getIsDeleted()) {
                throw new KnowledgeGraphException("知识空间不存在，ID：" + id);
            }
            
            // 校验关联关系（单个查询，兼容现有mapper方法）
            if (spaceMapper.existsRelation(id) != 0) {
                throw new KnowledgeGraphException(
                    String.format("知识空间[%s]存在关联的知识文档，无法删除", space.getBasesSpaceName()));
            }
        }
        
        return spaceMap;
    }

    /**
     * 批量删除RagFlow空间
     */
    private void batchDeleteRagFlowSpaces(Collection<KgBasesSpaceC> spaces) {
        if (CollectionUtils.isEmpty(spaces)) {
            return;
        }
        
        List<String> ragFlowIds = spaces.stream()
                .map(KgBasesSpaceC::getRagFlowId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        if (!ragFlowIds.isEmpty()) {
            try {
                DatasetCreateRequest request = new DatasetCreateRequest();
                request.setIds(ragFlowIds);
                ragFlowClient.delRagFlowSpace(request);
                log.info("批量删除RagFlow空间成功，数量：{}", ragFlowIds.size());
            } catch (Exception e) {
                log.error("批量删除RagFlow空间失败", e);
                // 不抛出异常，继续执行本地删除
            }
        }
    }

    /**
     * 批量逻辑删除知识空间
     */
    private void batchLogicalDeleteSpaces(List<Long> ids) {
        String currentUser = PtSecurityUtils.getUsername();
        LocalDateTime now = LocalDateTime.now();
        
        // 使用批量更新提升性能
        LambdaUpdateWrapper<KgBasesSpaceC> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(KgBasesSpaceC::getKgBasesSpaceCId, ids)
                    .set(KgBasesSpaceC::getIsDeleted, true)
                    .set(KgBasesSpaceC::getUpdatedUser, currentUser)
                    .set(KgBasesSpaceC::getUpdatedTime, now);
        
        int updateCount = spaceMapper.update(null, updateWrapper);
        log.info("批量逻辑删除知识空间完成，期望：{}，实际：{}", ids.size(), updateCount);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSpace(KgBasesRelConfigDTO configDTO) {
        log.info("创建知识空间配置，参数：{}", configDTO);
        
        try {
            // 1. 创建知识空间配置
            return updateSpaceConfig(configDTO);
        } catch (Exception e) {
            log.error("创建知识空间失败", e);
            throw new KnowledgeBasesException("创建知识空间失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateSpaceConfig(KgBasesRelConfigDTO configDTO) {
        log.info("更新知识空间配置，参数：{}", configDTO);

        try {
            // 根据配置类型分发处理
            if (ConfigTypeEnum.SPACE.getCode().equals(configDTO.getConfigType())) {
                return handleSpaceConfig(configDTO);
            } else if (ConfigTypeEnum.DOCUMENT.getCode().equals(configDTO.getConfigType())) {
                return handleDocumentConfig(configDTO);
            } else {
                throw new KnowledgeBasesException("不支持的配置类型：" + configDTO.getConfigType());
            }
            
        } catch (KnowledgeBasesException e) {
            log.error("更新配置业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新配置系统异常", e);
            throw new KnowledgeBasesException("更新配置失败：" + e.getMessage());
        }
    }

    /**
     * 处理知识空间级配置（需要同步到RagFlow）
     */
    private Long handleSpaceConfig(KgBasesRelConfigDTO configDTO) {
        // 1. 校验知识空间存在性
        KgBasesSpaceC spaceEntity = validateSpaceExists(configDTO.getSpaceId());
        
        // 2. 查询或创建配置实体
        KgBasesSpaceConfigC configEntity = findOrCreateConfig(configDTO);
        
        // 3. 更新配置属性
        updateConfigEntity(configEntity, configDTO);
        
        // 4. 保存配置并同步到RagFlow
        if (configEntity.getId() == null) {
            spaceConfigMapper.insert(configEntity);
            createRagFlowDataset(spaceEntity, configDTO);
        } else {
            spaceConfigMapper.updateById(configEntity);
            updateRagFlowDataset(spaceEntity, configDTO);
        }
        
        log.info("知识空间配置更新成功，配置ID：{}", configEntity.getId());
        return configEntity.getId();
    }

    /**
     * 处理文档级配置（无需同步到RagFlow，向量化时处理）
     */
    private Long handleDocumentConfig(KgBasesRelConfigDTO configDTO) {
        // 1. 查询或创建配置实体
        KgBasesSpaceConfigC configEntity = findOrCreateConfig(configDTO);
        
        // 2. 更新配置属性
        updateConfigEntity(configEntity, configDTO);
        
        // 3. 保存配置（文档配置不需要调用RagFlow）
        if (configEntity.getId() == null) {
            spaceConfigMapper.insert(configEntity);
        } else {
            spaceConfigMapper.updateById(configEntity);
        }
        
        log.info("文档配置更新成功，配置ID：{}，文档ID：{}", 
                configEntity.getId(), configDTO.getRelationId());
        return configEntity.getId();
    }

    /**
     * 查询或创建配置实体
     */
    private KgBasesSpaceConfigC findOrCreateConfig(KgBasesRelConfigDTO configDTO) {
        LambdaQueryWrapper<KgBasesSpaceConfigC> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KgBasesSpaceConfigC::getConfigType, configDTO.getConfigType())
                   .eq(KgBasesSpaceConfigC::getRelationId, configDTO.getRelationId())
                   .eq(KgBasesSpaceConfigC::getSpaceId, configDTO.getSpaceId())
                   .eq(KgBasesSpaceConfigC::getIsDeleted, false);

        KgBasesSpaceConfigC configEntity = spaceConfigMapper.selectOne(queryWrapper);

        if (configEntity == null) {
            configEntity = createNewConfigEntity(configDTO);
        }
        
        return configEntity;
    }

    /**
     * 创建新的配置实体
     */
    private KgBasesSpaceConfigC createNewConfigEntity(KgBasesRelConfigDTO configDTO) {
        KgBasesSpaceConfigC configEntity = new KgBasesSpaceConfigC();
        String currentUser = PtSecurityUtils.getUsername();
        LocalDateTime now = LocalDateTime.now();
        
        /*configEntity.setConfigType(configDTO.getConfigType());
        configEntity.setRelationId(configDTO.getRelationId());
        configEntity.setSpaceId(configDTO.getSpaceId());*/

        BeanUtils.copyProperties(configDTO, configEntity);

        configEntity.setCreatedTime(now);
        configEntity.setCreatedUser(currentUser);
        configEntity.setUpdatedTime(now);
        configEntity.setUpdatedUser(currentUser);
        configEntity.setIsDeleted(false);
        
        return configEntity;
    }

    /**
     * 更新配置实体属性
     */
    private void updateConfigEntity(KgBasesSpaceConfigC configEntity, KgBasesRelConfigDTO configDTO) {
        BeanUtils.copyProperties(configDTO, configEntity, "spaceId", "id", "createdTime", "createdUser");
        configEntity.setUpdatedTime(LocalDateTime.now());
        configEntity.setUpdatedUser(PtSecurityUtils.getUsername());
    }

    /**
     * 创建RagFlow数据集
     */
    private void createRagFlowDataset(KgBasesSpaceC spaceEntity, KgBasesRelConfigDTO configDTO) {
        try {
            RagFlowDatasetCreateRequest request = buildRagFlowCreateRequest(spaceEntity, configDTO);
            
            ApiResponse response = ragFlowClient.createDataset(request);
            log.debug("RagFlow创建数据集响应：{}", response);
            
            if (response.getCode() == -1) {
                throw new KnowledgeBasesException("RagFlow创建知识空间失败");
            }
            
            // 更新空间实体的RagFlowId
            DatasetResponse dataset = response.getData();
            spaceEntity.setRagFlowId(dataset.getId());
            spaceMapper.updateById(spaceEntity);
            
            log.info("RagFlow数据集创建成功，ID：{}", dataset.getId());
            
        } catch (Exception e) {
            log.error("创建RagFlow数据集失败", e);
            throw new KnowledgeBasesException("创建RagFlow数据集失败：" + e.getMessage());
        }
    }

    /**
     * 更新RagFlow数据集
     */
    private void updateRagFlowDataset(KgBasesSpaceC spaceEntity, KgBasesRelConfigDTO configDTO) {
        try {
            RagFlowDatasetUpdateRequest request = buildRagFlowUpdateRequest(spaceEntity, configDTO);
            log.debug("RagFlow更新数据集请求：{}", JsonUtil.objectToJson(request));
            
            ApiResponse response = ragFlowClient.updateDataset(spaceEntity.getRagFlowId(), request);
            if (response == null || response.getCode() != 0) {
                String errorMsg = response != null ? response.getMessage() : "响应为空";
                throw new KnowledgeBasesException("RagFlow更新数据集失败：" + errorMsg);
            }
            
            log.info("RagFlow数据集更新成功，空间ID：{}，数据集ID：{}", 
                    spaceEntity.getKgBasesSpaceCId(), spaceEntity.getRagFlowId());
            
        } catch (Exception e) {
            log.error("更新RagFlow数据集失败", e);
            throw new KnowledgeBasesException("更新RagFlow数据集失败：" + e.getMessage());
        }
    }

    /**
     * 构建RagFlow创建请求
     */
    private RagFlowDatasetCreateRequest buildRagFlowCreateRequest(KgBasesSpaceC spaceEntity, KgBasesRelConfigDTO configDTO) {
        RagFlowDatasetCreateRequest request = new RagFlowDatasetCreateRequest();
        request.setName(spaceEntity.getBasesSpaceName());
        request.setPermission("me");
        
        // 设置切片方法，默认为naive
        String chunkMethod = StringUtils.isNotBlank(configDTO.getSliceMethod()) ? 
                           configDTO.getSliceMethod() : "naive";
        request.setChunk_method(chunkMethod);
        
        // 构建解析器配置
        Map<String, Object> parserConfig = buildParserConfig(chunkMethod, configDTO);
        request.setParser_config(parserConfig);
        
        return request;
    }

    /**
     * 构建RagFlow更新请求（完整支持所有API参数）
     */
    private RagFlowDatasetUpdateRequest buildRagFlowUpdateRequest(KgBasesSpaceC spaceEntity, KgBasesRelConfigDTO configDTO) {
        log.info("构建RagFlow数据集更新请求，空间ID：{}，数据集ID：{}", 
                spaceEntity.getKgBasesSpaceCId(), spaceEntity.getRagFlowId());
        
        RagFlowDatasetUpdateRequest request = new RagFlowDatasetUpdateRequest();
        
        // 1. 设置基本信息
        request.setName(spaceEntity.getBasesSpaceName());
        
        // 2. 设置描述信息（从空间摘要获取）
        if (StringUtils.isNotBlank(spaceEntity.getSummary())) {
            request.setDescription(spaceEntity.getSummary());
        }
        
        // 3. 设置权限（默认为me，可根据需要扩展）
        request.setPermission("me");
        
        // 4. 设置嵌入模型（如果提供）
        /*if (StringUtils.isNotBlank(configDTO.getEmbeddingModel())) {
            request.setEmbedding_model(configDTO.getEmbeddingModel());
            log.info("设置嵌入模型：{}", configDTO.getEmbeddingModel());
        }*/
        //todo 默认设置为空
        //request.setEmbedding_model("");
        
        // 5. 设置切片方法
        String chunkMethod = StringUtils.isNotBlank(configDTO.getSliceMethod()) ? 
                           configDTO.getSliceMethod() : "naive";
        request.setChunk_method(chunkMethod);
        
        // 6. 设置页权重（PageRank，默认为0）
        request.setPagerank(0);
        
        // 7. 构建解析器配置
        Map<String, Object> parserConfig = buildParserConfig(chunkMethod, configDTO);
        request.setParser_config(parserConfig);
        
        log.info("RagFlow数据集更新请求构建完成：name={}, chunk_method={}, embedding_model={}, parser_config_keys={}", 
                request.getName(), request.getChunk_method(), request.getEmbedding_model(), 
                parserConfig != null ? parserConfig.keySet() : "null");
        
        return request;
    }

    /**
     * 构建解析器配置
     */
    private Map<String, Object> buildParserConfig(String chunkMethod, KgBasesRelConfigDTO configDTO) {
        Map<String, Object> parserConfig = RagFlowParserConfigBuilder.buildParserConfig(chunkMethod);
        
        // 如果是naive方法，使用具体配置参数
        if ("naive".equals(chunkMethod)) {
            parserConfig = RagFlowParserConfigBuilder.updateNaiveConfig(
                parserConfig,
                configDTO.getAutoKeywordCount(),     // 自动关键词数量
                configDTO.getAutoQuestionCount(),    // 自动问题数量
                configDTO.getTextBlockSize(),        // chunk_token_num
                configDTO.getTextSegmentSeparator(), // delimiter
                12                                    // task_page_size，默认12
            );
        }
        
        return parserConfig;
    }

    @Override
    public KgBasesRelConfigDTO getSpaceConfig(KgBasesConfigDTO configDTO) {
        log.info("获取知识空间配置：{}", configDTO.toString());

        try {

            // 1. 查询知识空间配置
            LambdaQueryWrapper<KgBasesSpaceConfigC> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KgBasesSpaceConfigC::getConfigType, configDTO.getConfigType())
                       .eq(KgBasesSpaceConfigC::getRelationId, configDTO.getRelId())
                       .eq(KgBasesSpaceConfigC::getSpaceId, configDTO.getSpaceId())
                       .eq(KgBasesSpaceConfigC::getIsDeleted, false);

            KgBasesSpaceConfigC configEntity = spaceConfigMapper.selectOne(queryWrapper);

            // 3. 转换为DTO
            KgBasesRelConfigDTO kgBasesRelConfigDTO = new KgBasesRelConfigDTO();
            if (configEntity != null) {
                BeanUtils.copyProperties(configEntity, kgBasesRelConfigDTO);
            }

            return kgBasesRelConfigDTO;
        } catch (Exception e) {
            log.error("获取知识空间配置失败", e);
            throw new KnowledgeBasesException("获取知识空间配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocument(Long spaceId, Long kgKnowledgeBasesId) {
        log.info("删除文档，知识空间ID：{}，文档ID：{}", spaceId, kgKnowledgeBasesId);
        
        try {
            // 1. 查询知识空间信息
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(spaceId);
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在");
            }
            
            // 2. 查询文档基本信息，确认文档存在
            LambdaQueryWrapper<KnowledgeBasesEntity> docQueryWrapper = new LambdaQueryWrapper<>();
            docQueryWrapper.eq(KnowledgeBasesEntity::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                           .eq(KnowledgeBasesEntity::getIsDeleted, "0"); // 0表示未删除
            
            KnowledgeBasesEntity docEntity = knowledgeBasesMapper.selectOne(docQueryWrapper);
            if (docEntity == null) {
                throw new KnowledgeBasesException("文档不存在或已被删除");
            }
            
            // 3. 查询文档处理信息（可能为空）
            LambdaQueryWrapper<KgBasesDocumentProcessInfoC> processQueryWrapper = new LambdaQueryWrapper<>();
            processQueryWrapper.eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                               .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                               .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false);
            
            KgBasesDocumentProcessInfoC processInfo = documentProcessMapper.selectOne(processQueryWrapper);
            log.info("文档处理信息查询结果：{}", processInfo != null ? "存在" : "不存在");
            
            String currentUser = PtSecurityUtils.getUsername();
            LocalDateTime now = LocalDateTime.now();
            
            // 3. 逻辑删除关联表 kg_bases_space_relation_c
            LambdaUpdateWrapper<KgBasesSpaceRelationC> relationUpdateWrapper = new LambdaUpdateWrapper<>();
            relationUpdateWrapper.eq(KgBasesSpaceRelationC::getKgBasesSpaceCId, spaceId)
                                 .eq(KgBasesSpaceRelationC::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                                 .eq(KgBasesSpaceRelationC::getBaseType, "1")
                                 .eq(KgBasesSpaceRelationC::getIsDeleted, false)
                                 .set(KgBasesSpaceRelationC::getIsDeleted, true)
                                 .set(KgBasesSpaceRelationC::getUpdatedUser, currentUser)
                                 .set(KgBasesSpaceRelationC::getUpdatedTime, new Date());
            
            int relationDeleteCount = spaceRelationMapper.update(null, relationUpdateWrapper);
            log.info("逻辑删除空间关联关系，影响行数：{}", relationDeleteCount);
            
            // 4. 逻辑删除文档基本信息 kg_knowledge_bases_d
            LambdaUpdateWrapper<KnowledgeBasesEntity> docUpdateWrapper = new LambdaUpdateWrapper<>();
            docUpdateWrapper.eq(KnowledgeBasesEntity::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                            .eq(KnowledgeBasesEntity::getIsDeleted, "0")
                            .set(KnowledgeBasesEntity::getIsDeleted, "1") // 1表示删除
                            .set(KnowledgeBasesEntity::getUpdatedUserName, currentUser)
                            .set(KnowledgeBasesEntity::getUpdatedTime, new Date());
            
            int docDeleteCount = knowledgeBasesMapper.update(null, docUpdateWrapper);
            log.info("逻辑删除文档基本信息，影响行数：{}", docDeleteCount);
            
            // 5. 逻辑删除文档处理信息 kg_bases_document_process_info_c（如果存在）
            int processDeleteCount = 0;
            if (processInfo != null) {
                LambdaUpdateWrapper<KgBasesDocumentProcessInfoC> processUpdateWrapper = new LambdaUpdateWrapper<>();
                processUpdateWrapper.eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                                    .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                                    .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false)
                                    .set(KgBasesDocumentProcessInfoC::getIsDeleted, true)
                                    .set(KgBasesDocumentProcessInfoC::getUpdatedUser, currentUser)
                                    .set(KgBasesDocumentProcessInfoC::getUpdatedTime, now);
                
                processDeleteCount = documentProcessMapper.update(null, processUpdateWrapper);
                log.info("逻辑删除文档处理信息，影响行数：{}", processDeleteCount);
            } else {
                log.info("文档处理信息不存在，跳过删除操作");
            }
            
            // 6. 逻辑删除文档切片 kg_bases_document_slice_d
            LambdaUpdateWrapper<KgBasesDocumentSliceD> sliceUpdateWrapper = new LambdaUpdateWrapper<>();
            sliceUpdateWrapper.eq(KgBasesDocumentSliceD::getKgBasesSpaceCId, spaceId)
                              .eq(KgBasesDocumentSliceD::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                              .eq(KgBasesDocumentSliceD::getIsDeleted, false)
                              .set(KgBasesDocumentSliceD::getIsDeleted, true)
                              .set(KgBasesDocumentSliceD::getUpdatedUser, currentUser)
                              .set(KgBasesDocumentSliceD::getUpdatedTime, now);
            
            int sliceDeleteCount = documentSliceMapper.update(null, sliceUpdateWrapper);
            log.info("逻辑删除文档切片，影响行数：{}", sliceDeleteCount);
            
            // 7. 逻辑删除文档配置 kg_bases_space_config_c (如果存在文档级配置)
            LambdaUpdateWrapper<KgBasesSpaceConfigC> configUpdateWrapper = new LambdaUpdateWrapper<>();
            configUpdateWrapper.eq(KgBasesSpaceConfigC::getConfigType, ConfigTypeEnum.DOCUMENT.getCode())
                               .eq(KgBasesSpaceConfigC::getRelationId, kgKnowledgeBasesId)
                               .eq(KgBasesSpaceConfigC::getSpaceId, spaceId)
                               .eq(KgBasesSpaceConfigC::getIsDeleted, false)
                               .set(KgBasesSpaceConfigC::getIsDeleted, true)
                               .set(KgBasesSpaceConfigC::getUpdatedUser, currentUser)
                               .set(KgBasesSpaceConfigC::getUpdatedTime, now);
            
            int configDeleteCount = spaceConfigMapper.update(null, configUpdateWrapper);
            log.info("逻辑删除文档配置，影响行数：{}", configDeleteCount);
            
            // 8. 调用RAGFlow服务删除文档（仅在有文档处理信息且有RAG文档ID时）
            boolean ragFlowDeleteResult = true; // 默认成功，避免阻塞业务流程
            
            try {
                if (processInfo != null && 
                    StringUtils.isNotBlank(processInfo.getRagDocumentId()) && 
                    StringUtils.isNotBlank(spaceEntity.getRagFlowId())) {
                    
                    // 构建要删除的文档ID列表
                    String ragDocumentId = processInfo.getRagDocumentId();
                    ragFlowDeleteResult = ragFlowClient.deleteDocuments(
                        spaceEntity.getRagFlowId(), 
                        Collections.singletonList(ragDocumentId)
                    );
                    
                    if (!ragFlowDeleteResult) {
                        log.warn("RAGFlow删除文档失败，但不影响本地删除操作，RAG文档ID：{}", ragDocumentId);
                        // 注意：这里不抛异常，避免影响本地数据的删除
                    } else {
                        log.info("RAGFlow删除文档成功，RAG文档ID：{}", ragDocumentId);
                    }
                } else {
                    log.info("文档处理信息不存在或未同步到RAGFlow，跳过RAGFlow删除操作");
                }
                
            } catch (Exception e) {
                log.error("调用RAGFlow删除文档异常，但不影响本地删除操作", e);
                // 不抛异常，避免影响本地数据的删除
                ragFlowDeleteResult = false;
            }
            
            // 9. 记录删除操作日志
            log.info("文档删除完成 - 知识空间ID：{}，文档ID：{}，关联关系删除：{}行，" +
                    "文档基本信息删除：{}行，处理信息删除：{}行，切片删除：{}行，配置删除：{}行，RAGFlow删除：{}", 
                    spaceId, kgKnowledgeBasesId, relationDeleteCount, docDeleteCount, processDeleteCount,
                    sliceDeleteCount, configDeleteCount, ragFlowDeleteResult ? "成功" : "跳过");
            
            return true;
            
        } catch (KnowledgeBasesException e) {
            // 业务异常直接抛出
            log.error("删除文档业务异常", e);
            throw e;
        } catch (Exception e) {
            // 系统异常包装后抛出
            log.error("删除文档系统异常", e);
            throw new KnowledgeBasesException("删除文档失败：" + e.getMessage(), e);
        }
    }


    @Override
    public boolean updateSliceKeywords(Long spaceId, String sliceId, KgBasesSliceKeywordDTO keywordDTO) {
        log.info("更新切片关键词，知识空间ID：{}，切片ID：{}", spaceId, sliceId);
        
        try {
            // 1. 查询知识空间
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(spaceId);
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在");
            }
            
            // 2. 调用RAGFlow服务更新切片关键词
            boolean result = ragFlowClient.updateSliceKeywords(spaceEntity.getRagFlowId(), sliceId, keywordDTO);
            
            return result;
        } catch (Exception e) {
            log.error("更新切片关键词失败", e);
            throw new KnowledgeBasesException("更新切片关键词失败：" + e.getMessage());
        }
    }

    @Override
    public boolean updateSliceQuestions(Long spaceId, String sliceId, KgBasesSliceQuestionDTO questionDTO) {
        log.info("更新切片问题，知识空间ID：{}，切片ID：{}", spaceId, sliceId);
        
        try {
            // 1. 查询知识空间
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(spaceId);
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在");
            }
            
            // 2. 调用RAGFlow服务更新切片问题
            boolean result = ragFlowClient.updateSliceQuestions(spaceEntity.getKgBasesSpaceCId().toString(), sliceId, questionDTO);
            
            return result;
        } catch (Exception e) {
            log.error("更新切片问题失败", e);
            throw new KnowledgeBasesException("更新切片问题失败：" + e.getMessage());
        }
    }

    @Override
    public KgBasesConfigOptionsVO getConfigOptions() {
        log.info("获取配置选项");
        
        try {
            // 构建配置选项
            KgBasesConfigOptionsVO options = new KgBasesConfigOptionsVO();
            
            // 从数据库获取PDF解析器选项
            List<KgBasesPdfParserConfigC> pdfParserConfigs = pdfParserConfigMapper.selectList(
                    new LambdaQueryWrapper<KgBasesPdfParserConfigC>()
                            .eq(KgBasesPdfParserConfigC::getIsEnabled, true)
            );
            List<ConfigOptionItemVO> pdfParsers = pdfParserConfigs.stream()
                    .map(config -> new ConfigOptionItemVO(config.getCode(), config.getName(), ""))
                    .collect(Collectors.toList());
            options.setPdfParsers(pdfParsers);
            
            // 从数据库获取预处理方法选项
            List<KgBasesPreprocessConfigC> preprocessConfigs = preprocessConfigMapper.selectList(
                    new LambdaQueryWrapper<KgBasesPreprocessConfigC>()
                            .eq(KgBasesPreprocessConfigC::getIsEnabled, true)
            );
            List<ConfigOptionItemVO> preprocessMethods = preprocessConfigs.stream()
                    .map(config -> new ConfigOptionItemVO(config.getCode(), config.getName(), ""))
                    .collect(Collectors.toList());
            options.setPreprocessMethods(preprocessMethods);
            
            // 从数据库获取切片方法选项
            List<KgBasesSliceConfigC> sliceConfigs = sliceConfigMapper.selectList(
                    new LambdaQueryWrapper<KgBasesSliceConfigC>()
                            .eq(KgBasesSliceConfigC::getIsEnabled, true)
            );
            List<ConfigOptionItemVO> sliceMethods = sliceConfigs.stream()
                    .map(config -> new ConfigOptionItemVO(config.getCode(), config.getName(), ""))
                    .collect(Collectors.toList());
            options.setSliceMethods(sliceMethods);
            
            // 从数据库获取Embedding模型选项
            List<KgBasesEmbeddingConfigC> embeddingConfigs = embeddingConfigMapper.selectList(
                    new LambdaQueryWrapper<KgBasesEmbeddingConfigC>()
                            .eq(KgBasesEmbeddingConfigC::getIsEnabled, true)
            );
            List<ConfigOptionItemVO> embeddingModels = embeddingConfigs.stream()
                    .map(config -> new ConfigOptionItemVO(config.getCode(), config.getName(), ""))
                    .collect(Collectors.toList());
            options.setEmbeddingModels(embeddingModels);
            
            // 从数据库获取检索方法选项
            List<KgBasesRetrievalConfigC> retrievalConfigs = retrievalConfigMapper.selectList(
                    new LambdaQueryWrapper<KgBasesRetrievalConfigC>()
                            .eq(KgBasesRetrievalConfigC::getIsEnabled, true)
            );
            List<ConfigOptionItemVO> retrievalMethods = retrievalConfigs.stream()
                    .map(config -> new ConfigOptionItemVO(config.getCode(), config.getName(), ""))
                    .collect(Collectors.toList());
            options.setRetrievalMethods(retrievalMethods);
            
            // 从数据库获取Rerank模型选项
            List<KgBasesRerankConfigC> rerankConfigs = rerankConfigMapper.selectList(
                    new LambdaQueryWrapper<KgBasesRerankConfigC>()
                            .eq(KgBasesRerankConfigC::getIsEnabled, true)
            );
            List<ConfigOptionItemVO> rerankModels = rerankConfigs.stream()
                    .map(config -> new ConfigOptionItemVO(config.getCode(), config.getName(), ""))
                    .collect(Collectors.toList());
            options.setRerankModels(rerankModels);
            
            // 设置数据来源选项
            List<ConfigOptionItemVO> dataSources = new ArrayList<>();
            dataSources.add(new ConfigOptionItemVO(DataSourceEnum.TEST.getCode(), DataSourceEnum.TEST.getDesc(), DataSourceEnum.TEST.getCode()));
            dataSources.add(new ConfigOptionItemVO(DataSourceEnum.PRODUCTION.getCode(), DataSourceEnum.PRODUCTION.getDesc(), DataSourceEnum.PRODUCTION.getCode()));
            options.setDataSources(dataSources);
            
            // 获取参数设置
            List<KgBasesParamSettingsC> paramSettings = paramSettingsMapper.selectList(
                    new LambdaQueryWrapper<KgBasesParamSettingsC>()
                            .eq(KgBasesParamSettingsC::getIsEnabled, true)
                            .orderByAsc(KgBasesParamSettingsC::getSortOrder)
            );
            options.setParamSettings(paramSettings);
            
            return options;
        } catch (Exception e) {
            log.error("获取配置选项失败", e);
            throw new KnowledgeBasesException("获取配置选项失败：" + e.getMessage());
        }
    }
    
    @Override
    public PageInfo<KgBasesDocumentListVO> getDocumentList(KgBasesDocumentQueryDTO queryDTO) {
        log.info("获取文档列表，参数：{}", queryDTO);

        try {
            // 设置分页
            Page<KgBasesDocumentListVO> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());

            // 1. 先查询知识空间关联的文档ID列表
            LambdaQueryWrapper<KgBasesSpaceRelationC> relationWrapper = new LambdaQueryWrapper<>();
            relationWrapper.eq(KgBasesSpaceRelationC::getKgBasesSpaceCId, queryDTO.getSpaceId())
                    .eq(KgBasesSpaceRelationC::getBaseType, "1") // base_type为1表示关联文档
                    .eq(KgBasesSpaceRelationC::getIsDeleted, false);
            List<KgBasesSpaceRelationC> relations = spaceRelationMapper.selectList(relationWrapper);

            if (CollectionUtils.isEmpty(relations)) {
                // 如果没有关联文档，返回空列表
                return createEmptyPageInfo(queryDTO);
            }

            List<Long> documentIds = relations.stream()
                    .map(KgBasesSpaceRelationC::getKgKnowledgeBasesId)
                    .collect(Collectors.toList());

            // 2. 查询文档基本信息（带分页）
            LambdaQueryWrapper<KnowledgeBasesEntity> docWrapper = new LambdaQueryWrapper<>();
            docWrapper.in(KnowledgeBasesEntity::getKgKnowledgeBasesId, documentIds)
                    .eq(KnowledgeBasesEntity::getIsDeleted, "0");

            // 添加关键词搜索条件
            if (StringUtils.isNotBlank(queryDTO.getKeyword())) {
                docWrapper.and(wrapper -> wrapper
                        .like(KnowledgeBasesEntity::getKnowledgeName, queryDTO.getKeyword())
                        .or()
                        .like(KnowledgeBasesEntity::getSummary, queryDTO.getKeyword())
                        .or()
                        .like(KnowledgeBasesEntity::getOperativeWord, queryDTO.getKeyword())
                );
            }

            Page<KnowledgeBasesEntity> docPage = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
            Page<KnowledgeBasesEntity> resultPage = knowledgeBasesMapper.selectPage(docPage, docWrapper);

            if (CollectionUtils.isEmpty(resultPage.getRecords())) {
                return createEmptyPageInfo(queryDTO);
            }

            // 3. 获取当前页文档ID列表，进行批量查询优化
            List<Long> currentPageDocIds = resultPage.getRecords().stream()
                    .map(KnowledgeBasesEntity::getKgKnowledgeBasesId)
                    .collect(Collectors.toList());

            // 4. 批量查询处理信息 - 避免N+1查询
            Map<Long, KgBasesDocumentProcessInfoC> processInfoMap = batchQueryProcessInfo(queryDTO.getSpaceId(), currentPageDocIds);

            // 5. 批量查询配置信息 - 避免N+1查询
            Map<Long, KgBasesSpaceConfigC> configMap = batchQuerySpaceConfig(queryDTO.getSpaceId(), currentPageDocIds);

            // 6. 高效转换为VO列表
            List<KgBasesDocumentListVO> documentList = resultPage.getRecords().stream()
                    .map(doc -> buildDocumentListVO(doc, queryDTO.getSpaceId(), processInfoMap, configMap))
                    .collect(Collectors.toList());

            // 7. 构建分页结果
            return buildPageInfo(documentList, resultPage);

        } catch (Exception e) {
            log.error("获取文档列表失败", e);
            throw new KnowledgeBasesException("获取文档列表失败：" + e.getMessage());
        }
    }

    /**
     * 创建空的分页结果
     */
    private PageInfo<KgBasesDocumentListVO> createEmptyPageInfo(KgBasesDocumentQueryDTO queryDTO) {
        PageInfo<KgBasesDocumentListVO> pageInfo = new PageInfo<>();
        pageInfo.setList(new ArrayList<>());
        pageInfo.setTotal(0L);
        pageInfo.setPageNum(queryDTO.getPageNo());
        pageInfo.setPageSize(queryDTO.getPageSize());
        pageInfo.setPages(0);
        return pageInfo;
    }

    /**
     * 批量查询处理信息 - 性能优化关键
     */
    private Map<Long, KgBasesDocumentProcessInfoC> batchQueryProcessInfo(Long spaceId, List<Long> documentIds) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<KgBasesDocumentProcessInfoC> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                .in(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, documentIds)
                .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false);

        List<KgBasesDocumentProcessInfoC> processInfoList = documentProcessMapper.selectList(wrapper);
        
        return processInfoList.stream()
                .collect(Collectors.toMap(
                    KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId,
                    info -> info,
                    (existing, replacement) -> existing // 处理重复key
                ));
    }

    /**
     * 批量查询配置信息 - 性能优化关键
     */
    private Map<Long, KgBasesSpaceConfigC> batchQuerySpaceConfig(Long spaceId, List<Long> documentIds) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<KgBasesSpaceConfigC> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(KgBasesSpaceConfigC::getRelationId, documentIds)
                .eq(KgBasesSpaceConfigC::getConfigType, ConfigTypeEnum.DOCUMENT.getCode())
                .eq(KgBasesSpaceConfigC::getSpaceId, spaceId)
                .eq(KgBasesSpaceConfigC::getIsDeleted, false);

        List<KgBasesSpaceConfigC> configList = spaceConfigMapper.selectList(wrapper);
        
        return configList.stream()
                .collect(Collectors.toMap(
                    KgBasesSpaceConfigC::getRelationId,
                    config -> config,
                    (existing, replacement) -> existing // 处理重复key
                ));
    }

    /**
     * 构建文档列表VO - 高效转换
     */
    private KgBasesDocumentListVO buildDocumentListVO(
            KnowledgeBasesEntity doc, 
            Long spaceId,
            Map<Long, KgBasesDocumentProcessInfoC> processInfoMap,
            Map<Long, KgBasesSpaceConfigC> configMap) {
        
        KgBasesDocumentListVO vo = new KgBasesDocumentListVO();
        
        // 基本信息
        vo.setKgKnowledgeBasesId(doc.getKgKnowledgeBasesId());
        vo.setDocumentName(doc.getKnowledgeName());
        vo.setDocumentSize(doc.getFilesize());
        vo.setCreatedTime(doc.getCreatedTime());

        // 处理信息 - 从Map中获取，无需数据库查询
        KgBasesDocumentProcessInfoC processInfo = processInfoMap.get(doc.getKgKnowledgeBasesId());
        if (Objects.nonNull(processInfo)) {
            vo.setDocumentId(processInfo.getId());
            vo.setProcessStatus(String.valueOf(processInfo.getProcessStatus()));
            vo.setProcessStatusDesc(getProcessStatusDesc(processInfo.getProcessStatus()));
            vo.setSliceCount(processInfo.getChunkCount() != null ? processInfo.getChunkCount() : 0);
            vo.setEnabledUse(processInfo.isEnabledUse());

            //返回rag日志
            vo.setRagMessage(processInfo.getRagMessage());
            vo.setFilePath(processInfo.getPreprocessedFilePath());
        } else {
            // 设置默认值
            vo.setProcessStatus(ProcessStatusEnum.UNPROCESSED.getCode());
            vo.setProcessStatusDesc(ProcessStatusEnum.UNPROCESSED.getDesc());
            vo.setSliceCount(0);
            vo.setEnabledUse(false);
        }

        // 配置信息 - 从Map中获取，无需数据库查询
        KgBasesSpaceConfigC config = configMap.get(doc.getKgKnowledgeBasesId());
        if (Objects.nonNull(config)) {
            vo.setSliceMethod(config.getSliceMethod());
            vo.setConfigId(config.getId());
        }

        return vo;
    }

    /**
     * 构建分页结果 - 性能优化
     */
    private PageInfo<KgBasesDocumentListVO> buildPageInfo(
            List<KgBasesDocumentListVO> documentList, 
            Page<KnowledgeBasesEntity> resultPage) {
        
        PageInfo<KgBasesDocumentListVO> pageInfo = new PageInfo<>();
        pageInfo.setList(documentList);
        pageInfo.setTotal(resultPage.getTotal());
        pageInfo.setPageNum(Integer.valueOf(String.valueOf(resultPage.getCurrent())));
        pageInfo.setPageSize(Integer.valueOf(String.valueOf(resultPage.getSize())));
        pageInfo.setPages(Integer.valueOf(String.valueOf(resultPage.getPages())));
        
        return pageInfo;
    }
    
    @Override
    public String preprocessDocument(Long spaceId, Long documentId) {
        log.info("预处理文档，知识空间ID：{}，文档ID：{}", spaceId, documentId);
        
        try {
            // 1. 查询知识空间
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(spaceId);
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在");
            }
            
            // 2. 查询文档处理信息
            KgBasesDocumentProcessInfoC processInfo = documentProcessMapper.selectOne(
                    new LambdaQueryWrapper<KgBasesDocumentProcessInfoC>()
                            .eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                            .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, documentId)
            );
            
            if (processInfo == null) {
                throw new KnowledgeBasesException("文档不存在");
            }
            
            // 3. 获取知识空间配置
            KgBasesSpaceConfigC spaceConfig = spaceConfigMapper.selectOne(
                    new LambdaQueryWrapper<KgBasesSpaceConfigC>()
                            .eq(KgBasesSpaceConfigC::getConfigType, ConfigTypeEnum.SPACE.getCode())
                            .eq(KgBasesSpaceConfigC::getRelationId, spaceId)
            );
            
            // 4. 获取文档配置（如果存在）
            KgBasesSpaceConfigC documentConfig = spaceConfigMapper.selectOne(
                    new LambdaQueryWrapper<KgBasesSpaceConfigC>()
                            .eq(KgBasesSpaceConfigC::getConfigType, ConfigTypeEnum.DOCUMENT.getCode())
                            .eq(KgBasesSpaceConfigC::getRelationId, documentId)
            );
            
            // 5. 合并配置，文档配置优先
            String pdfParser = documentConfig != null && StringUtils.isNotBlank(documentConfig.getPdfParser()) ? 
                    documentConfig.getPdfParser() : spaceConfig.getPdfParser();
            String preprocessMethod = documentConfig != null && StringUtils.isNotBlank(documentConfig.getPreprocessMethod()) ? 
                    documentConfig.getPreprocessMethod() : spaceConfig.getPreprocessMethod();
            String sliceMethod = documentConfig != null && StringUtils.isNotBlank(documentConfig.getSliceMethod()) ? 
                    documentConfig.getSliceMethod() : spaceConfig.getSliceMethod();
            Integer textBlockSize = documentConfig != null && documentConfig.getTextBlockSize() != null ? 
                    documentConfig.getTextBlockSize() : spaceConfig.getTextBlockSize();
            String textSegmentSeparator = documentConfig != null && StringUtils.isNotBlank(documentConfig.getTextSegmentSeparator()) ? 
                    documentConfig.getTextSegmentSeparator() : spaceConfig.getTextSegmentSeparator();
            Integer autoKeywordCount = documentConfig != null && documentConfig.getAutoKeywordCount() != null ? 
                    documentConfig.getAutoKeywordCount() : spaceConfig.getAutoKeywordCount();
            Integer autoQuestionCount = documentConfig != null && documentConfig.getAutoQuestionCount() != null ? 
                    documentConfig.getAutoQuestionCount() : spaceConfig.getAutoQuestionCount();
            
            // 6. 更新文档处理状态为预处理中
            LambdaUpdateWrapper<KgBasesDocumentProcessInfoC> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                    .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, documentId)
                    .set(KgBasesDocumentProcessInfoC::getProcessStatus, ProcessStatusEnum.PREPROCESSING.getCode())
                    .set(KgBasesDocumentProcessInfoC::getStatusMessage, "预处理中")
                    .set(KgBasesDocumentProcessInfoC::getUpdatedTime, LocalDateTime.now());
            documentProcessMapper.update(null, updateWrapper);
            
            // 7. 调用RAGFlow服务预处理文档
            String taskId = ragFlowClient.preprocessFile(
                    processInfo.getRagDocumentId(),
                    spaceEntity.getRagFlowId(),
                    preprocessMethod,
                    pdfParser,
                    sliceMethod,
                    textBlockSize,
                    50, // 默认重叠大小
                    textSegmentSeparator,
                    autoKeywordCount,
                    autoQuestionCount
            );
            
            return taskId;
        } catch (Exception e) {
            log.error("预处理文档失败", e);
            throw new KnowledgeBasesException("预处理文档失败：" + e.getMessage());
        }
    }

    @Override
    public String vectorizeDocument(Long spaceId, Long kgKnowledgeBasesId) {
        log.info("向量化文档，知识空间ID：{}，文档ID：{}", spaceId, kgKnowledgeBasesId);

        try {
            // 1. 获取知识空间信息
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(spaceId);
            if (Objects.isNull(spaceEntity)) {
                throw new KnowledgeBasesException("知识空间不存在");
            }

            // 2. 获取文档处理信息，使用同步锁避免并发重复创建
            synchronized (this) {
                KgBasesDocumentProcessInfoC processInfo = documentProcessMapper.selectOne(
                        new LambdaQueryWrapper<KgBasesDocumentProcessInfoC>()
                                .eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                                .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                                .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false)
                );

                // 3. 检查当前状态，如果已经在向量化中或已完成，则抛出异常
                if (Objects.nonNull(processInfo)) {
                    Integer currentStatus = processInfo.getProcessStatus();
                    if (currentStatus != null) {
                        // 如果已经向量化中、向量化完成，则不允许重复向量化
                        if (ProcessStatusEnum.VECTORIZING.getCode().equals(String.valueOf(currentStatus))) {
                            String statusDesc = getProcessStatusDesc(currentStatus);
                            throw new KnowledgeBasesException("文档当前状态为：" + statusDesc + "，不允许重复向量化");
                        }
                    }
                }

                // 4. 检查是否需要先上传文件
                String ragDocumentId = null;
                if (Objects.nonNull(processInfo) && StringUtils.isNotBlank(processInfo.getRagDocumentId())) {
                    ragDocumentId = processInfo.getRagDocumentId();
                    log.info("文档已存在RAGFlow文档ID：{}，跳过上传步骤", ragDocumentId);
                } else {
                    // 需要先上传文件到RAGFlow
                    log.info("文档尚未上传到RAGFlow，开始上传流程");
                    
                    // 4.1 从知识库表获取文件路径
                    KnowledgeBasesEntity knowledgeBase = knowledgeBasesMapper.selectById(kgKnowledgeBasesId);
                    if (Objects.isNull(knowledgeBase) || StringUtils.isBlank(knowledgeBase.getKnowledgeFilePath())) {
                        throw new KnowledgeBasesException("知识库文件路径为空，无法上传");
                    }
                    String filePath = knowledgeBase.getKnowledgeFilePath();
                    log.info("准备上传文件：{}", filePath);
                    
                    // 4.2 如果processInfo为null，先创建一个初始记录，状态为"预处理中"
                    // 注意：这里只创建记录但不设置ragDocumentId，让processPublishedKnowledgeBase方法来设置
                    if (Objects.isNull(processInfo)) {
                        processInfo = new KgBasesDocumentProcessInfoC();
                        processInfo.setKgBasesSpaceCId(spaceId);
                        processInfo.setKgKnowledgeBasesId(kgKnowledgeBasesId);
                        processInfo.setProcessStatus(Integer.parseInt(ProcessStatusEnum.PREPROCESSING.getCode()));
                        processInfo.setStatusMessage("准备上传文件到RAGFlow");
                        processInfo.setChunkCount(0);
                        processInfo.setEnabledUse(false);
                        processInfo.setRagDocumentId(""); // 暂时为空，等待processPublishedKnowledgeBase填充
                        processInfo.setPreprocessedFilePath(filePath);
                        processInfo.setCreatedTime(LocalDateTime.now());
                        processInfo.setUpdatedTime(LocalDateTime.now());
                        processInfo.setCreatedUser(PtSecurityUtils.getUsername());
                        processInfo.setUpdatedUser(PtSecurityUtils.getUsername());
                        processInfo.setIsDeleted(false);
                        
                        documentProcessMapper.insert(processInfo);
                        log.info("已创建文档处理信息记录，准备上传文件");
                    }
                    
                    // 4.3 调用RAGFlow上传文件（这个方法会更新处理记录）
                    ragDocumentId = ragFlowClient.processPublishedKnowledgeBase(filePath, kgKnowledgeBasesId, spaceEntity, processInfo);

                    // 4.4 重新查询处理信息，获取RAGFlow文档ID

                    if (StringUtils.isBlank(ragDocumentId)) {
                        throw new KnowledgeBasesException("文件上传失败，未获取到RAGFlow文档ID");
                    }

                    log.info("文件上传成功，获取到RAGFlow文档ID：{}", ragDocumentId);
                }

                // 5. 更新状态为向量化中
                LambdaUpdateWrapper<KgBasesDocumentProcessInfoC> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                        .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                        .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false)
                        .set(KgBasesDocumentProcessInfoC::getProcessStatus, Integer.parseInt(ProcessStatusEnum.VECTORIZING.getCode()))
                        .set(KgBasesDocumentProcessInfoC::getStatusMessage, "向量化中...")
                        .set(KgBasesDocumentProcessInfoC::getUpdatedUser, PtSecurityUtils.getUsername())
                        .set(KgBasesDocumentProcessInfoC::getUpdatedTime, LocalDateTime.now());

                documentProcessMapper.update(null, updateWrapper);
                log.info("已更新文档状态为向量化中");

                // 6. 异步执行向量化（使用现有的线程池配置）
                String finalRagDocumentId = ragDocumentId;
                performVectorizationAsync(spaceEntity, finalRagDocumentId, spaceId, kgKnowledgeBasesId);

                return "向量化任务已启动，请稍后查看处理结果";
            }

        } catch (Exception e) {
            log.error("向量化文档时发生错误", e);
            throw new KnowledgeBasesException("向量化文档失败：" + e.getMessage());
        }
    }

    /**
     * 异步执行向量化操作
     * 
     * @param spaceEntity 知识空间实体
     * @param ragDocumentId RAGFlow文档ID
     * @param spaceId 知识空间ID
     * @param kgKnowledgeBasesId 文档ID
     */
    @Async("taskExecutor")
    public void performVectorizationAsync(KgBasesSpaceC spaceEntity, String ragDocumentId, Long spaceId, Long kgKnowledgeBasesId) {
        log.info("开始异步向量化，知识空间ID：{}，文档ID：{}，RAGFlow文档ID：{}", spaceId, kgKnowledgeBasesId, ragDocumentId);
        
        try {
            // 1. 调用 RAGFlow 向量化接口
            log.info("正在调用RAGFlow向量化接口...");
            
            // 这里假设有一个向量化接口，您需要根据实际的RAGFlow接口来调用
            // 比如：ragFlowClient.vectorizeDocument(ragDocumentId, spaceEntity.getRagDatasetId());
            
                         // 2. 调用真实的RAGFlow向量化接口
            boolean vectorizeSuccess = performRagFlowVectorization(ragDocumentId, spaceEntity.getRagFlowId(), spaceId, kgKnowledgeBasesId);
            
            if (vectorizeSuccess) {
                // 3. 向量化中，更新状态
                updateVectorizationStatus(spaceId, kgKnowledgeBasesId, ProcessStatusEnum.VECTORIZING, "向量化中", true);
                log.info("文档向量化完成，知识空间ID：{}，文档ID：{}", spaceId, kgKnowledgeBasesId);
            } else {
                // 4. 向量化失败，更新状态
                updateVectorizationStatus(spaceId, kgKnowledgeBasesId, ProcessStatusEnum.FAILED, "向量化失败", false);
                log.error("文档向量化失败，知识空间ID：{}，文档ID：{}", spaceId, kgKnowledgeBasesId);
            }
            
        } catch (Exception e) {
            log.error("异步向量化过程中发生错误，知识空间ID：{}，文档ID：{}", spaceId, kgKnowledgeBasesId, e);
            
            // 5. 异常情况，更新状态为失败
            updateVectorizationStatus(spaceId, kgKnowledgeBasesId, ProcessStatusEnum.FAILED,
                    "向量化过程中发生异常：" + e.getMessage(), false);
        }
    }

        /**
     * 执行RAGFlow向量化操作
     * 
     * @param ragDocumentId RAGFlow文档ID
     * @param datasetId 数据集ID
     * @param spaceId 知识空间ID
     * @param kgKnowledgeBasesId 知识库ID（文档ID）
     * @return 是否成功
     */
    private boolean performRagFlowVectorization(String ragDocumentId, String datasetId, Long spaceId, Long kgKnowledgeBasesId) {
        try {
            log.info("开始调用RAGFlow解析接口进行向量化，文档ID：{}，数据集ID：{}，知识空间ID：{}", ragDocumentId, datasetId, spaceId);

                        // 1. 查询配置信息，获取chunk_method等参数
            // 配置查询优先级：文档级配置 > 知识空间级配置 > 默认配置
            // 注意：查询需要三个参数才能唯一确定一条记录，因为同一个文档在不同知识空间下可能有不同配置
            String chunkMethod = "naive"; // 默认值
            Object parserConfig = null;
            
            try {
                // 1. 首先查询文档级别的配置
                // 参数：config_type=2(文档), relation_id=文档ID, space_id=知识空间ID
                KgBasesSpaceConfigC documentConfig = spaceConfigMapper.getByTypeRelationAndSpaceId(
                    ConfigTypeEnum.DOCUMENT.getCode(), kgKnowledgeBasesId, spaceId);
                KgBasesSpaceConfigC effectiveConfig = null;
                
                if (documentConfig != null) {
                    log.info("找到文档级配置，文档ID：{}，知识空间ID：{}", kgKnowledgeBasesId, spaceId);
                    effectiveConfig = documentConfig;
                } else {
                    // 2. 如果文档没有配置，查询知识空间级别的配置
                    // 参数：config_type=1(知识空间), relation_id=知识空间ID, space_id=知识空间ID
                    KgBasesSpaceConfigC spaceConfig = spaceConfigMapper.getByTypeRelationAndSpaceId(
                        ConfigTypeEnum.SPACE.getCode(), spaceId, spaceId);
                    if (spaceConfig != null) {
                        log.info("未找到文档级配置，使用知识空间级配置，知识空间ID：{}", spaceId);
                        effectiveConfig = spaceConfig;
                    } else {
                        log.warn("未找到文档和知识空间配置，使用默认配置，文档ID：{}，知识空间ID：{}", kgKnowledgeBasesId, spaceId);
                    }
                }
                
                if (effectiveConfig != null) {
                    // 使用有效配置中的切片方法
                    if (StringUtils.isNotBlank(effectiveConfig.getSliceMethod())) {
                        chunkMethod = effectiveConfig.getSliceMethod();
                    }
                    
                    // 根据配置构建parser_config
                    parserConfig = buildParserConfigFromSpaceConfig(chunkMethod, effectiveConfig);
                    log.info("使用配置构建parser_config，配置类型：{}，关联ID：{}，切片方法：{}", 
                            effectiveConfig.getConfigType() == 1 ? "知识空间" : "文档", 
                            effectiveConfig.getRelationId(), 
                            chunkMethod);
                } else {
                    // 3. 如果都没有配置，使用默认配置
                    parserConfig = buildParserConfig(chunkMethod);
                    log.info("使用默认配置构建parser_config，切片方法：{}", chunkMethod);
                }
            } catch (Exception e) {
                log.warn("获取配置失败，使用默认配置，文档ID：{}，知识空间ID：{}", kgKnowledgeBasesId, spaceId, e);
                parserConfig = buildParserConfig(chunkMethod);
            }

            // 2. 调用RAGFlow更新文档配置接口
            boolean configUpdateSuccess = ragFlowClient.parseDocument(
                    datasetId,
                    ragDocumentId,
                    null, // 文档名称，可选
                    chunkMethod,
                    parserConfig
            );

            if (!configUpdateSuccess) {
                log.error("更新RAGFlow文档配置失败，文档ID：{}，数据集ID：{}", ragDocumentId, datasetId);
                return false;
            }
            
            log.info("更新RAGFlow文档配置成功，文档ID：{}，数据集ID：{}", ragDocumentId, datasetId);

            // 3. 调用RAGFlow解析文档接口进行真正的解析
            Boolean parseSuccess = ragFlowClient.parseDocumentChunks(datasetId, Collections.singletonList(ragDocumentId));

            //todo 先异步
            if (Objects.isNull(parseSuccess) || parseSuccess) {
                log.info("RAGFlow文档解析成功，文档ID：{}，数据集ID：{}", ragDocumentId, datasetId);
                return true;
            } else {
                log.error("RAGFlow文档解析失败，文档ID：{}，数据集ID：{}", ragDocumentId, datasetId);
                return false;
            }

        } catch (Exception e) {
            log.error("调用RAGFlow向量化接口失败，文档ID：{}，数据集ID：{}", ragDocumentId, datasetId, e);
            return false;
        }
    }

    /**
     * 根据chunk_method构建parser_config
     * 
     * @param chunkMethod 分块方法
     * @return parser_config配置
     */
    private Object buildParserConfig(String chunkMethod) {
        if (StringUtils.isBlank(chunkMethod)) {
            chunkMethod = "naive";
        }
        
        Map<String, Object> config = new HashMap<>();
        
        switch (chunkMethod.toLowerCase()) {
            case "naive":
                // naive方法的完整配置
                config.put("auto_keywords", 0);
                config.put("auto_questions", 0);
                config.put("chunk_token_num", 128);
                config.put("delimiter", "\\n");
                config.put("html4excel", false);
                config.put("layout_recognize", "DeepDOC");
                config.put("tag_kb_ids", new ArrayList<>());
                config.put("task_page_size", 12);
                
                Map<String, Object> raptor = new HashMap<>();
                raptor.put("use_raptor", false);
                config.put("raptor", raptor);
                
                Map<String, Object> graphrag = new HashMap<>();
                graphrag.put("use_graphrag", false);
                config.put("graphrag", graphrag);
                break;
                
            case "qa":
            case "manual":
            case "paper":
            case "book":
            case "laws":
            case "presentation":
                // 这些方法只需要raptor配置
                Map<String, Object> raptorConfig = new HashMap<>();
                raptorConfig.put("use_raptor", false);
                config.put("raptor", raptorConfig);
                break;
                
            case "table":
            case "picture":
            case "one":
            case "email":
            case "tag":
                // 这些方法使用空配置
                config = new HashMap<>();
                break;
                
            default:
                // 未知方法，使用默认的naive配置
                log.warn("未知的chunk_method：{}，使用默认naive配置", chunkMethod);
                return buildParserConfig("naive");
        }
        
        return config;
    }

    /**
     * 根据知识空间配置构建parser_config
     * 
     * @param chunkMethod 分块方法
     * @param spaceConfig 知识空间配置
     * @return parser_config配置
     */
    private Object buildParserConfigFromSpaceConfig(String chunkMethod, KgBasesSpaceConfigC spaceConfig) {
        if (StringUtils.isBlank(chunkMethod)) {
            chunkMethod = "naive";
        }
        
        Map<String, Object> config = new HashMap<>();
        
        switch (chunkMethod.toLowerCase()) {
            case "naive":
                // naive方法的配置，从数据库配置中读取
                config.put("auto_keywords", spaceConfig.getAutoKeywordCount() != null ? spaceConfig.getAutoKeywordCount() : 0);
                config.put("auto_questions", spaceConfig.getAutoQuestionCount() != null ? spaceConfig.getAutoQuestionCount() : 0);
                config.put("chunk_token_num", spaceConfig.getTextBlockSize() != null ? spaceConfig.getTextBlockSize() : 128);
                
                // 文本分段标识符
                String delimiter = spaceConfig.getTextSegmentSeparator();
                if (StringUtils.isBlank(delimiter)) {
                    delimiter = "\\n";
                }
                config.put("delimiter", delimiter);
                
                config.put("html4excel", false);
                config.put("layout_recognize", "DeepDOC");
                
                // 标签集合
                if (StringUtils.isNotBlank(spaceConfig.getTagSet())) {
                    try {
                        /*// 如果tagSet是逗号分隔的字符串，转换为列表
                        String[] tags = spaceConfig.getTagSet().split(",");
                        List<String> tagList = new ArrayList<>();
                        for (String tag : tags) {
                            if (StringUtils.isNotBlank(tag.trim())) {
                                tagList.add(tag.trim());
                            }
                        }
                        config.put("tag_kb_ids", tagList);*/
                        //tood 标签先不设置 等ai组

                        config.put("tag_kb_ids", new ArrayList<>());
                    } catch (Exception e) {
                        log.warn("解析tagSet失败，使用空列表", e);
                        config.put("tag_kb_ids", new ArrayList<>());
                    }
                } else {
                    config.put("tag_kb_ids", new ArrayList<>());
                }
                
                config.put("task_page_size", 12); // 默认值
                
                Map<String, Object> raptor = new HashMap<>();
                raptor.put("use_raptor", false);
                config.put("raptor", raptor);
                
                Map<String, Object> graphrag = new HashMap<>();
                graphrag.put("use_graphrag", false);
                config.put("graphrag", graphrag);
                break;
                
            case "qa":
            case "manual":
            case "paper":
            case "book":
            case "laws":
            case "presentation":
                // 这些方法只需要raptor配置
                Map<String, Object> raptorConfig = new HashMap<>();
                raptorConfig.put("use_raptor", false);
                config.put("raptor", raptorConfig);
                break;
                
            case "table":
            case "picture":
            case "one":
            case "email":
            case "tag":
                // 这些方法使用空配置
                config = new HashMap<>();
                break;
                
            default:
                // 未知方法，使用默认的naive配置
                log.warn("未知的chunk_method：{}，使用默认naive配置", chunkMethod);
                return buildParserConfig("naive");
        }
        
        log.info("根据知识空间配置构建parser_config成功，chunk_method：{}，配置：{}", chunkMethod, config);
        return config;
    }

    /**
     * 更新向量化状态
     * 
     * @param spaceId 知识空间ID
     * @param kgKnowledgeBasesId 文档ID
     * @param status 状态枚举
     * @param message 状态消息
     * @param enabledUse 是否启用使用
     */
    private void updateVectorizationStatus(Long spaceId, Long kgKnowledgeBasesId, ProcessStatusEnum status,
                                         String message, boolean enabledUse) {
        try {
            LambdaUpdateWrapper<KgBasesDocumentProcessInfoC> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceId)
                    .eq(KgBasesDocumentProcessInfoC::getKgKnowledgeBasesId, kgKnowledgeBasesId)
                    .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false)
                    .set(KgBasesDocumentProcessInfoC::getProcessStatus, Integer.parseInt(status.getCode()))
                    .set(KgBasesDocumentProcessInfoC::getStatusMessage, message)
                    .set(KgBasesDocumentProcessInfoC::isEnabledUse, enabledUse)
                    .set(KgBasesDocumentProcessInfoC::getUpdatedUser, PtSecurityUtils.getUsername())
                    .set(KgBasesDocumentProcessInfoC::getUpdatedTime, LocalDateTime.now());

            int updateCount = documentProcessMapper.update(null, updateWrapper);
            if (updateCount > 0) {
                log.info("向量化状态更新成功，状态：{}，消息：{}", status.getDesc(), message);
            } else {
                log.warn("向量化状态更新失败，未找到对应记录");
            }
            
        } catch (Exception e) {
            log.error("更新向量化状态时发生错误", e);
        }
    }
    
    @Override
    public PageInfo<KgBasesDocumentSliceVO> getDocumentSlices(KgBasesDocumentSliceQueryDTO queryDTO) {
        log.info("获取文档切片，参数：{}", queryDTO);

        try {
            // 分页查询切片列表
            Page<KgBasesDocumentSliceD> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());

            // 执行查询
            LambdaQueryWrapper<KgBasesDocumentSliceD> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KgBasesDocumentSliceD::getKgBasesSpaceCId, queryDTO.getSpaceId())
                   .eq(KgBasesDocumentSliceD::getDocumentProcessId, queryDTO.getDocumentProcessId())
                   .eq(KgBasesDocumentSliceD::getIsDeleted, false)
                   .orderByAsc(KgBasesDocumentSliceD::getSliceIndex);

            // 添加关键词搜索条件
            if (StringUtils.isNotBlank(queryDTO.getKeyword())) {
                wrapper.and(w -> w
                        .like(KgBasesDocumentSliceD::getSliceContent, queryDTO.getKeyword())
                        .or()
                        .like(KgBasesDocumentSliceD::getChapterTitle, queryDTO.getKeyword())
                        .or()
                        .like(KgBasesDocumentSliceD::getAutoKeywords, queryDTO.getKeyword())
                        .or()
                        .like(KgBasesDocumentSliceD::getManualKeywords, queryDTO.getKeyword())
                );
            }

            Page<KgBasesDocumentSliceD> resultPage = documentSliceMapper.selectPage(page, wrapper);

            // 转换为VO
            List<KgBasesDocumentSliceVO> sliceList = new ArrayList<>();
            for (KgBasesDocumentSliceD slice : resultPage.getRecords()) {
                KgBasesDocumentSliceVO vo = new KgBasesDocumentSliceVO();
                BeanUtils.copyProperties(slice, vo);
                sliceList.add(vo);
            }

            // 构建分页结果
            PageInfo<KgBasesDocumentSliceVO> pageInfo = new PageInfo<>();
            pageInfo.setList(sliceList);
            pageInfo.setTotal(resultPage.getTotal());
            pageInfo.setPageNum(Integer.valueOf(String.valueOf(resultPage.getCurrent())));
            pageInfo.setPageSize(Integer.valueOf(String.valueOf(resultPage.getSize())));
            pageInfo.setPages(Integer.valueOf(String.valueOf(resultPage.getPages())));

            return pageInfo;
        } catch (Exception e) {
            log.error("获取文档切片失败", e);
            throw new KnowledgeBasesException("获取文档切片失败：" + e.getMessage());
        }
    }
    
    @Override
    public List<String> extractSliceKeywords(KgBasesSliceKeywordDTO keywordDTO) {
        log.info("提取切片关键词，参数：{}", keywordDTO);
        
        try {
            // 调用RAGFlow服务提取关键词
            return ragFlowClient.extractKeywords(keywordDTO.getSliceContent(), keywordDTO.getCount());
        } catch (Exception e) {
            log.error("提取切片关键词失败", e);
            throw new KnowledgeBasesException("提取切片关键词失败：" + e.getMessage());
        }
    }
    
    @Override
    public List<String> extractSliceQuestions(KgBasesSliceQuestionDTO questionDTO) {
        log.info("提取切片问题，参数：{}", questionDTO);
        
        try {
            // 调用RAGFlow服务提取问题
            return ragFlowClient.extractQuestions(questionDTO.getSliceContent(), questionDTO.getCount());
        } catch (Exception e) {
            log.error("提取切片问题失败", e);
            throw new KnowledgeBasesException("提取切片问题失败：" + e.getMessage());
        }
    }
    
    @Override
    public PageInfo<KgBasesRecallHistoryVO> getRecallHistory(KgBasesRecallHistoryQueryDTO queryDTO) {
        log.info("获取召回历史记录，参数：{}", queryDTO);
        
        try {
            // 分页查询召回历史记录
            Page<KgBasesRecallHistoryVO> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
            
            // 执行查询
            List<KgBasesRecallHistoryVO> historyList = recallTestMapper.selectRecallHistoryList(
                    page, 
                    queryDTO.getSpaceId(), 
                    queryDTO.getQuery(),
                    queryDTO.getRetrievalMethod(),
                    queryDTO.getDataSource()
            );
            
            // 构建分页结果
            PageInfo<KgBasesRecallHistoryVO> pageInfo = new PageInfo<>();
            pageInfo.setList(historyList);
            pageInfo.setTotal(page.getTotal());
            pageInfo.setPageNum((int) page.getCurrent());
            pageInfo.setPageSize((int) page.getSize());
            pageInfo.setPages((int) page.getPages());
            
            return pageInfo;
        } catch (Exception e) {
            log.error("获取召回历史记录失败", e);
            throw new KnowledgeBasesException("获取召回历史记录失败：" + e.getMessage());
        }
    }
    
    @Override
    public KgBasesRecallTestDTO getUserRecallConfig(Long spaceId) {
        log.info("获取用户最近召回配置，知识空间ID：{}", spaceId);
        
        try {
            // 获取当前用户
            String loginName = PtSecurityUtils.getUsername();
            
            // 查询用户最近召回配置
            KgBasesUserRecallConfigC userConfig = userRecallConfigMapper.selectOne(
                    new LambdaQueryWrapper<KgBasesUserRecallConfigC>()
                            .eq(KgBasesUserRecallConfigC::getLoginName, loginName)
                            .eq(KgBasesUserRecallConfigC::getKgBasesSpaceCId, spaceId)
                            .eq(KgBasesUserRecallConfigC::getIsDeleted, false)
            );
            
            if (userConfig == null) {
                // 如果没有用户配置，返回默认配置
                KgBasesRecallTestDTO defaultConfig = new KgBasesRecallTestDTO();
                defaultConfig.setSpaceId(spaceId);
                defaultConfig.setRetrievalMethod(RetrievalMethodEnum.VECTOR.getCode());
                defaultConfig.setTopk(5);
                defaultConfig.setThreshold(new BigDecimal("0.7"));
                defaultConfig.setRerankEnabled(false);
                return defaultConfig;
            }
            
            // 转换为DTO
            KgBasesRecallTestDTO testDTO = new KgBasesRecallTestDTO();
            testDTO.setSpaceId(spaceId);
            testDTO.setRetrievalMethod(userConfig.getRetrievalMethod());
            
            // 解析配置JSON
            JsonNode node = userConfig.getRetrievalConfig();
            if (node != null && !node.isNull()) {
                if (node.has("topk")) {
                    testDTO.setTopk(node.get("topk").asInt());
                }
                if (node.has("threshold")) {
                    testDTO.setThreshold(new BigDecimal(node.get("threshold").asText()));
                }
                if (node.has("rerankEnabled")) {
                    testDTO.setRerankEnabled(node.get("rerankEnabled").asBoolean());
                }
                if (node.has("rerankModel")) {
                    testDTO.setRerankModel(node.get("rerankModel").asText());
                }
                if (node.has("hybridVectorWeight")) {
                    testDTO.setHybridVectorWeight(new BigDecimal(node.get("hybridVectorWeight").asText()));
                }
                if (node.has("hybridFulltextWeight")) {
                    testDTO.setHybridFulltextWeight(new BigDecimal(node.get("hybridFulltextWeight").asText()));
                }
            }
            
            return testDTO;
        } catch (Exception e) {
            log.error("获取用户最近召回配置失败", e);
            throw new KnowledgeBasesException("获取用户最近召回配置失败：" + e.getMessage());
        }
    }
    
    @Override
    public boolean saveUserRecallConfig(KgBasesRecallUserDTO testDTO) {
        log.info("保存用户召回配置，参数：{}", testDTO);

        try {
            // 获取当前用户（这里应该从上下文中获取）
            String loginName = "admin";

            // 查询用户最近召回配置
            KgBasesUserRecallConfigC userConfig = userRecallConfigMapper.selectOne(
                    new LambdaQueryWrapper<KgBasesUserRecallConfigC>()
                            .eq(KgBasesUserRecallConfigC::getLoginName, loginName)
                            .eq(KgBasesUserRecallConfigC::getKgBasesSpaceCId, testDTO.getSpaceId())
                            .eq(KgBasesUserRecallConfigC::getIsDeleted, false)
            );

            // 使用 ObjectMapper 构建 JsonNode
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode configNode = objectMapper.createObjectNode();

            // 填充配置
            configNode.put("topk", testDTO.getTopk());
            configNode.put("threshold", testDTO.getThreshold().toString()); // BigDecimal 转 String
            configNode.put("rerankEnabled", testDTO.getRerankEnabled());

            if (testDTO.getRerankEnabled() && StringUtils.isNotBlank(testDTO.getRerankModel())) {
                configNode.put("rerankModel", testDTO.getRerankModel());
            }

            if (RetrievalMethodEnum.HYBRID.getCode().equals(testDTO.getRetrievalMethod())) {
                configNode.put("hybridVectorWeight", testDTO.getHybridVectorWeight());
                configNode.put("hybridFulltextWeight", testDTO.getHybridFulltextWeight());
            }

            if (userConfig == null) {
                // 创建新配置
                userConfig = new KgBasesUserRecallConfigC();
                userConfig.setLoginName(loginName);
                userConfig.setKgBasesSpaceCId(testDTO.getSpaceId());
                userConfig.setRetrievalMethod(testDTO.getRetrievalMethod());
                userConfig.setRetrievalConfig(configNode); // 直接存储 JsonNode
                userConfig.setCreatedTime(LocalDateTime.now());
                userConfig.setUpdatedTime(LocalDateTime.now());
                userConfig.setIsDeleted(false);
                userRecallConfigMapper.insert(userConfig);
            } else {
                // 更新配置
                userConfig.setRetrievalMethod(testDTO.getRetrievalMethod());
                userConfig.setRetrievalConfig(configNode); // 直接存储 JsonNode
                userConfig.setUpdatedTime(LocalDateTime.now());
                userRecallConfigMapper.updateById(userConfig);
            }

            return true;
        } catch (Exception e) {
            log.error("保存用户召回配置失败", e);
            throw new KnowledgeBasesException("保存用户召回配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer importDocumentSlices(KgBasesDocumentSliceImportDTO importDTO) throws KnowledgeBasesException {
        log.info("导入文档切片，参数：{}", importDTO.getDoc().getId());
        
        // 记录AI接口调用开始
        KgBasesAiCallLogD logRecord = aiCallLogService.recordCallStart(
            AiCallTypeEnum.SLICE_IMPORT,
            null, // spaceId 后面获取
            null, // documentId 后面获取
            importDTO,
            "AI组-切片导入"
        );
        
        try {
            // 1. 根据dataset_id查询知识空间
            String datasetId = importDTO.getDoc().getDataset_id();
            if (StringUtils.isEmpty(datasetId)) {
                datasetId = importDTO.getChunks().get(0).getDataset_id();
            }
            LambdaQueryWrapper<KgBasesSpaceC> spaceQueryWrapper = new LambdaQueryWrapper<>();
            spaceQueryWrapper.eq(KgBasesSpaceC::getRagFlowId, datasetId)
                    .eq(KgBasesSpaceC::getIsDeleted, false);
            KgBasesSpaceC spaceEntity = spaceMapper.selectOne(spaceQueryWrapper);
            
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在，RAG Flow ID: " + datasetId);
            }
            
            // 2. 根据document_id查询文档处理信息
            String documentId = importDTO.getDoc().getId();
            LambdaQueryWrapper<KgBasesDocumentProcessInfoC> docQueryWrapper = new LambdaQueryWrapper<>();
            docQueryWrapper.eq(KgBasesDocumentProcessInfoC::getRagDocumentId, documentId)
                    .eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceEntity.getKgBasesSpaceCId())
                    .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false);
            KgBasesDocumentProcessInfoC docEntity = documentProcessMapper.selectOne(docQueryWrapper);
            
            if (docEntity == null) {
                throw new KnowledgeBasesException("文档处理信息不存在，RAG Document ID: " + documentId);
            }
            
            // 3. 先删除已存在的切片记录
            LambdaQueryWrapper<KgBasesDocumentSliceD> sliceDeleteWrapper = new LambdaQueryWrapper<>();
            sliceDeleteWrapper.eq(KgBasesDocumentSliceD::getDocumentProcessId, docEntity.getId())
                    .eq(KgBasesDocumentSliceD::getKgBasesSpaceCId, spaceEntity.getKgBasesSpaceCId());
            documentSliceMapper.delete(sliceDeleteWrapper);
            
            // 4. 批量插入切片记录
            List<KgBasesDocumentSliceImportDTO.ChunkDTO> chunks = importDTO.getChunks();
            List<KgBasesDocumentSliceD> sliceEntities = new ArrayList<>();
            String currentUser = PtSecurityUtils.getUsername();
            LocalDateTime now = LocalDateTime.now();
            
            for (int i = 0; i < chunks.size(); i++) {
                KgBasesDocumentSliceImportDTO.ChunkDTO chunk = chunks.get(i);
                KgBasesDocumentSliceD sliceEntity = new KgBasesDocumentSliceD();
                
                sliceEntity.setKgBasesSpaceCId(spaceEntity.getKgBasesSpaceCId());
                sliceEntity.setKgKnowledgeBasesId(docEntity.getKgKnowledgeBasesId());
                sliceEntity.setDocumentProcessId(docEntity.getId());
                sliceEntity.setRagDocumentId(documentId);
                sliceEntity.setSliceIndex(i);
                sliceEntity.setSliceContent(chunk.getContent());
                sliceEntity.setSliceCount(chunk.getContent().length());
                
                // 设置关键词和问题
                if (CollectionUtils.isNotEmpty(chunk.getImportant_keywords())) {
                    sliceEntity.setAutoKeywords(String.join(",", chunk.getImportant_keywords()));
                }
                
                if (CollectionUtils.isNotEmpty(chunk.getQuestions())) {
                    sliceEntity.setAutoQuestions(String.join(",", chunk.getQuestions()));
                }
                
                sliceEntity.setVectorId(chunk.getId());
                sliceEntity.setIsSyncedToRagflow(true);
                sliceEntity.setSyncTime(now);
                sliceEntity.setCreatedUser(currentUser);
                sliceEntity.setCreatedTime(now);
                sliceEntity.setUpdatedUser(currentUser);
                sliceEntity.setUpdatedTime(now);
                sliceEntity.setIsDeleted(false);
                
                sliceEntities.add(sliceEntity);
            }
            
            // 批量插入切片
            int count = batchInsertSlices(sliceEntities);
            
            // 5. 更新文档处理信息
            docEntity.setChunkCount(count);
            docEntity.setUpdatedUser(PtSecurityUtils.getUsername());
            docEntity.setUpdatedTime(LocalDateTime.now());
            documentProcessMapper.updateById(docEntity);
            
            // 6. 异步同步问答对和专业词汇
            asyncSyncQaPairsAndTerms(spaceEntity, docEntity, chunks);
            
            // 更新日志记录中的spaceId和documentId
            logRecord.setSpaceId(spaceEntity.getKgBasesSpaceCId());
            logRecord.setDocumentId(docEntity.getKgKnowledgeBasesId());
            
            // 记录AI接口调用成功
            aiCallLogService.recordCallSuccess(logRecord, chunks.size(), count, 
                "成功导入" + count + "个切片");
            
            return count;
        } catch (KnowledgeBasesException e) {
            log.error("导入文档切片失败", e);
            // 记录AI接口调用失败
            aiCallLogService.recordCallFailure(logRecord, e.getMessage(), 
                importDTO.getChunks() != null ? importDTO.getChunks().size() : 0, 0);
            throw e;
        } catch (Exception e) {
            log.error("导入文档切片失败", e);
            // 记录AI接口调用失败
            aiCallLogService.recordCallFailure(logRecord, e.getMessage(), 
                importDTO.getChunks() != null ? importDTO.getChunks().size() : 0, 0);
            throw new KnowledgeBasesException("导入文档切片失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDocumentProcessStatus(KgBasesDocumentStatusUpdateDTO statusUpdateDTO) throws KnowledgeBasesException {
        log.info("更新文档处理状态，参数：{}", statusUpdateDTO);
        
        // 记录AI接口调用开始
        KgBasesAiCallLogD logRecord = aiCallLogService.recordCallStart(
            AiCallTypeEnum.STATUS_UPDATE,
            null, // spaceId 后面获取
            null, // documentId 后面获取
            statusUpdateDTO,
            "AI组-状态更新"
        );

        try {
            // 1. 根据dataset_id查询知识空间
            String datasetId = statusUpdateDTO.getDataset_id();
            LambdaQueryWrapper<KgBasesSpaceC> spaceQueryWrapper = new LambdaQueryWrapper<>();
            spaceQueryWrapper.eq(KgBasesSpaceC::getRagFlowId, datasetId)
                    .eq(KgBasesSpaceC::getIsDeleted, false);
            KgBasesSpaceC spaceEntity = spaceMapper.selectOne(spaceQueryWrapper);

            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在，RAG Flow ID: " + datasetId);
            }

            // 2. 根据document_id查询文档处理信息
            String documentId = statusUpdateDTO.getDoc_id();
            LambdaQueryWrapper<KgBasesDocumentProcessInfoC> docQueryWrapper = new LambdaQueryWrapper<>();
            docQueryWrapper.eq(KgBasesDocumentProcessInfoC::getRagDocumentId, documentId)
                    .eq(KgBasesDocumentProcessInfoC::getKgBasesSpaceCId, spaceEntity.getKgBasesSpaceCId())
                    .eq(KgBasesDocumentProcessInfoC::getIsDeleted, false);
            KgBasesDocumentProcessInfoC docEntity = documentProcessMapper.selectOne(docQueryWrapper);

            if (docEntity == null) {
                throw new KnowledgeBasesException("文档处理信息不存在，RAG Document ID: " + documentId);
            }

            // 3. 状态转换和更新处理状态
            String statusMessage = statusUpdateDTO.getStatus_message();
            Integer processStatus = null;
            String mappingLog = "";

            // 优先使用rag_status进行状态转换
            if (statusUpdateDTO.getRag_status() != null) {
                processStatus = StatusMappingUtil.mapRagStatusToProcessStatus(statusUpdateDTO.getRag_status());
                mappingLog = StatusMappingUtil.buildStatusMappingLog(statusUpdateDTO.getRag_status(), processStatus);
                log.info("状态转换：{}", mappingLog);
            } 
            // 如果没有rag_status，使用原有的process_status
            else if (statusUpdateDTO.getProcess_status() != null) {
                processStatus = statusUpdateDTO.getProcess_status();
                log.info("使用原有process_status：{}", processStatus);
            }
            // 都没有则抛出异常
            else {
                throw new KnowledgeBasesException("rag_status和process_status不能都为空");
            }

            docEntity.setProcessStatus(processStatus);

            // 构建状态消息（包含转换信息）
            StringBuilder messageBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(mappingLog)) {
                messageBuilder.append(mappingLog);
            }
            if (StringUtils.isNotBlank(statusMessage)) {
                if (messageBuilder.length() > 0) {
                    messageBuilder.append(" | ");
                }
                messageBuilder.append(statusMessage);
            }

            // 追加状态消息
            if (messageBuilder.length() > 0) {
                String currentMessage = docEntity.getRagMessage();
                if (StringUtils.isNotBlank(currentMessage)) {
                    docEntity.setRagMessage(currentMessage + "\n" + messageBuilder.toString());
                } else {
                    docEntity.setRagMessage(messageBuilder.toString());
                }
            }

            docEntity.setUpdatedUser(PtSecurityUtils.getUsername());
            docEntity.setUpdatedTime(LocalDateTime.now());
            documentProcessMapper.updateById(docEntity);

            // 更新日志记录中的spaceId和documentId
            logRecord.setSpaceId(spaceEntity.getKgBasesSpaceCId());
            logRecord.setDocumentId(docEntity.getKgKnowledgeBasesId());
            
            // 记录AI接口调用成功
            String successMessage = StringUtils.isNotBlank(mappingLog) ? 
                "成功更新文档状态，" + mappingLog :
                "成功更新文档状态：" + processStatus;
            aiCallLogService.recordCallSuccess(logRecord, 1, 1, successMessage);

            return true;
        } catch (KnowledgeBasesException e) {
            log.error("更新文档处理状态失败", e);
            // 记录AI接口调用失败
            aiCallLogService.recordCallFailure(logRecord, e.getMessage(), 1, 0);
            throw e;
        } catch (Exception e) {
            log.error("更新文档处理状态失败", e);
            // 记录AI接口调用失败
            aiCallLogService.recordCallFailure(logRecord, e.getMessage(), 1, 0);
            throw new KnowledgeBasesException("更新文档处理状态失败：" + e.getMessage());
        }
    }



    @Override
    public Long recallTestAndSave(KgBasesRecallTestDTO testDTO) {
        log.info("召回测试并保存历史，知识空间ID：{}，测试参数：{}", testDTO.getSpaceId(), testDTO);
        String dataSource = StringUtils.isBlank(testDTO.getDataSource()) ? DataSourceEnum.TEST.getCode() : testDTO.getDataSource();
        
        try {
            // 1. 查询知识空间
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(testDTO.getSpaceId());
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("知识空间不存在");
            }
            
            // 2. 调用RAGFlow服务进行召回测试
            KgBasesRagFlowResVO result = ragFlowClient.testRecall(spaceEntity.getRagFlowId(), testDTO);
            if (Objects.nonNull(result) && result.getCode() == 0) {
                KgBasesRagFlowResVO.Data resData = result.getData();

                // 3. 保存测试历史
                KgBasesRecallTestHistoryD history = new KgBasesRecallTestHistoryD();
                history.setKgBasesSpaceCId(testDTO.getSpaceId());
                history.setQuery(testDTO.getQuery());
                history.setRetrievalMethod(testDTO.getRetrievalMethod());
                history.setTopK(testDTO.getTopk());
                history.setThreshold(testDTO.getThreshold());
                history.setHybridFulltextWeight(testDTO.getHybridVectorWeight());
                history.setKeywordEnabled(testDTO.getRetrievalMethod().equals(RetrievalMethodEnum.FULLTEXT.getCode()));
                history.setRerankEnabled(testDTO.getRerankEnabled());
                history.setRerankModel(testDTO.getRerankModel());
                history.setResultCount(CollectionUtils.isNotEmpty(resData.getDoc_aggs()) ? resData.getDoc_aggs().get(0).getCount() : 0);
                history.setDataSource(dataSource); // 设置数据来源为测试
                history.setCreatedTime(LocalDateTime.now());
                history.setCreatedUser(PtSecurityUtils.getUsername());
                history.setIsDeleted(false);

                recallTestHistoryMapper.insert(history);

                if (CollectionUtils.isNotEmpty(resData.getChunks())) {
                    for (KgBasesRagFlowResVO.Chunk chunk : resData.getChunks()) {
                        KgBasesRecallTestResultD resultEntity = new KgBasesRecallTestResultD();
                        resultEntity.setHistoryId(history.getId());
                        resultEntity.setChunkId(chunk.getId());
                        resultEntity.setContent(chunk.getContent());
                        resultEntity.setContentLtks(chunk.getContent_ltks());
                        resultEntity.setDocumentId(chunk.getDocument_id());
                        resultEntity.setDocumentName(chunk.getDocument_keyword());
                        resultEntity.setHighlight(chunk.getHighlight());
                        resultEntity.setImageId(chunk.getImage_id());
                        resultEntity.setKbId(chunk.getKb_id());

                        // 处理关键词
                        if (CollectionUtils.isNotEmpty(chunk.getImportant_keywords())) {
                            resultEntity.setImportantKeywords(String.join(",", chunk.getImportant_keywords()));
                        }

                        // 处理位置信息
                        if (chunk.getPositions() != null && !chunk.getPositions().isEmpty()) {
                            resultEntity.setPositions(String.join(",", chunk.getPositions()));
                        }

                        resultEntity.setSimilarity(chunk.getSimilarity());
                        resultEntity.setTermSimilarity(chunk.getTerm_similarity());
                        resultEntity.setVectorSimilarity(chunk.getVector_similarity());

                        resultEntity.setCreatedTime(LocalDateTime.now());
                        resultEntity.setCreatedUser(PtSecurityUtils.getUsername()); // 可以替换为实际用户
                        resultEntity.setIsDeleted(false);

                        recallTestResultMapper.insert(resultEntity);
                    }
                }

                return history.getId();
            }
            return 0L;
        } catch (Exception e) {
            log.error("召回测试失败", e);
            throw new KnowledgeBasesException("召回测试失败：" + e.getMessage());
        }
    }

    @Override
    public PageInfo<KgBasesRecallTestResultD> pageRecallResult(RecallResultPageQueryDTO queryDTO) {
        log.info("分页获取召回结果，参数：{}", queryDTO);
        
        try {
            // 分页查询召回结果
            Page<KgBasesRecallTestResultD> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
            
            // 执行查询
            LambdaQueryWrapper<KgBasesRecallTestResultD> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KgBasesRecallTestResultD::getHistoryId, queryDTO.getHistoryId())
                   .eq(KgBasesRecallTestResultD::getIsDeleted, false)
                   .orderByAsc(KgBasesRecallTestResultD::getChunkId);

            Page<KgBasesRecallTestResultD> resultPage = recallTestResultMapper.selectPage(page, wrapper);


            // 构建分页结果
            PageInfo<KgBasesRecallTestResultD> pageInfo = new PageInfo<>();
            pageInfo.setList(resultPage.getRecords());
            pageInfo.setTotal(resultPage.getTotal());
            pageInfo.setPageNum(Integer.parseInt(String.valueOf(resultPage.getCurrent())));
            pageInfo.setPageSize(Integer.parseInt(String.valueOf(resultPage.getSize())));
            pageInfo.setPages(Integer.parseInt(String.valueOf(resultPage.getPages())));

            return pageInfo;
        } catch (Exception e) {
            log.error("分页获取召回结果失败", e);
            throw new KnowledgeBasesException("分页获取召回结果失败：" + e.getMessage());
        }
    }

    @Override
    public PageInfo<KgBasesRecallTestHistoryD> pageRecallHistory(RecallHistoryPageQueryDTO queryDTO) {
        log.info("分页获取召回历史，参数：{}", queryDTO);
        
        try {
            // 分页查询召回历史记录
            Page<KgBasesRecallTestHistoryD> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
            
            // 执行查询
            LambdaQueryWrapper<KgBasesRecallTestHistoryD> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(KgBasesRecallTestHistoryD::getKgBasesSpaceCId, queryDTO.getKgBasesSpaceCId())
                   .eq(KgBasesRecallTestHistoryD::getIsDeleted, false)
                   .orderByDesc(KgBasesRecallTestHistoryD::getCreatedTime);

            Page<KgBasesRecallTestHistoryD> resultPage = recallTestHistoryMapper.selectPage(page, wrapper);

            // 转换为VO
            List<KgBasesRecallTestHistoryD> historyList = new ArrayList<>();
            for (KgBasesRecallTestHistoryD historyEntity : resultPage.getRecords()) {
                KgBasesRecallTestHistoryD history = new KgBasesRecallTestHistoryD();
                BeanUtils.copyProperties(historyEntity, history);
                historyList.add(history);
            }

            // 构建分页结果
            PageInfo<KgBasesRecallTestHistoryD> pageInfo = new PageInfo<>();
            pageInfo.setList(historyList);
            pageInfo.setTotal(resultPage.getTotal());
            pageInfo.setPageNum(Integer.parseInt(String.valueOf(resultPage.getCurrent())));
            pageInfo.setPageSize(Integer.parseInt(String.valueOf(resultPage.getSize())));
            pageInfo.setPages(Integer.parseInt(String.valueOf(resultPage.getPages())));

            return pageInfo;
        } catch (Exception e) {
            log.error("分页获取召回历史失败", e);
            throw new KnowledgeBasesException("分页获取召回历史失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRecallTestHistory(Long historyId) throws KnowledgeBasesException {
        log.info("开始删除召回测试历史记录，历史ID: {}", historyId);
        
        try {
            // 1. 参数验证
            if (historyId == null || historyId <= 0) {
                throw new KnowledgeBasesException("历史记录ID不能为空或无效");
            }
            
            // 2. 查询历史记录是否存在
            KgBasesRecallTestHistoryD historyRecord = recallTestHistoryMapper.selectById(historyId);
            if (historyRecord == null || Boolean.TRUE.equals(historyRecord.getIsDeleted())) {
                throw new KnowledgeBasesException("历史记录不存在或已被删除");
            }
            
            // 3. 权限校验：验证当前用户是否有权限删除此记录
            String currentUser = PtSecurityUtils.getUsername();
            validateDeletePermission(historyRecord, currentUser);
            
            // 4. 查询并统计关联的测试结果数量
            LambdaQueryWrapper<KgBasesRecallTestResultD> resultQueryWrapper = new LambdaQueryWrapper<>();
            resultQueryWrapper.eq(KgBasesRecallTestResultD::getHistoryId, historyId)
                    .eq(KgBasesRecallTestResultD::getIsDeleted, false);
            Integer resultCount = recallTestResultMapper.selectCount(resultQueryWrapper);
            
            // 5. 批量逻辑删除测试结果
            if (resultCount > 0) {
                LambdaUpdateWrapper<KgBasesRecallTestResultD> resultUpdateWrapper = new LambdaUpdateWrapper<>();
                resultUpdateWrapper.eq(KgBasesRecallTestResultD::getHistoryId, historyId)
                        .eq(KgBasesRecallTestResultD::getIsDeleted, false)
                        .set(KgBasesRecallTestResultD::getIsDeleted, true)
                        .set(KgBasesRecallTestResultD::getUpdatedUser, currentUser)
                        .set(KgBasesRecallTestResultD::getUpdatedTime, LocalDateTime.now());
                
                int deletedResultCount = recallTestResultMapper.update(null, resultUpdateWrapper);
                if (deletedResultCount == 0) {
                    throw new KnowledgeBasesException("删除关联测试结果失败");
                }
                
                log.info("成功删除关联测试结果，数量: {}", deletedResultCount);
            }
            
            // 6. 逻辑删除历史记录
            LambdaUpdateWrapper<KgBasesRecallTestHistoryD> historyUpdateWrapper = new LambdaUpdateWrapper<>();
            historyUpdateWrapper.eq(KgBasesRecallTestHistoryD::getId, historyId)
                    .eq(KgBasesRecallTestHistoryD::getIsDeleted, false)
                    .set(KgBasesRecallTestHistoryD::getIsDeleted, true)
                    .set(KgBasesRecallTestHistoryD::getUpdatedUser, currentUser)
                    .set(KgBasesRecallTestHistoryD::getUpdatedTime, LocalDateTime.now());
            
            int deletedHistoryCount = recallTestHistoryMapper.update(null, historyUpdateWrapper);
            if (deletedHistoryCount == 0) {
                throw new KnowledgeBasesException("删除历史记录失败，可能已被其他用户删除");
            }
            
            log.info("成功删除召回测试历史记录，历史ID: {}，关联结果数量: {}", historyId, resultCount);
            return true;
            
        } catch (KnowledgeBasesException e) {
            log.error("删除召回测试历史记录失败，历史ID: {}，错误信息: {}", historyId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("删除召回测试历史记录异常，历史ID: {}", historyId, e);
            throw new KnowledgeBasesException("删除召回测试历史记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证删除权限
     * 
     * @param historyRecord 历史记录
     * @param currentUser 当前用户
     * @throws KnowledgeBasesException 权限不足时抛出异常
     */
    private void validateDeletePermission(KgBasesRecallTestHistoryD historyRecord, String currentUser) 
            throws KnowledgeBasesException {
        
        // 验证用户是否为记录创建者
        if (!currentUser.equals(historyRecord.getCreatedUser())) {
            // 如果不是创建者，检查是否为知识空间的管理员或拥有者
            KgBasesSpaceC spaceEntity = spaceMapper.selectById(historyRecord.getKgBasesSpaceCId());
            if (spaceEntity == null) {
                throw new KnowledgeBasesException("关联的知识空间不存在");
            }
            
            // 如果不是空间创建者，则权限不足
            if (!currentUser.equals(spaceEntity.getCreatedUser())) {
                log.warn("用户 {} 尝试删除不属于自己的历史记录 {}，记录创建者: {}，空间创建者: {}", 
                        currentUser, historyRecord.getId(), historyRecord.getCreatedUser(), spaceEntity.getCreatedUser());
                throw new KnowledgeBasesException("权限不足，您只能删除自己创建的历史记录");
            }
        }
        
        // 验证记录的数据来源，只允许删除测试数据
        if (!DataSourceEnum.TEST.getCode().equals(historyRecord.getDataSource())) {
            throw new KnowledgeBasesException("只能删除测试来源的历史记录");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchDocumentProcess(KgBasesDocumentProcessSwitchDTO dto) {
        log.info("开始批量切换文档处理状态，文档ID列表：{}，启用状态：{}", dto.getIds(), dto.isEnabledUse());
        
        try {
            List<Long> ids = dto.getIds();
            if (CollectionUtils.isEmpty(ids)) {
                throw new KnowledgeBasesException("文档ID列表不能为空");
            }

            // 1. 批量查询所有文档处理信息
            List<KgBasesDocumentProcessInfoC> processInfoList = documentProcessMapper.selectBatchIds(ids);
            
            // 2. 验证查询结果
            validateProcessInfoList(processInfoList, ids);
            
            // 3. 批量更新本地文档处理状态
            batchUpdateDocumentProcessStatus(processInfoList, dto.isEnabledUse());
            
            // 4. 批量调用RagFlow启停接口
            batchCallRagFlowUpdateDocument(processInfoList, dto.getEnable());
            
            log.info("批量切换文档处理状态成功，影响文档数量: {}, 启用状态: {}", ids.size(), dto.isEnabledUse());
            
            return true;
        } catch (Exception e) {
            log.error("批量切换文档处理状态失败，参数: {}", dto, e);
            throw new KnowledgeBasesException("批量切换文档处理状态失败: " + e.getMessage());
        }
    }

    /**
     * 验证文档处理信息列表
     *
     * @param processInfoList 查询到的文档处理信息列表
     * @param requestIds      请求的ID列表
     */
    private void validateProcessInfoList(List<KgBasesDocumentProcessInfoC> processInfoList, List<Long> requestIds) {
        if (processInfoList.size() != requestIds.size()) {
            Set<Long> foundIds = processInfoList.stream()
                    .map(KgBasesDocumentProcessInfoC::getId)
                    .collect(Collectors.toSet());
            List<Long> notFoundIds = requestIds.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toList());
            throw new KnowledgeBasesException("以下文档处理信息不存在: " + notFoundIds);
        }

        // 验证所有文档的处理状态
        Integer completedStatus = Integer.parseInt(ProcessStatusEnum.VECTORIZED.getCode());
        List<Long> invalidStatusIds = processInfoList.stream()
                .filter(info -> !completedStatus.equals(info.getProcessStatus()))
                .map(KgBasesDocumentProcessInfoC::getId)
                .collect(Collectors.toList());
        
        if (!invalidStatusIds.isEmpty()) {
            throw new KnowledgeBasesException("以下文档当前状态不允许启停操作: " + invalidStatusIds);
        }
    }

    /**
     * 批量更新文档处理状态
     *
     * @param processInfoList 文档处理信息列表
     * @param enabledUse      是否启用
     */
    private void batchUpdateDocumentProcessStatus(List<KgBasesDocumentProcessInfoC> processInfoList, boolean enabledUse) {
        LocalDateTime currentTime = LocalDateTime.now();
        String currentUser = PtSecurityUtils.getUsername();
        
        try {
            // 方案1：使用 LambdaUpdateWrapper 批量更新（推荐）
            List<Long> ids = processInfoList.stream()
                    .map(KgBasesDocumentProcessInfoC::getId)
                    .collect(Collectors.toList());
            
            LambdaUpdateWrapper<KgBasesDocumentProcessInfoC> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(KgBasesDocumentProcessInfoC::getId, ids)
                    .set(KgBasesDocumentProcessInfoC::isEnabledUse, enabledUse)
                    .set(KgBasesDocumentProcessInfoC::getUpdatedTime, currentTime)
                    .set(KgBasesDocumentProcessInfoC::getUpdatedUser, currentUser);
            
            int updateCount = documentProcessMapper.update(null, updateWrapper);
            if (updateCount != processInfoList.size()) {
                throw new KnowledgeBasesException("批量更新文档处理状态失败，期望更新 " + processInfoList.size() + " 条，实际更新 " + updateCount + " 条");
            }
            
            log.info("批量更新文档处理状态成功，更新数量: {}", updateCount);
            
        } catch (Exception e) {
            log.error("批量更新文档处理状态异常，使用单条更新备用方案", e);
            
            // 方案2：备用方案 - 逐条更新（确保事务一致性）
            batchUpdateDocumentProcessStatusFallback(processInfoList, enabledUse, currentTime, currentUser);
        }
    }
    
    /**
     * 批量调用RagFlow文档更新接口
     *
     * @param processInfoList 文档处理信息列表
     * @param enabledUse      是否启用
     */
    private void batchCallRagFlowUpdateDocument(List<KgBasesDocumentProcessInfoC> processInfoList, Integer enabledUse) {
        log.info("开始批量调用RagFlow文档更新接口，文档数量：{}，启用状态：{}", processInfoList.size(), enabledUse);
        
        // 统计调用结果
        int successCount = 0;
        int failureCount = 0;
        List<String> failureMessages = new ArrayList<>();
        
        for (KgBasesDocumentProcessInfoC processInfo : processInfoList) {
            try {
                // 1. 获取知识空间信息，获取datasetId
                KgBasesSpaceC spaceEntity = spaceMapper.selectById(processInfo.getKgBasesSpaceCId());
                if (spaceEntity == null || StringUtils.isBlank(spaceEntity.getRagFlowId())) {
                    String errorMsg = "知识空间不存在或RAGFlow ID为空，文档处理ID：" + processInfo.getId();
                    log.warn(errorMsg);
                    failureMessages.add(errorMsg);
                    failureCount++;
                    continue;
                }
                
                // 2. 检查RAGFlow文档ID
                if (StringUtils.isBlank(processInfo.getRagDocumentId())) {
                    String errorMsg = "RAGFlow文档ID为空，无法调用启停接口，文档处理ID：" + processInfo.getId();
                    log.warn(errorMsg);
                    failureMessages.add(errorMsg);
                    failureCount++;
                    continue;
                }
                
                // 3. 构建RagFlow更新请求
                RagFlowDocumentUpdateDTO updateDTO = new RagFlowDocumentUpdateDTO();
                updateDTO.setEnabled(enabledUse);
                // 注：其他字段如name、parser_config、chunk_method、meta_fields根据需要设置，
                // 当前仅用于启停功能，所以只设置enabled字段
                
                // 4. 调用RagFlow接口
                RagFlowDocumentUpdateResponseDTO response = ragFlowClient.updateDocument(
                        spaceEntity.getRagFlowId(), 
                        processInfo.getRagDocumentId(), 
                        updateDTO
                );
                
                // 5. 处理响应结果
                if (response != null && response.getCode().equals(0)) {
                    log.info("RagFlow文档启停成功，文档处理ID：{}，RAGFlow文档ID：{}，启用状态：{}", 
                            processInfo.getId(), processInfo.getRagDocumentId(), enabledUse);
                    successCount++;
                } else {
                    String errorMsg = String.format("RagFlow文档启停失败，文档处理ID：%d，响应：%s", 
                            processInfo.getId(), response != null ? response.getMessage() : "响应为空");
                    log.error(errorMsg);
                    failureMessages.add(errorMsg);
                    failureCount++;
                }
                
            } catch (Exception e) {
                String errorMsg = String.format("调用RagFlow文档启停接口异常，文档处理ID：%d，错误：%s", 
                        processInfo.getId(), e.getMessage());
                log.error(errorMsg, e);
                failureMessages.add(errorMsg);
                failureCount++;
            }
        }
        
        // 6. 记录汇总结果
        log.info("批量调用RagFlow文档更新接口完成，总数：{}，成功：{}，失败：{}", 
                processInfoList.size(), successCount, failureCount);
        
        if (failureCount > 0) {
            log.warn("以下RagFlow文档启停调用失败：{}", String.join("; ", failureMessages));
            // 注：这里不抛异常，因为本地数据库已经更新成功，RagFlow调用失败不应影响整个事务
            // 实际业务中可以考虑记录失败日志供后续重试或人工处理
        }
    }

    /**
     * 批量更新文档处理状态的备用方案（逐条更新）
     *
     * @param processInfoList 文档处理信息列表
     * @param enabledUse     是否启用
     * @param currentTime    当前时间
     * @param currentUser    当前用户
     */
    private void batchUpdateDocumentProcessStatusFallback(List<KgBasesDocumentProcessInfoC> processInfoList, 
                                                         boolean enabledUse, 
                                                         LocalDateTime currentTime, 
                                                         String currentUser) {
        int successCount = 0;
        List<Long> failedIds = new ArrayList<>();
        
        for (KgBasesDocumentProcessInfoC processInfo : processInfoList) {
            try {
                processInfo.setEnabledUse(enabledUse);
                processInfo.setUpdatedTime(currentTime);
                processInfo.setUpdatedUser(currentUser);
                
                int updateResult = documentProcessMapper.updateById(processInfo);
                if (updateResult > 0) {
                    successCount++;
                } else {
                    failedIds.add(processInfo.getId());
                }
            } catch (Exception e) {
                log.error("更新文档处理状态失败，ID: {}", processInfo.getId(), e);
                failedIds.add(processInfo.getId());
            }
        }
        
        if (!failedIds.isEmpty()) {
            throw new KnowledgeBasesException("部分文档更新失败，失败的ID: " + failedIds + "，成功更新: " + successCount + " 条");
        }
        
        log.info("备用方案批量更新文档处理状态成功，更新数量: {}", successCount);
    }

    @Override
    public Boolean relateKnowledgeBases(KgBasesRelateKnowledgeBasesDTO dto) {
        // 查询已有关联的知识库ID
        // 伪代码：请在 KgBasesSpaceRelationCMapper 中实现 selectRelatedKnowledgeBases 方法
        List<KgBasesSpaceRelationC> kgBasesSpaceRelationCS = spaceRelationMapper.selectList(new LambdaQueryWrapper<KgBasesSpaceRelationC>().eq(KgBasesSpaceRelationC::getKgBasesSpaceCId, dto.getSpaceId())
                .in(KgBasesSpaceRelationC::getKgKnowledgeBasesId, dto.getKgKnowledgeBasesIdList()));

        List<Long> spaceKnowIds = kgBasesSpaceRelationCS.stream().map(KgBasesSpaceRelationC::getKgKnowledgeBasesId).distinct().collect(Collectors.toList());

        List<Long> insertIds = dto.getKgKnowledgeBasesIdList().stream().filter(oo -> !spaceKnowIds.contains(oo)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(insertIds)) {
            throw new KnowledgeBasesException("所有知识库已关联");
        }

        // 过滤未关联的
        insertIds.forEach(id -> {
            KgBasesSpaceRelationC rel = new KgBasesSpaceRelationC();
            rel.setKgBasesSpaceCId(dto.getSpaceId());
            rel.setKgKnowledgeBasesId(id);
            rel.setCreatedTime(new Date());
            rel.setCreatedUser(PtSecurityUtils.getUsername());
            rel.setIsDeleted(false);
            // 属于文档采编的
            rel.setBaseType("1");
            spaceRelationMapper.insert(rel);
        });
        return true;
    }


    /**
     * 获取处理状态描述
     */
    private String getProcessStatusDesc(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        ProcessStatusEnum byCode = ProcessStatusEnum.getByCode(FormatUtil.stringParse(status));
        return byCode.getDesc();
    }

    private void validateBasesSpace(String spaceName, String spaceCode) {
        if (spaceMapper.existsByName(spaceName)) {
            throw new KnowledgeGraphException("知识空间名称已存在");
        }
        if (spaceMapper.existsByCode(spaceCode)) {
            throw new KnowledgeGraphException("知识空间编码已存在");
        }
    }

    /**
     * 异步同步问答对和专业词汇
     * 
     * @param spaceEntity 知识空间实体
     * @param docEntity 文档处理信息
     * @param chunks 切片列表
     */
    @Async
    public void asyncSyncQaPairsAndTerms(KgBasesSpaceC spaceEntity, 
                                        KgBasesDocumentProcessInfoC docEntity, 
                                        List<KgBasesDocumentSliceImportDTO.ChunkDTO> chunks) {
        log.info("开始异步同步问答对和专业词汇，文档ID：{}", docEntity.getId());
        
        try {
            // 查询关联的知识库信息，获取公共字段
            KnowledgeBasesEntity knowledgeBaseInfo = knowledgeBasesMapper.selectById(docEntity.getKgKnowledgeBasesId());
            if (knowledgeBaseInfo == null) {
                log.warn("未找到对应的知识库信息，跳过同步，知识库ID：{}", docEntity.getKgKnowledgeBasesId());
                return;
            }
            
            String currentUser = PtSecurityUtils.getUsername();
            Date currentTime = new Date();
            
            // 同步问答对
            syncQaPairs(spaceEntity, knowledgeBaseInfo, chunks, currentUser, currentTime);
            
            // 同步专业词汇
            syncProfessionalTerms(knowledgeBaseInfo, chunks, currentUser, currentTime);
            
            log.info("异步同步问答对和专业词汇完成，文档ID：{}", docEntity.getId());
            
        } catch (Exception e) {
            log.error("异步同步问答对和专业词汇失败，文档ID：{}", docEntity.getId(), e);
        }
    }

    /**
     * 同步问答对
     */
    private void syncQaPairs(KgBasesSpaceC spaceEntity, 
                            KnowledgeBasesEntity knowledgeBaseInfo, 
                            List<KgBasesDocumentSliceImportDTO.ChunkDTO> chunks,
                            String currentUser, 
                            Date currentTime) {
        log.info("开始同步问答对，知识库ID：{}", knowledgeBaseInfo.getKgKnowledgeBasesId());
        
        // 1. 收集所有自动问题
        List<String> allQuestions = new ArrayList<>();
        Map<String, String> questionToContentMap = new HashMap<>();

        for (KgBasesDocumentSliceImportDTO.ChunkDTO chunk : chunks) {
            if (chunk.getQuestions() != null && !chunk.getQuestions().isEmpty()) {
                for (String question : chunk.getQuestions()) {
                    if (StringUtils.isNotBlank(question)) {
                        allQuestions.add(question.trim());
                        questionToContentMap.put(question.trim(), chunk.getContent());
                    }
                }
            }
        }
        
        if (allQuestions.isEmpty()) {
            log.info("没有发现自动问题，跳过问答对同步");
            return;
        }
        
        // 2. 批量查询已存在的问题进行去重
        Set<String> existingQuestions = batchCheckExistingQuestions(allQuestions);
        log.info("发现已存在问题数量：{}", existingQuestions.size());
        
        // 3. 过滤掉已存在的问题
        List<String> newQuestions = allQuestions.stream()
                .distinct() // 去除重复
                .filter(question -> !existingQuestions.contains(question))
                .filter(question -> question.length() <= 500) // 过滤过长的问题
                .collect(Collectors.toList());
        
        if (newQuestions.isEmpty()) {
            log.info("所有问题已存在，跳过问答对同步");
            return;
        }
        
        // 4. 构建问答对实体列表
        List<KgQaPairD> qaPairs = buildQaPairEntities(newQuestions, knowledgeBaseInfo, currentUser, currentTime, questionToContentMap);
        
        // 5. 批量插入问答对
        if (!qaPairs.isEmpty()) {
            try {
                int insertCount = batchInsertQaPairs(qaPairs);
                log.info("成功批量插入问答对数量：{}", insertCount);
                
                // 6. 批量写入ES索引
                batchSaveQaPairsToEs(qaPairs);
                
                // 7. 创建问答对与知识空间的关联关系
                batchCreateQaPairSpaceRelations(qaPairs, spaceEntity.getKgBasesSpaceCId(), currentUser, currentTime);
                
            } catch (Exception e) {
                log.error("批量插入问答对失败", e);
            }
        }
    }

    /**
     * 同步专业词汇
     */
    private void syncProfessionalTerms(KnowledgeBasesEntity knowledgeBaseInfo, 
                                      List<KgBasesDocumentSliceImportDTO.ChunkDTO> chunks,
                                      String currentUser, 
                                      Date currentTime) {
        log.info("开始同步专业词汇到kg_professional_terms_d表，知识库ID：{}", knowledgeBaseInfo.getKgKnowledgeBasesId());
        
        // 1. 收集所有关键词
        List<String> allTerms = new ArrayList<>();
        for (KgBasesDocumentSliceImportDTO.ChunkDTO chunk : chunks) {
            if (chunk.getImportant_keywords() != null && !chunk.getImportant_keywords().isEmpty()) {
                for (String keyword : chunk.getImportant_keywords()) {
                    if (StringUtils.isNotBlank(keyword)) {
                        allTerms.add(keyword.trim()); // 去除首尾空格
                    }
                }
            }
        }
        
        if (allTerms.isEmpty()) {
            log.info("没有发现关键词，跳过专业词汇同步");
            return;
        }
        
        // 2. 批量查询已存在的术语进行去重
        Set<String> existingTerms = batchCheckExistingTerms(allTerms);
        log.info("发现已存在术语数量：{}", existingTerms.size());
        
        // 3. 过滤掉已存在的术语
        List<String> newTerms = allTerms.stream()
                .distinct() // 去除重复
                .filter(term -> !existingTerms.contains(term))
                .filter(term -> term.length() <= 100) // 过滤过长的术语
                .collect(Collectors.toList());
        
        if (newTerms.isEmpty()) {
            log.info("所有术语已存在，跳过专业词汇同步");
            return;
        }
        
        // 4. 构建专业词汇实体列表 - 确保正确映射到kg_professional_terms_d表
        List<ProfessionalTermEntity> terms = buildProfessionalTermEntities(newTerms, knowledgeBaseInfo, currentUser, currentTime);
        
        // 5. 批量插入到kg_professional_terms_d表
        if (!terms.isEmpty()) {
            try {
                int insertCount = batchInsertToKgProfessionalTermsD(terms);
                log.info("成功批量插入专业词汇到kg_professional_terms_d表，数量：{}", insertCount);
                
                // 6. 批量写入ES索引
                batchSaveProfessionalTermsToEs(terms);
                
            } catch (Exception e) {
                log.error("批量插入专业词汇到kg_professional_terms_d表失败", e);
            }
        }
    }

    /**
     * 构建专业词汇实体列表 - 确保字段正确映射到kg_professional_terms_d表
     */
    private List<ProfessionalTermEntity> buildProfessionalTermEntities(List<String> newTerms,
                                                                       KnowledgeBasesEntity knowledgeBaseInfo,
                                                                       String currentUser,
                                                                       Date currentTime) {
        List<ProfessionalTermEntity> terms = new ArrayList<>();

        for (String keyword : newTerms) {
            ProfessionalTermEntity term = new ProfessionalTermEntity();

            // 设置术语信息
            term.setTerm(keyword);                    // term字段
            term.setChinese(keyword);                 // chinese字段
            term.setEnglish(keyword);                 // english字段
            term.setChineseDefinition(keyword);       // chinese_definition字段
            term.setEnglishDefinition(keyword);       // english_definition字段

            // 从知识库信息中复制业务字段  默认都只用第一个 因为专业词汇是默认选第一个
            if (StringUtils.isNotEmpty(knowledgeBaseInfo.getMajor())) {
                String[] split = knowledgeBaseInfo.getMajor().split(",");
                term.setMajor(split[0]);
            }
            if (StringUtils.isNotEmpty(knowledgeBaseInfo.getApplicationScene())) {
                String[] split = knowledgeBaseInfo.getApplicationScene().split(",");
                term.setApplicationScene(split[0]);
            }
            /*term.setMajor(knowledgeBaseInfo.getMajor());                    // major字段
            term.setApplicationScene(knowledgeBaseInfo.getApplicationScene()); // application_scene字段*/
            term.setRegion(knowledgeBaseInfo.getRegion() != null ?
                    String.valueOf(knowledgeBaseInfo.getRegion()) : null); // region字段

            //默认设置否
            term.setIsTelecomInfoField(CommonConstant.NO);

            // 设置标准字段
            term.setIsDeleted("0");                   // is_deleted字段
            term.setState(CaseStateEnum.PENDING_AUDIT.getStateValue());                       // state字段 - 有效状态
            term.setAuditStatus(CommonConstant.NO);                 // audit_status字段 - 审核通过

            // 设置计数字段
            term.setSearchNumber(0);                  // search_number字段
            term.setClickCount(0);                    // click_count字段

            // 设置时间和用户字段
            term.setCreatedUserName(currentUser);     // created_user_name字段
            term.setCreatedTime(currentTime);         // created_time字段
            term.setUpdatedUserName(currentUser);     // updated_user_name字段
            term.setUpdatedTime(currentTime);
            // updated_time字段
            // 默认未导出状态
            term.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
            term.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());

            // 加上默认的权限 todo 后续看要不要优化
            dtPermissionConverter.initPermission(term, DataPermissionConstant.PROFESSIONAL_TERM, null);

            terms.add(term);
        }

        return terms;
    }

    /**
     * 批量保存问答对到ES索引
     */
    private void batchSaveQaPairsToEs(List<KgQaPairD> qaPairs) {
        if (CollectionUtils.isEmpty(qaPairs)) {
            return;
        }
        
        try {
            List<IndexQuery> indexQueries = new ArrayList<>();
            
            for (KgQaPairD qaPair : qaPairs) {
                // 验证ID是否存在
                if (qaPair.getKgQaPairId() == null) {
                    log.warn("问答对ID为空，跳过ES索引写入，问题：{}", qaPair.getQuestion());
                    continue;
                }
                
                // 创建ES索引对象
                KnowledgeBasesIdx knowledgeBasesIdx = new KnowledgeBasesIdx();
                BeanUtils.copyProperties(qaPair, knowledgeBasesIdx);
                
                // 设置问答对特有字段
                knowledgeBasesIdx.setKnowledgeType(knowledgeBaseConfig.getQwd().getCategoryId());
                knowledgeBasesIdx.setCategoryId(FormatUtil.longParse(knowledgeBaseConfig.getQwd().getCategoryId()));
                knowledgeBasesIdx.setBasesType(KnowModelType.QA_PAIR.getType()); // 问答对类型
                knowledgeBasesIdx.setKgKnowledgeBasesId(qaPair.getKgQaPairId());
                knowledgeBasesIdx.setKnowledgeName(qaPair.getQuestion());
                knowledgeBasesIdx.setSummary(qaPair.getAnswer());
                knowledgeBasesIdx.setUpdatedTime(new Date());
                knowledgeBasesIdx.setChangeTime(new Date());
                
                // 设置全文检索字段
                knowledgeBasesIdx.setFullText();
                
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "qa_" + qaPair.getKgQaPairId();
                IndexQuery indexQuery = new IndexQueryBuilder()
                    .withId(documentId)
                    .withObject(knowledgeBasesIdx)
                    .build();
                    
                indexQueries.add(indexQuery);
            }
            
            // 批量写入ES
            IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
            List<IndexedObjectInformation> results = elasticsearchRestTemplate.bulkIndex(indexQueries, indexCoordinates);
            
            log.info("批量保存问答对到ES索引成功，数量：{}", results.size());
            
        } catch (Exception ex) {
            if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                // ES写入失败不影响主流程，只记录日志
                log.error("批量保存问答对到ES索引失败", ex);
            }


        }
    }

    /**
     * 批量保存专业词汇到ES索引
     */
    private void batchSaveProfessionalTermsToEs(List<ProfessionalTermEntity> terms) {
        if (CollectionUtils.isEmpty(terms)) {
            return;
        }
        
        try {
            List<IndexQuery> indexQueries = new ArrayList<>();
            
            for (ProfessionalTermEntity termEntity : terms) {
                // 验证ID是否存在
                if (termEntity.getKgProfessionalTermsId() == null) {
                    log.warn("专业词汇ID为空，跳过ES索引写入，词汇：{}", termEntity.getTerm());
                    continue;
                }
                
                // 构建KnowledgeBasesIdx对象用于ES索引
                KnowledgeBasesIdx idx = new KnowledgeBasesIdx();
                BeanUtils.copyProperties(termEntity, idx);
                
                // 设置专业词汇特有字段
                idx.setKgKnowledgeBasesId(termEntity.getKgProfessionalTermsId());
                idx.setTerm(termEntity.getTerm());
                idx.setChinese(termEntity.getChinese());
                idx.setEnglish(termEntity.getEnglish());
                idx.setChineseSynonym(termEntity.getChineseSynonym());
                idx.setChineseDefinition(termEntity.getChineseDefinition());
                idx.setEnglishDefinition(termEntity.getEnglishDefinition());
                idx.setEnglishSynonym(termEntity.getEnglishSynonym());
                idx.setMajor(termEntity.getMajor());
                idx.setApplicationScene(termEntity.getApplicationScene());
                idx.setIsTelecomInfoField(termEntity.getIsTelecomInfoField());
                idx.setBasesType(KnowModelType.PROFESSIONAL.getType());
                idx.setKnowledgeName(termEntity.getTerm());
                idx.setCreatedUserName(termEntity.getCreatedUserName());
                idx.setCreatedTime(termEntity.getCreatedTime());
                idx.setUpdatedUserName(termEntity.getUpdatedUserName());
                idx.setUpdatedTime(termEntity.getUpdatedTime());
                idx.setIsDeleted(termEntity.getIsDeleted());
                idx.setState(termEntity.getState());
                idx.setAuditStatus(termEntity.getAuditStatus());
                idx.setFullText();
                
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "profess_" + termEntity.getKgProfessionalTermsId();
                IndexQuery indexQuery = new IndexQueryBuilder()
                    .withId(documentId)
                    .withObject(idx)
                    .build();
                    
                indexQueries.add(indexQuery);
            }
            
            // 批量写入ES
            IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
            List<IndexedObjectInformation> results = elasticsearchRestTemplate.bulkIndex(indexQueries, indexCoordinates);
            
            log.info("批量保存专业词汇到ES索引成功，数量：{}", results.size());
            
        } catch (Exception ex) {
            if (!ex.getMessage().contains("Created") && !ex.getMessage().contains("200 OK") && !ex.getMessage().contains("201 OK")) {
                // ES写入失败不影响主流程，只记录日志
                log.error("批量保存专业词汇到ES索引失败", ex);
            }
        }
    }

    /**
     * 构建问答对实体列表
     */
    private List<KgQaPairD> buildQaPairEntities(List<String> newQuestions, 
                                              KnowledgeBasesEntity knowledgeBaseInfo,
                                              String currentUser, 
                                              Date currentTime, Map<String, String> questionToContentMap) {
        List<KgQaPairD> qaPairs = new ArrayList<>();
        
        for (String question : newQuestions) {
            KgQaPairD qaPair = new KgQaPairD();
            
            // 设置问答内容
            qaPair.setQuestion(question);
            qaPair.setAnswer("自动生成的问答对，答案待完善"); // 默认答案

            // 从知识库信息中复制公共字段
            qaPair.setKgKnowledgeBasesId(knowledgeBaseInfo.getKgKnowledgeBasesId());
            qaPair.setInstitution(knowledgeBaseInfo.getInstitution());
            qaPair.setAuthor(knowledgeBaseInfo.getAuthor());
            qaPair.setMajor(knowledgeBaseInfo.getMajor());
            qaPair.setApplicationScene(knowledgeBaseInfo.getApplicationScene());
            qaPair.setKnowledgeOrigin(knowledgeBaseInfo.getKnowledgeOrigin());
            qaPair.setFlowScene(knowledgeBaseInfo.getFlowScene());
            qaPair.setPublicity(knowledgeBaseInfo.getPublicity());
            qaPair.setPeriodValidity(knowledgeBaseInfo.getPeriodValidity());
            qaPair.setQuestionClassify(knowledgeBaseInfo.getQuestionClassify());
            qaPair.setRegion(knowledgeBaseInfo.getRegion());
            // 默认未导出状态
            qaPair.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
            qaPair.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());

            // 默认设置的属性
            qaPair.setCategoryId(FormatUtil.intParse(knowledgeBaseConfig.getQwd().getCategoryId()));
            qaPair.setLifeCycle(StringUtils.isNotEmpty(knowledgeBaseInfo.getLifeCycle())
                    ? knowledgeBaseInfo.getLifeCycle() : knowledgeBaseConfig.getQwd().getLifeCycleId());
            qaPair.setQuestionClassify(knowledgeBaseConfig.getQwd().getQuestionClassifyId());

            // 设置问答内容
            qaPair.setQuestion(question);
            qaPair.setAnswer(questionToContentMap.get(question)); // 答案是切片内容

            // 设置默认状态
            qaPair.setIsDeleted(CommonConstant.NO);
            qaPair.setSearchNumber(0L);
            qaPair.setClickCount(0L);
            qaPair.setAuditStatus(CommonConstant.NO); // 默认审核通过
            qaPair.setState(CaseStateEnum.PENDING_AUDIT.getStateValue()); // 默认有效状态
            qaPair.setCreatedUserName(currentUser);
            qaPair.setCreatedTime(currentTime);
            qaPair.setUpdatedUserName(currentUser);
            qaPair.setUpdatedTime(currentTime);

            //todo 后续再更新权限默认数据
            qaPair.setPermissionType("1");


            qaPairs.add(qaPair);
        }
        
        return qaPairs;
    }

    /**
     * 批量检查已存在的问题
     */
    private Set<String> batchCheckExistingQuestions(List<String> questions) {
        if (questions.isEmpty()) {
            return new HashSet<>();
        }
        
        try {
            Set<String> existingQuestions = new HashSet<>();
            int batchSize = 500; // 避免IN子句过长
            
            for (int i = 0; i < questions.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, questions.size());
                List<String> batch = questions.subList(i, endIndex);
                
                // 使用批量查询
                List<String> existingQuestionsList = qaPairMapper.batchCheckQuestionRepeat(batch);
                existingQuestions.addAll(existingQuestionsList);
            }
            
            return existingQuestions;
        } catch (Exception e) {
            log.warn("批量检查问题重复失败，返回空集合", e);
            return new HashSet<>();
        }
    }

    /**
     * 批量插入专业词汇到kg_professional_terms_d表
     */
    private int batchInsertToKgProfessionalTermsD(List<ProfessionalTermEntity> terms) {
        if (terms.isEmpty()) {
            return 0;
        }
        
        try {
            // 确保使用正确的mapper批量插入到kg_professional_terms_d表
            int batchSize = 500;
            int totalCount = 0;
            
            for (int i = 0; i < terms.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, terms.size());
                List<ProfessionalTermEntity> batch = terms.subList(i, endIndex);
                
                // 批量插入到kg_professional_terms_d表
                int insertCount = professionalTermMapper.batchInsert(batch);
                totalCount += insertCount;
                
                log.debug("批量插入专业词汇到kg_professional_terms_d表，批次大小：{}，成功数量：{}", 
                         batch.size(), insertCount);
            }
            
            log.info("批量插入专业词汇到kg_professional_terms_d表成功，总数量：{}", totalCount);
            return totalCount;
            
        } catch (Exception e) {
            log.error("批量插入专业词汇到kg_professional_terms_d表失败", e);
            throw new KnowledgeBasesException("批量插入专业词汇失败：" + e.getMessage());
        }
    }

    /**
     * 批量检查已存在的术语
     */
    private Set<String> batchCheckExistingTerms(List<String> terms) {
        if (terms.isEmpty()) {
            return new HashSet<>();
        }

        try {
            Set<String> existingTerms = new HashSet<>();
            int batchSize = 500; // 避免IN子句过长

            for (int i = 0; i < terms.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, terms.size());
                List<String> batch = terms.subList(i, endIndex);

                // 使用真正的批量查询
                List<String> existingTermsList = professionalTermMapper.batchCheckTermRepeat(batch);
                existingTerms.addAll(existingTermsList);
            }

            return existingTerms;
        } catch (Exception e) {
            log.warn("批量检查术语重复失败，返回空集合", e);
            return new HashSet<>();
        }
    }

    /**
     * 创建问答对与知识空间的关联关系（单个，兼容方法）
     */
    private void createQaPairSpaceRelation(Long qaPairId, Long spaceId) {
        try {
            KgBasesSpaceRelationC relation = new KgBasesSpaceRelationC();
            relation.setKgBasesSpaceCId(spaceId);
            relation.setKgKnowledgeBasesId(qaPairId); // 问答对ID作为关联ID
            relation.setBaseType(KnowModelType.QA_PAIR.getType()); // 2代表问答对类型
            relation.setIsDeleted(false);
            relation.setCreatedUser(PtSecurityUtils.getUsername());
            relation.setCreatedTime(new Date());
            relation.setUpdatedUser(PtSecurityUtils.getUsername());
            relation.setUpdatedTime(new Date());
            
            spaceRelationMapper.insert(relation);
        } catch (Exception e) {
            log.error("创建问答对空间关联关系失败，问答对ID：{}，空间ID：{}", qaPairId, spaceId, e);
        }
    }

    /**
     * 批量插入切片
     */
    private int batchInsertSlices(List<KgBasesDocumentSliceD> sliceEntities) {
        if (sliceEntities.isEmpty()) {
            return 0;
        }
        
        try {
            // 使用真正的批量插入，每次处理1000条
            int batchSize = 1000;
            int totalCount = 0;
            
            for (int i = 0; i < sliceEntities.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, sliceEntities.size());
                List<KgBasesDocumentSliceD> batch = sliceEntities.subList(i, endIndex);
                
                // 真正的批量插入，一次SQL插入多条记录
                int insertCount = documentSliceMapper.batchInsert(batch);
                totalCount += insertCount;
            }
            
            log.info("批量插入切片成功，总数量：{}", totalCount);
            return totalCount;
        } catch (Exception e) {
            log.error("批量插入切片失败", e);
            throw new KnowledgeBasesException("批量插入切片失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入问答对
     */
    private int batchInsertQaPairs(List<KgQaPairD> qaPairs) {
        if (qaPairs.isEmpty()) {
            return 0;
        }
        
        try {
            // 使用真正的批量插入，每次处理500条
            int batchSize = 500;
            int totalCount = 0;
            
            for (int i = 0; i < qaPairs.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, qaPairs.size());
                List<KgQaPairD> batch = qaPairs.subList(i, endIndex);
                
                // 真正的批量插入，一次SQL插入多条记录
                int insertCount = qaPairMapper.batchInsert(batch);
                totalCount += insertCount;
            }
            
            log.info("批量插入问答对成功，总数量：{}", totalCount);
            return totalCount;
        } catch (Exception e) {
            log.error("批量插入问答对失败", e);
            throw new KnowledgeBasesException("批量插入问答对失败：" + e.getMessage());
        }
    }



    /**
     * 批量创建问答对与知识空间的关联关系
     */
    private void batchCreateQaPairSpaceRelations(List<KgQaPairD> qaPairs, Long spaceId, String currentUser, Date currentTime) {
        if (qaPairs.isEmpty()) {
            return;
        }
        
        try {
            List<KgBasesSpaceRelationC> relations = new ArrayList<>();
            
            for (KgQaPairD qaPair : qaPairs) {
                if (qaPair.getKgQaPairId() != null) {
                    KgBasesSpaceRelationC relation = new KgBasesSpaceRelationC();
                    relation.setKgBasesSpaceCId(spaceId);
                    relation.setKgKnowledgeBasesId(qaPair.getKgQaPairId()); // 问答对ID作为关联ID
                    relation.setBaseType(KnowModelType.QA_PAIR.getType()); // 2代表问答对类型
                    relation.setIsDeleted(false);
                    relation.setCreatedUser(currentUser);
                    relation.setCreatedTime(currentTime);
                    relation.setUpdatedUser(currentUser);
                    relation.setUpdatedTime(currentTime);
                    
                    relations.add(relation);
                }
            }
            
            // 真正的批量插入关联关系
            if (!relations.isEmpty()) {
                int batchSize = 500;
                int totalCount = 0;
                for (int i = 0; i < relations.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, relations.size());
                    List<KgBasesSpaceRelationC> batch = relations.subList(i, endIndex);
                    
                    // 真正的批量插入，一次SQL插入多条记录
                    int insertCount = spaceRelationMapper.batchInsert(batch);
                    totalCount += insertCount;
                }
                log.info("批量创建问答对空间关联关系成功，数量：{}", totalCount);
            }
        } catch (Exception e) {
            log.error("批量创建问答对空间关联关系失败", e);
        }
    }
}