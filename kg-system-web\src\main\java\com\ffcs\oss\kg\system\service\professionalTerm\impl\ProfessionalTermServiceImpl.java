package com.ffcs.oss.kg.system.service.professionalTerm.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.kg.common.core.constant.CommonConstant;
import com.ffcs.oss.kg.common.core.exception.KnowledgeBasesException;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.common.core.util.ExcelUtil;
import com.ffcs.oss.kg.common.core.util.StringUtil;
import com.ffcs.oss.kg.data.converter.graph.DtPermissionConverter;
import com.ffcs.oss.kg.data.enums.CaseStateEnum;
import com.ffcs.oss.kg.data.enums.ExportStatusEnum;
import com.ffcs.oss.kg.data.enums.ProfessionalStateEnum;
import com.ffcs.oss.kg.data.enums.ReportStatusEnum;
import com.ffcs.oss.kg.data.es.KnowledgeBasesIdx;
import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.KgBasesAuditPersonDEvt;
import com.ffcs.oss.kg.data.model.evt.professionalTerm.ProfessionalTermReviewConclusionEvt;
import com.ffcs.oss.kg.data.model.vm.bases.KgBasesAuditPersonDVm;
import com.ffcs.oss.kg.data.rd.entity.DictionaryEntity;
import com.ffcs.oss.kg.data.rd.entity.cases.KgAuditorAllocationRuleConfigD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.ProfessionalTermAuditDimensionD;
import com.ffcs.oss.kg.data.rd.entity.knowledgeBases.ProfessionalTermEntity;
import com.ffcs.oss.kg.data.rd.mapper.DictionaryMapper;
import com.ffcs.oss.kg.data.rd.mapper.cases.AuditorAllocationRuleConfigMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.BasesAuditPersonMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.ProfessionalTermAuditDimensionMapper;
import com.ffcs.oss.kg.data.rd.mapper.knowledgeBases.ProfessionalTermMapper;
import com.ffcs.oss.kg.system.client.SmQryClient;
import com.ffcs.oss.kg.system.config.CommonConfig;
import com.ffcs.oss.kg.system.config.KnowledgeBaseConfig;
import com.ffcs.oss.kg.system.constants.DataPermissionConstant;
import com.ffcs.oss.kg.system.evt.professionalTerm.*;
import com.ffcs.oss.kg.system.evt.user.QryUserEvt;
import com.ffcs.oss.kg.system.service.professionalTerm.ProfessionalTermService;
import com.ffcs.oss.kg.system.utils.ExcelUtils;
import com.ffcs.oss.kg.system.utils.ReportFormatUtil;
import com.ffcs.oss.kg.system.vm.professionalTerm.ImportResultVm;
import com.ffcs.oss.kg.system.vm.user.SimpleUserVm;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 专业词汇Service实现类
 */
@Service
@Slf4j
public class ProfessionalTermServiceImpl extends ServiceImpl<ProfessionalTermMapper, ProfessionalTermEntity> implements ProfessionalTermService {

    @Autowired
    private ProfessionalTermMapper professionalTermMapper;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private KnowledgeBaseConfig knowledgeBaseConfig;

    @Autowired
    private DtPermissionConverter dtPermissionConverter;

    @Autowired
    private SmQryClient smQryClient;

    @Resource
    private AuditorAllocationRuleConfigMapper auditorAllocationRuleConfigMapper;

    @Resource
    private BasesAuditPersonMapper basesAuditPersonMapper;

    @Resource
    private ProfessionalTermAuditDimensionMapper professionalTermAuditDimensionMapper;

    @Resource
    private DictionaryMapper dictionaryMapper;

    @Override
    public PageInfo<ProfessionalTermEntity> page(ProfessionalTermQueryEvt evt) {
        // 使用公共权限处理逻辑
        buildCommonQueryConditions(evt);


        if (StringUtils.isNotBlank(evt.getFullText())) {
            // 构建ES查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 只查询专业词汇类型
            boolQuery.must(QueryBuilders.termQuery("basesType", "3"));

            // 只查询未删除的记录
            boolQuery.must(QueryBuilders.termQuery("isDeleted", CommonConstant.NO));

            // 全文检索
//            boolQuery.should(QueryBuilders.matchPhraseQuery("term", evt.getFullText()));
//            boolQuery.should(QueryBuilders.matchPhraseQuery("chinese", evt.getFullText()));
//            boolQuery.should(QueryBuilders.matchPhraseQuery("english", evt.getFullText()));
//            boolQuery.should(QueryBuilders.matchPhraseQuery("chineseDefinition", evt.getFullText()));
//            boolQuery.should(QueryBuilders.matchPhraseQuery("englishDefinition", evt.getFullText()));
//            boolQuery.should(QueryBuilders.matchPhraseQuery("chineseSynonym", evt.getFullText()));
//            boolQuery.should(QueryBuilders.matchPhraseQuery("englishSynonym", evt.getFullText()));
//            boolQuery.minimumShouldMatch(1);

            if (StringUtils.isNotEmpty(evt.getFullText())) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("fullText", evt.getFullText()));
            }


            // 权限专业过滤（优先）
            if (CollectionUtils.isNotEmpty(evt.getStateMajor())) {
                boolQuery.filter(QueryBuilders.termsQuery("major", evt.getStateMajor()));
            } else if (StringUtils.isNotBlank(evt.getMajor())) {
                evt.setMajorList(new ArrayList<>(Arrays.asList(evt.getMajor().split(","))));
                if (CollectionUtils.isNotEmpty(evt.getMajorList())) {
                    boolQuery.filter(QueryBuilders.termsQuery("major", evt.getMajorList()));
                }
            }

            // 权限区域过滤（优先）
            if (CollectionUtils.isNotEmpty(evt.getStateRegion())) {
                boolQuery.filter(QueryBuilders.termsQuery("region", evt.getStateRegion()));
            }
            // 表单区域过滤
            if (CollectionUtils.isNotEmpty(evt.getRegion())) {
                boolQuery.filter(QueryBuilders.termsQuery("region", evt.getRegion()));
            }

            // 其他状态过滤
            if (CollectionUtils.isNotEmpty(evt.getState())) {
                boolQuery.filter(QueryBuilders.termsQuery("state", evt.getState()));
            }

            // 创建NativeSearchQuery
            org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder searchQueryBuilder = new org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder();
            searchQueryBuilder.withQuery(boolQuery);

            // 设置分页
            searchQueryBuilder.withPageable(org.springframework.data.domain.PageRequest.of(evt.getPageNo() - 1, evt.getPageSize()));

            // 设置排序（按更新时间和创建时间降序）
            searchQueryBuilder.withSort(org.elasticsearch.search.sort.SortBuilders.fieldSort("updatedTime").order(org.elasticsearch.search.sort.SortOrder.DESC));
            searchQueryBuilder.withSort(org.elasticsearch.search.sort.SortBuilders.fieldSort("createdTime").order(org.elasticsearch.search.sort.SortOrder.DESC));

            // 执行查询
            org.springframework.data.elasticsearch.core.SearchHits<KnowledgeBasesIdx> searchHits =
                    elasticsearchRestTemplate.search(searchQueryBuilder.build(), KnowledgeBasesIdx.class);

            // 转换查询结果为ProfessionalTermEntity
            List<ProfessionalTermEntity> resultList = new ArrayList<>();
            for (org.springframework.data.elasticsearch.core.SearchHit<KnowledgeBasesIdx> hit : searchHits.getSearchHits()) {
                KnowledgeBasesIdx idx = hit.getContent();
                ProfessionalTermEntity entity = new ProfessionalTermEntity();
                BeanUtil.copyProperties(idx, entity);
                // 手动映射字段
                entity.setKgProfessionalTermsId(idx.getKgKnowledgeBasesId());
                entity.setTerm(idx.getTerm());
                entity.setChinese(idx.getChinese());
                entity.setEnglish(idx.getEnglish());
                entity.setChineseSynonym(idx.getChineseSynonym());
                entity.setChineseDefinition(idx.getChineseDefinition());
                entity.setEnglishDefinition(idx.getEnglishDefinition());
                entity.setEnglishSynonym(idx.getEnglishSynonym());
                entity.setMajor(idx.getMajor());
                entity.setApplicationScene(idx.getApplicationScene());
                entity.setIsTelecomInfoField(idx.getIsTelecomInfoField());
                entity.setCreatedUserName(idx.getCreatedUserName());
                entity.setCreatedTime(idx.getCreatedTime());
                entity.setUpdatedUserName(idx.getUpdatedUserName());
                entity.setUpdatedTime(idx.getUpdatedTime());
                entity.setIsDeleted(idx.getIsDeleted());
                entity.setState(idx.getState());
                entity.setAuditStatus(idx.getAuditStatus());
                entity.setSearchNumber(idx.getSearchNumber());
                entity.setRegion(idx.getRegion() == null ? null : String.valueOf(idx.getRegion()));
                entity.setReviewer(idx.getReviewer());
                entity.setAuditTime(idx.getAuditTime());
                entity.setReleaseTime(idx.getReleaseTime());
                entity.setChangeTime(idx.getChangeTime());
                entity.setSubmitTime(idx.getSubmitTime());
                entity.setWhetherFirstOffline(idx.getWhetherFirstOffline());
                entity.setClickCount(idx.getClickCount());

                resultList.add(entity);
            }

            // 创建PageInfo对象
            PageInfo<ProfessionalTermEntity> pageInfos = new PageInfo<>(resultList);
            pageInfos.setPageNum(evt.getPageNo());
            pageInfos.setPageSize(evt.getPageSize());
            pageInfos.setTotal(searchHits.getTotalHits());
            if (CollectionUtil.isNotEmpty(pageInfos.getList())) {
                //todo 先不处理后续权限还要改
                DtPermissionConverter.permissionConvert(pageInfos.getList(), evt);
            }
            return pageInfos;

        } else {
            PageHelper.startPage(evt.getPageNo(), evt.getPageSize());

            LambdaQueryWrapper<ProfessionalTermEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO);

            // 添加查询条件
            if (StringUtils.isNotBlank(evt.getTerm())) {
                queryWrapper.like(ProfessionalTermEntity::getTerm, evt.getTerm());
            }
            if (StringUtils.isNotBlank(evt.getChinese())) {
                queryWrapper.like(ProfessionalTermEntity::getChinese, evt.getChinese());
            }
            if (StringUtils.isNotBlank(evt.getEnglish())) {
                queryWrapper.like(ProfessionalTermEntity::getEnglish, evt.getEnglish());
            }

            // 支持审核状态多选查询
            if (StringUtils.isNotBlank(evt.getAuditStatus())) {
                String[] auditStatusArray = evt.getAuditStatus().split(",");
                if (auditStatusArray.length > 1) {
                    queryWrapper.in(ProfessionalTermEntity::getAuditStatus, Arrays.asList(auditStatusArray));
                } else {
                    queryWrapper.eq(ProfessionalTermEntity::getAuditStatus, evt.getAuditStatus());
                }
            }

            if (StringUtils.isNotBlank(evt.getCreatedUserName())) {
                queryWrapper.like(ProfessionalTermEntity::getCreatedUserName, evt.getCreatedUserName());
            }
            if (evt.getCreateTimeStart() != null) {
                queryWrapper.ge(ProfessionalTermEntity::getCreatedTime, evt.getCreateTimeStart());
            }
            if (evt.getCreateTimeEnd() != null) {
                queryWrapper.le(ProfessionalTermEntity::getCreatedTime, evt.getCreateTimeEnd());
            }

            // 添加state状态过滤条件
            if (CollectionUtil.isNotEmpty(evt.getState())) {
                queryWrapper.in(ProfessionalTermEntity::getState, evt.getState());
            }

            // 支持专业领域多选查询
            if (StringUtils.isNotBlank(evt.getMajor())) {
                String[] majorArray = evt.getMajor().split(",");
                if (majorArray.length > 1) {
                    queryWrapper.and(wrapper -> {
                        for (String major : majorArray) {
                            wrapper.or().apply("major ~* ('(^|,)' || {0} || '(,|$)')", major);
                        }
                    });
                } else {
                    queryWrapper.eq(ProfessionalTermEntity::getMajor, evt.getMajor());
                }
            }

            // 支持应用场景多选查询
            if (StringUtils.isNotBlank(evt.getApplicationScene())) {
                String[] sceneArray = evt.getApplicationScene().split(",");
                if (sceneArray.length > 1) {
                    queryWrapper.and(wrapper -> {
                        for (String scene : sceneArray) {
                            wrapper.or().apply("application_scene ~* ('(^|,)' || {0} || '(,|$)')", scene);
                        }
                    });
                } else {
                    queryWrapper.eq(ProfessionalTermEntity::getApplicationScene, evt.getApplicationScene());
                }
            }

            // 添加stateMajor过滤条件
            if (CollectionUtil.isNotEmpty(evt.getStateMajor())) {
                queryWrapper.and(wrapper -> {
                    for (String major : evt.getStateMajor()) {
                        wrapper.or().apply("major ~* ('(^|,)' || {0} || '(,|$)')", major);
                    }
                });
            }

            // 添加stateRegion过滤条件
            if (CollectionUtil.isNotEmpty(evt.getStateRegion())) {
                queryWrapper.in(ProfessionalTermEntity::getRegion, evt.getStateRegion().stream().map(String::valueOf).collect(Collectors.toList()));
            }

            // todo 查询条件的 区域
            if (CollectionUtil.isNotEmpty(evt.getRegion())) {
                queryWrapper.in(ProfessionalTermEntity::getRegion, evt.getRegion().stream().map(String::valueOf).collect(Collectors.toList()));
            }

            // 支持上报状态查询
            if (CollectionUtil.isNotEmpty(evt.getReportStatusList())) {
                queryWrapper.in(ProfessionalTermEntity::getReportStatus, evt.getReportStatusList());
            }

            if (CollectionUtil.isNotEmpty(evt.getExportStatusList())) {
                queryWrapper.in(ProfessionalTermEntity::getExportStatus, evt.getExportStatusList());
            }


            // 按创建时间降序排序
            queryWrapper.orderByDesc(ProfessionalTermEntity::getUpdatedTime).orderByDesc(ProfessionalTermEntity::getCreatedTime);

            // 使用professionalTermMapper
            List<ProfessionalTermEntity> list = professionalTermMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {

                //todo 先不处理，后续权限还要改
                DtPermissionConverter.permissionConvert(list, evt);
            }
            return new PageInfo<>(list);
        }
    }

    @Override
    public ProfessionalTermEntity getById(Long id) {
        return professionalTermMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(ProfessionalTermEvt evt) throws KnowledgeBasesException {
        // 校验参数
        checkParams(evt);

        // 检查术语名称的唯一性
        LambdaQueryWrapper<ProfessionalTermEntity> termCheckQueryWrapper = new LambdaQueryWrapper<>();
        termCheckQueryWrapper.eq(ProfessionalTermEntity::getTerm, evt.getTerm())
                .eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO);

        // 如果是更新操作，需要排除自身
        if (evt.getKgProfessionalTermsId() != null) {
            termCheckQueryWrapper.ne(ProfessionalTermEntity::getKgProfessionalTermsId, evt.getKgProfessionalTermsId());
        }

        // 查询是否存在相同术语的专业词汇
        ProfessionalTermEntity existingTerm = professionalTermMapper.selectOne(termCheckQueryWrapper);
        if (existingTerm != null) {
            throw new KnowledgeBasesException("已存在相同术语的专业词汇：" + evt.getTerm());
        }

        // 构建专业词汇对象
        ProfessionalTermEntity termEntity = new ProfessionalTermEntity();

        // 设置专业词汇特有字段
        termEntity.setTerm(evt.getTerm());
        termEntity.setChinese(evt.getChinese());
        termEntity.setEnglish(evt.getEnglish());
        termEntity.setChineseSynonym(evt.getChineseSynonym());
        termEntity.setChineseDefinition(evt.getChineseDefinition());
        termEntity.setEnglishDefinition(evt.getEnglishDefinition());
        termEntity.setEnglishSynonym(evt.getEnglishSynonym());
        termEntity.setMajor(evt.getMajor());
        termEntity.setApplicationScene(evt.getApplicationScene());
        termEntity.setIsTelecomInfoField(evt.getIsTelecomInfoField());

        // 设置基础信息
        String currentUsername = PtSecurityUtils.getUsername();
        Date now = new Date();

        QryUserEvt user = new QryUserEvt();
        user.setLoginName(PtSecurityUtils.getUsername());
        try {
            SimpleUserVm simpleUserVm;
            simpleUserVm = smQryClient.qrySimpleUser(user).getBody();
            if (simpleUserVm != null) {
                termEntity.setRegion(String.valueOf(simpleUserVm.getRegionId()));
            }
        } catch (Exception e) {
            log.error("请求qry服务报错，暂时跳过", e);
        }

        if (evt.getKgProfessionalTermsId() != null) {
            // 更新
            termEntity.setKgProfessionalTermsId(evt.getKgProfessionalTermsId());
            termEntity.setUpdatedUserName(currentUsername);
            termEntity.setUpdatedTime(now);

            // 先查询原记录
            ProfessionalTermEntity originalEntity = professionalTermMapper.selectById(evt.getKgProfessionalTermsId());
            if (originalEntity == null) {
                throw new KnowledgeBasesException("要更新的专业词汇不存在");
            }

            // 保留原有的创建人和创建时间等字段
            termEntity.setCreatedUserName(originalEntity.getCreatedUserName());
            termEntity.setCreatedTime(originalEntity.getCreatedTime());
            termEntity.setIsDeleted(originalEntity.getIsDeleted());
            Date date = new Date();
            // 如果是提交操作，状态改为"待审核"
            if (Boolean.TRUE.equals(evt.getSubmitFlag())) {
                termEntity.setState(ProfessionalStateEnum.PENDING_AUDIT.getStateCode());  // 设置为"待审核"状态
                termEntity.setAuditStatus(CommonConstant.NO);  // 设置为待审核
                termEntity.setSubmitTime(date);
            } else {
                // 保存操作，保持原有状态
                termEntity.setState(originalEntity.getState());
                termEntity.setAuditStatus(originalEntity.getAuditStatus());
            }

            // 直接更新数据库
            professionalTermMapper.updateById(termEntity);

            // 同时更新ES索引
//            try {
            // 构建KnowledgeBasesIdx对象用于ES索引
            KnowledgeBasesIdx idx = new KnowledgeBasesIdx();
            BeanUtil.copyProperties(termEntity, idx);
            // 设置主键
            idx.setKgKnowledgeBasesId(termEntity.getKgProfessionalTermsId());
            // 设置各字段
            idx.setTerm(termEntity.getTerm());
            idx.setChinese(termEntity.getChinese());
            idx.setEnglish(termEntity.getEnglish());
            idx.setChineseSynonym(termEntity.getChineseSynonym());
            idx.setChineseDefinition(termEntity.getChineseDefinition());
            idx.setEnglishDefinition(termEntity.getEnglishDefinition());
            idx.setEnglishSynonym(termEntity.getEnglishSynonym());
            idx.setMajor(termEntity.getMajor());
            idx.setApplicationScene(termEntity.getApplicationScene());
            idx.setIsTelecomInfoField(termEntity.getIsTelecomInfoField());
            idx.setBasesType(CommonConstant.BASES_TYPE_PROFESSIONAL_TERM);
            idx.setKnowledgeName(termEntity.getTerm());
            idx.setCreatedUserName(termEntity.getCreatedUserName());
            idx.setCreatedTime(termEntity.getCreatedTime());
            idx.setUpdatedUserName(termEntity.getUpdatedUserName());
            idx.setUpdatedTime(termEntity.getUpdatedTime());
            idx.setIsDeleted(termEntity.getIsDeleted());
            idx.setState(termEntity.getState());
            idx.setAuditStatus(termEntity.getAuditStatus());
            idx.setFullText();

            // 转换为JSON并更新到ES
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
            String jsonString;

            try {
                jsonString = mapper.writeValueAsString(idx);
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "profess_" + termEntity.getKgProfessionalTermsId();
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(Document.parse(jsonString))
                        .build();
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                elasticsearchRestTemplate.update(updateQuery, indexCoordinates);

            } catch (Exception e) {
                if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                    log.error("专业词汇更新到ES索引失败，异常信息：{}", e.getMessage());
                    log.error("专业词汇更新到ES索引失败，异常信息：", e);
                    throw new KnowledgeBasesException("更新失败:{}", e.getMessage());
                }
            }

        } else {
            // 添加默认权限
            if (commonConfig.getPerSwitch()) {
                //添加默认权限
                dtPermissionConverter.initPermission(termEntity, DataPermissionConstant.PROFESSIONAL_TERM, null);
            }
            // 新增
            termEntity.setCreatedUserName(currentUsername);
            termEntity.setCreatedTime(now);
            termEntity.setUpdatedUserName(currentUsername);
            termEntity.setUpdatedTime(now);
            termEntity.setIsDeleted("0");  // 设置为有效
            
            // 设置上报状态初始值
            termEntity.setReportStatus(ReportStatusEnum.NOT_REPORT.getCode());
            termEntity.setExportStatus(ExportStatusEnum.NOT_EXPORTED.getCode());

            // 根据是保存还是提交，设置不同的状态
            if (Boolean.TRUE.equals(evt.getSubmitFlag())) {
                // 提交操作
                termEntity.setState(ProfessionalStateEnum.PENDING_AUDIT.getStateCode());  // 设置为"待审核"状态
                termEntity.setAuditStatus(CommonConstant.NO);  // 设置为待审核
            } else {
                // 保存操作
                termEntity.setState(ProfessionalStateEnum.NEW.getStateCode());  // 设置为"新生成"状态
                termEntity.setAuditStatus(CommonConstant.NO);  // 设置为待审核
            }

            // 直接插入数据库
            professionalTermMapper.insert(termEntity);

            // 更新ES索引
            try {
                // 构建KnowledgeBasesIdx对象用于ES索引
                KnowledgeBasesIdx idx = new KnowledgeBasesIdx();
                // 设置主键
                BeanUtil.copyProperties(termEntity, idx);
                idx.setKgKnowledgeBasesId(termEntity.getKgProfessionalTermsId());
                // 设置各字段
                idx.setTerm(termEntity.getTerm());
                idx.setChinese(termEntity.getChinese());
                idx.setEnglish(termEntity.getEnglish());
                idx.setChineseSynonym(termEntity.getChineseSynonym());
                idx.setChineseDefinition(termEntity.getChineseDefinition());
                idx.setEnglishDefinition(termEntity.getEnglishDefinition());
                idx.setEnglishSynonym(termEntity.getEnglishSynonym());
                idx.setMajor(termEntity.getMajor());
                idx.setApplicationScene(termEntity.getApplicationScene());
                idx.setIsTelecomInfoField(termEntity.getIsTelecomInfoField());
                idx.setBasesType(CommonConstant.BASES_TYPE_PROFESSIONAL_TERM);
                idx.setKnowledgeName(termEntity.getTerm());
                idx.setCreatedUserName(termEntity.getCreatedUserName());
                idx.setCreatedTime(termEntity.getCreatedTime());
                idx.setUpdatedUserName(termEntity.getUpdatedUserName());
                idx.setUpdatedTime(termEntity.getUpdatedTime());
                idx.setIsDeleted(termEntity.getIsDeleted());
                idx.setState(termEntity.getState());
                idx.setAuditStatus(termEntity.getAuditStatus());
                idx.setFullText();

                // 创建索引请求
                IndexCoordinates indexCoordinates = IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName());
                // 使用前缀区分不同来源的ID，避免重复
                String documentId = "profess_" + termEntity.getKgProfessionalTermsId();
                IndexQuery indexQuery = new IndexQueryBuilder()
                        .withId(documentId)
                        .withObject(idx)
                        .build();
                // 使用index方法指定文档ID
                elasticsearchRestTemplate.index(indexQuery, indexCoordinates);

            } catch (Exception e) {
                if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                    log.error("专业词汇保存到ES索引失败，异常信息：{}", e.getMessage());
                    log.error("专业词汇保存到ES索引失败，异常信息：", e);
                    throw new KnowledgeBasesException("新增失败:{}", e.getMessage());
                }
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(ProfessionalTermDeleteEvt evt) throws KnowledgeBasesException {
        if (CollUtil.isEmpty(evt.getIds())) {
            throw new KnowledgeBasesException("删除ID不能为空");
        }

        // 查询待删除的专业词汇，区分已审核和未审核的数据
        List<ProfessionalTermEntity> entityList = new ArrayList<>();
        for (Long id : evt.getIds()) {
            ProfessionalTermEntity entity = professionalTermMapper.selectById(id);
            if (entity == null) {
                continue;
            }

            entityList.add(entity);
        }

        // 处理删除逻辑
        for (ProfessionalTermEntity entity : entityList) {
            try {
                // 如果是未审核状态，直接物理删除
                if (ProfessionalStateEnum.NEW.getStateCode().equals(entity.getState())
                        || ProfessionalStateEnum.AUDIT_NOT_PASS.getStateCode().equals(entity.getState())) {
                    // 物理删除数据库记录
                    professionalTermMapper.deleteById(entity.getKgProfessionalTermsId());

                    // 删除ES中的索引
                    try {
                        elasticsearchRestTemplate.delete("profess_" + entity.getKgProfessionalTermsId(), KnowledgeBasesIdx.class);
                    } catch (Exception e) {
                        if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                            log.error("删除ES索引失败，索引ID:{}，异常信息：{}", entity.getKgProfessionalTermsId(), e.getMessage());
                            log.error("删除ES索引失败，异常信息", e);
                            throw new KnowledgeBasesException("更新失败:{}", e.getMessage());
                        }
                        // 不影响主流程，继续执行
                    }
                } else {
                    // 已审核状态，修改状态为无效
                    entity.setIsDeleted("1");
                    professionalTermMapper.updateById(entity);

                    // 更新ES索引
                    try {
                        // 检查ES中是否存在该记录
                        KnowledgeBasesIdx idx = elasticsearchRestTemplate.get("profess_" + entity.getKgProfessionalTermsId(), KnowledgeBasesIdx.class);
                        if (idx != null) {
                            // 更新索引中的isDeleted字段
                            idx.setIsDeleted("1");
                            elasticsearchRestTemplate.save(idx);
                        }
                    } catch (Exception e) {
                        if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                            log.error("更新ES索引删除状态失败，索引ID:{}，异常信息：{}", entity.getKgProfessionalTermsId(), e.getMessage());
                            log.error("更新ES索引删除状态失败，异常信息：", e);
                            throw new KnowledgeBasesException("更新失败:{}", e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                throw new KnowledgeBasesException("删除专业词汇失败：" + e.getMessage());
            }
        }

        return true;
    }

    @Autowired
    private ReportFormatUtil reportFormatUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultVm importTerms(MultipartFile file) throws IOException, KnowledgeBasesException {
        if (file == null) {
            throw new KnowledgeBasesException("导入文件不能为空");
        }

        // 校验文件类型
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new KnowledgeBasesException("只支持Excel格式文件导入，请选择xlsx或xls文件");
        }

        ImportResultVm resultVm = new ImportResultVm();
        List<ImportResultVm.FailRecordVm> failRecords = new ArrayList<>();
        resultVm.setFailRecords(failRecords);

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null || sheet.getLastRowNum() < 1) {
                throw new KnowledgeBasesException("Excel文件无数据");
            }

            // 读取数据并导入
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                try {
                    // 读取每一行数据
                    String term = ExcelUtils.getCellValueAsString(row.getCell(0));  // 术语/缩略语/专业名词
                    String chinese = ExcelUtils.getCellValueAsString(row.getCell(1));  // 中文
                    String chineseSynonym = ExcelUtils.getCellValueAsString(row.getCell(2));  // 中文同义词
                    String english = ExcelUtils.getCellValueAsString(row.getCell(3));  // 英文
                    String englishSynonym = ExcelUtils.getCellValueAsString(row.getCell(4));  // 英文同义词
                    String chineseDefinition = ExcelUtils.getCellValueAsString(row.getCell(5));  // 中文定义
                    String englishDefinition = ExcelUtils.getCellValueAsString(row.getCell(6));  // 英文定义
                    String parentMajor = ExcelUtils.getCellValueAsString(row.getCell(7));  // 父级专业领域
                    String subMajor = ExcelUtils.getCellValueAsString(row.getCell(8));  // 子专业领域
                    String parentScene = ExcelUtils.getCellValueAsString(row.getCell(9));  // 父级应用场景
                    String subScene = ExcelUtils.getCellValueAsString(row.getCell(10));  // 子应用场景
                    String isTelecomInfoField = ExcelUtils.getCellValueAsString(row.getCell(11));  // 是否通信信息领域

                    // 将子专业领域和子应用场景的中文转换为字典ID
                    String major = null;
                    String applicationScene = null;
                    
                    // 获取子专业领域对应的字典ID
                    if (StringUtils.isNotBlank(subMajor)) {
                        List<DictionaryEntity> majorDictList = dictionaryMapper.findByCodeTypeIn(Collections.singletonList("C2"));
                        if (CollUtil.isNotEmpty(majorDictList)) {
                            Optional<DictionaryEntity> majorDict = majorDictList.stream()
                                    .filter(dict -> subMajor.equals(dict.getCodeName()))
                                    .findFirst();
                            if (majorDict.isPresent()) {
                                major = String.valueOf(majorDict.get().getBdpDirId());
                            }
                        }
                    }
                    
                    // 获取子应用场景对应的字典ID
                    if (StringUtils.isNotBlank(subScene)) {
                        List<DictionaryEntity> sceneDictList = dictionaryMapper.findByCodeTypeIn(Collections.singletonList("C12"));
                        if (CollUtil.isNotEmpty(sceneDictList)) {
                            Optional<DictionaryEntity> sceneDict = sceneDictList.stream()
                                    .filter(dict -> subScene.equals(dict.getCodeName()))
                                    .findFirst();
                            if (sceneDict.isPresent()) {
                                applicationScene = String.valueOf(sceneDict.get().getBdpDirId());
                            }
                        }
                    }
                    
                    // todo  没有找到子专业就要报错
                    if (StringUtils.isBlank(major) && StringUtils.isNotBlank(parentMajor)) {
                        //major = DictionaryConvertUtils.convertNameToDictId(parentMajor, "C2", dictionaryMapper);
                    }
                    
                    if (StringUtils.isBlank(applicationScene) && StringUtils.isNotBlank(parentScene)) {
                        //applicationScene = DictionaryConvertUtils.convertNameToDictId(parentScene, "C12", dictionaryMapper);
                    }

                    // 创建保存对象
                    ProfessionalTermEvt termEvt = new ProfessionalTermEvt();
                    termEvt.setTerm(term);
                    termEvt.setChinese(chinese);
                    termEvt.setChineseSynonym(chineseSynonym);
                    termEvt.setEnglish(english);
                    termEvt.setEnglishSynonym(englishSynonym);
                    termEvt.setChineseDefinition(chineseDefinition);
                    termEvt.setEnglishDefinition(englishDefinition);
                    termEvt.setMajor(major);  // 使用转换后的字典ID
                    termEvt.setApplicationScene(applicationScene);  // 使用转换后的字典ID
                    
                    // 转换"是否通信信息领域"的值，将"是"转换为"1"，"否"转换为"0"
                    if ("是".equals(isTelecomInfoField)) {
                        isTelecomInfoField = "1";
                    } else if ("否".equals(isTelecomInfoField)) {
                        isTelecomInfoField = "0";
                    }
                    termEvt.setIsTelecomInfoField(isTelecomInfoField);

                    // 校验必填项
                    StringBuilder errorMsg = new StringBuilder();
                    if (StrUtil.isBlank(term)) {
                        errorMsg.append("术语/缩略语/专业名词不能为空;");
                    }
                    if (StrUtil.isBlank(english)) {
                        errorMsg.append("英文不能为空;");
                    }
                    if (StrUtil.isBlank(major)) {
                        errorMsg.append("专业领域不能为空;");
                    }
                    if (StrUtil.isBlank(applicationScene)) {
                        errorMsg.append("应用场景不能为空;");
                    }
                    if (StrUtil.isBlank(isTelecomInfoField)) {
                        errorMsg.append("是否通信信息领域不能为空;");
                    } else if (!"是".equals(isTelecomInfoField) && !"否".equals(isTelecomInfoField) && 
                               !"1".equals(isTelecomInfoField) && !"0".equals(isTelecomInfoField)) {
                        errorMsg.append("是否通信信息领域的值必须为\"是\"、\"否\"、\"1\"或\"0\";");
                    }

                    if (errorMsg.length() > 0) {
                        // 添加到失败记录
                        ImportResultVm.FailRecordVm failRecord = new ImportResultVm.FailRecordVm();
                        failRecord.setRowNum(i + 1);
                        failRecord.setErrorMsg(errorMsg.toString());
                        failRecord.setTerm(term);
                        failRecords.add(failRecord);
                        resultVm.setFailCount(resultVm.getFailCount() + 1);
                        continue;
                    }

                    // 检查是否已存在相同术语的词汇
                    LambdaQueryWrapper<ProfessionalTermEntity> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ProfessionalTermEntity::getTerm, term)
                            .eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO);
                    ProfessionalTermEntity existEntity = professionalTermMapper.selectOne(queryWrapper);

                    if (existEntity != null) {
                        // 已存在，添加到失败记录
                        ImportResultVm.FailRecordVm failRecord = new ImportResultVm.FailRecordVm();
                        failRecord.setRowNum(i + 1);
                        failRecord.setErrorMsg("导入失败：已存在相同术语的词汇");
                        failRecord.setTerm(term);
                        failRecord.setChinese(chinese);
                        failRecord.setEnglish(english);
                        failRecords.add(failRecord);
                        resultVm.setFailCount(resultVm.getFailCount() + 1);
                        continue;
                    }

                    // 保存数据 - 导入时直接提交审核
                    termEvt.setSubmitFlag(true);  // 导入时自动提交审核
                    saveOrUpdate(termEvt);
                    resultVm.setSuccessCount(resultVm.getSuccessCount() + 1);
                } catch (Exception e) {
                    log.error("导入专业词汇第{}行数据失败", i + 1, e);
                    ImportResultVm.FailRecordVm failRecord = new ImportResultVm.FailRecordVm();
                    failRecord.setRowNum(i + 1);
                    failRecord.setErrorMsg("导入失败：" + e.getMessage());
                    failRecord.setTerm(ExcelUtils.getCellValueAsString(row.getCell(0)));
                    failRecords.add(failRecord);
                    resultVm.setFailCount(resultVm.getFailCount() + 1);
                }
            }

            return resultVm;
        } catch (Exception e) {
            log.error("导入专业词汇失败", e);
            throw new KnowledgeBasesException("导入专业词汇失败：" + e.getMessage());
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建主模板sheet
            Sheet mainSheet = workbook.createSheet("专业词汇导入模板");

            // 创建表头
            Row headerRow = mainSheet.createRow(0);
            headerRow.createCell(0).setCellValue("术语/缩略语/专业名词(必填)");
            headerRow.createCell(1).setCellValue("中文");
            headerRow.createCell(2).setCellValue("中文同义词");
            headerRow.createCell(3).setCellValue("英文(必填)");
            headerRow.createCell(4).setCellValue("英文同义词");
            headerRow.createCell(5).setCellValue("中文定义");
            headerRow.createCell(6).setCellValue("英文定义");
            headerRow.createCell(7).setCellValue("专业领域(必填)");
            headerRow.createCell(8).setCellValue("子专业领域(必填)");
            headerRow.createCell(9).setCellValue("应用场景(必填)");
            headerRow.createCell(10).setCellValue("子应用场景(必填)");
            headerRow.createCell(11).setCellValue("是否通信信息领域(必填,是|否)");

            // 设置列宽
            for (int i = 0; i < 12; i++) {
                mainSheet.setColumnWidth(i, 20 * 256);
            }

            // 创建专业领域字典数据sheet
            Sheet majorSheet = workbook.createSheet("专业领域字典");
            Row majorHeaderRow = majorSheet.createRow(0);
            majorHeaderRow.createCell(0).setCellValue("专业领域ID");
            majorHeaderRow.createCell(1).setCellValue("专业领域名称");
            majorHeaderRow.createCell(2).setCellValue("专业领域代码");
            majorHeaderRow.createCell(3).setCellValue("父级ID");
            
            // 创建应用场景字典数据sheet
            Sheet sceneSheet = workbook.createSheet("应用场景字典");
            Row sceneHeaderRow = sceneSheet.createRow(0);
            sceneHeaderRow.createCell(0).setCellValue("应用场景ID");
            sceneHeaderRow.createCell(1).setCellValue("应用场景名称");
            sceneHeaderRow.createCell(2).setCellValue("应用场景代码");
            sceneHeaderRow.createCell(3).setCellValue("父级ID");

            // 创建名称管理器sheet（用于存储级联关系）
            Sheet nameManagerSheet = workbook.createSheet("级联关系");
            workbook.setSheetHidden(workbook.getSheetIndex("级联关系"), true); // 隐藏该sheet

            // 加载专业领域字典数据
            List<DictionaryEntity> majorList = dictionaryMapper.findByCodeTypeIn(Collections.singletonList("C2"));
            if (CollUtil.isNotEmpty(majorList)) {
                // 记录父级专业领域
                Map<Long, List<DictionaryEntity>> parentMajorMap = new HashMap<>();
                // 记录所有的子专业领域
                List<DictionaryEntity> subMajorList = new ArrayList<>();
                
                // 填充专业领域sheet并分类
                int majorRowIndex = 1;
                for (DictionaryEntity dict : majorList) {
                    Row row = majorSheet.createRow(majorRowIndex++);
                    row.createCell(0).setCellValue(dict.getBdpDirId() != null ? dict.getBdpDirId().toString() : "");
                    row.createCell(1).setCellValue(dict.getCodeName());
                    row.createCell(2).setCellValue(dict.getCodeValue());
                    row.createCell(3).setCellValue(dict.getParentId() != null ? dict.getParentId().toString() : "");
                    
                    // 如果有父级ID，则归类为子专业领域
                    if (dict.getParentId() != null) {
                        subMajorList.add(dict);
                        parentMajorMap.computeIfAbsent(dict.getParentId(), k -> new ArrayList<>()).add(dict);
                    }
                }
                
                // 设置专业领域列宽
                for (int i = 0; i < 4; i++) {
                    majorSheet.setColumnWidth(i, 20 * 256);
                }
                
                // 创建父级专业领域列表
                List<DictionaryEntity> parentMajorList = majorList.stream()
                        .filter(dict -> dict.getParentId() == null)
                        .collect(Collectors.toList());
                
                // 为主sheet添加专业领域下拉选择
                String[] parentMajorNames = parentMajorList.stream()
                        .map(DictionaryEntity::getCodeName)
                        .toArray(String[]::new);
                
                // 创建父级专业领域下拉列表
                DataValidationHelper validationHelper = mainSheet.getDataValidationHelper();
                CellRangeAddressList majorRangeAddressList = new CellRangeAddressList(1, 1000, 7, 7);
                DataValidationConstraint majorConstraint = validationHelper.createExplicitListConstraint(parentMajorNames);
                DataValidation majorValidation = validationHelper.createValidation(majorConstraint, majorRangeAddressList);
                majorValidation.setShowErrorBox(true);
                mainSheet.addValidationData(majorValidation);
                
                // 创建子专业领域下拉列表（使用数据有效性公式）
                // 为每个父级专业领域创建对应的子级列表
                int nameRowIndex = 0;
                Map<String, String> majorNameMap = new HashMap<>();
                
                for (DictionaryEntity parentMajor : parentMajorList) {
                    List<DictionaryEntity> children = parentMajorMap.get(parentMajor.getBdpDirId());
                    if (CollUtil.isEmpty(children)) {
                        continue;
                    }
                    
                    // 创建子专业领域列表
                    String[] childNames = children.stream()
                            .map(DictionaryEntity::getCodeName)
                            .toArray(String[]::new);
                    
                    // 在名称管理器sheet中创建数据
                    Row childMajorNameRow = nameManagerSheet.createRow(nameRowIndex++);
                    childMajorNameRow.createCell(0).setCellValue(parentMajor.getCodeName());
                    
                    for (int i = 0; i < childNames.length; i++) {
                        childMajorNameRow.createCell(i + 1).setCellValue(childNames[i]);
                    }
                    
                    // 记录父级名称和对应的引用
                    String rangeName = parentMajor.getCodeName();
                    int lastCol = childMajorNameRow.getLastCellNum() - 1;
                    if (lastCol > 0) {
                        String reference = "级联关系!$B$" + nameRowIndex + ":$" + 
                                         (char)('A' + lastCol) + "$" + nameRowIndex;
                        majorNameMap.put(rangeName, reference);
                    }
                }
                
                // 创建名称管理器
                for (Map.Entry<String, String> entry : majorNameMap.entrySet()) {
                    try {
                        Name name = workbook.createName();
                        // 使用父级专业领域名称作为名称管理器的名称，替换空格为下划线
                        name.setNameName(entry.getKey().replaceAll("\\s+", "_"));
                        name.setRefersToFormula(entry.getValue());
                    } catch (Exception e) {
                        log.warn("创建专业领域名称管理器失败: {}, 错误: {}", entry.getKey(), e.getMessage());
                    }
                }
                
                // 创建子专业领域下拉列表
                CellRangeAddressList subMajorRangeAddressList = new CellRangeAddressList(1, 1000, 8, 8);
                DataValidationConstraint subMajorConstraint = validationHelper.createFormulaListConstraint(
                        "INDIRECT(SUBSTITUTE(INDIRECT(\"H\"&ROW()),\" \",\"_\"))"); // 使用当前行的H列单元格值，替换空格为下划线
                DataValidation subMajorValidation = validationHelper.createValidation(subMajorConstraint, subMajorRangeAddressList);
                subMajorValidation.setShowErrorBox(true);
                mainSheet.addValidationData(subMajorValidation);
            }
            
            // 加载应用场景字典数据
            List<DictionaryEntity> sceneList = dictionaryMapper.findByCodeTypeIn(Collections.singletonList("C12"));
            if (CollUtil.isNotEmpty(sceneList)) {
                // 记录父级应用场景
                Map<Long, List<DictionaryEntity>> parentSceneMap = new HashMap<>();
                // 记录所有的子应用场景
                List<DictionaryEntity> subSceneList = new ArrayList<>();
                
                // 填充应用场景sheet并分类
                int sceneRowIndex = 1;
                for (DictionaryEntity dict : sceneList) {
                    Row row = sceneSheet.createRow(sceneRowIndex++);
                    row.createCell(0).setCellValue(dict.getBdpDirId() != null ? dict.getBdpDirId().toString() : "");
                    row.createCell(1).setCellValue(dict.getCodeName());
                    row.createCell(2).setCellValue(dict.getCodeValue());
                    row.createCell(3).setCellValue(dict.getParentId() != null ? dict.getParentId().toString() : "");
                    
                    // 如果有父级ID，则归类为子应用场景
                    if (dict.getParentId() != null) {
                        subSceneList.add(dict);
                        parentSceneMap.computeIfAbsent(dict.getParentId(), k -> new ArrayList<>()).add(dict);
                    }
                }
                
                // 设置应用场景列宽
                for (int i = 0; i < 4; i++) {
                    sceneSheet.setColumnWidth(i, 20 * 256);
                }
                
                // 获取父级应用场景列表
                List<DictionaryEntity> parentSceneList = sceneList.stream()
                        .filter(dict -> dict.getParentId() == null)
                        .collect(Collectors.toList());
                
                // 为主sheet添加应用场景下拉选择
                String[] parentSceneNames = parentSceneList.stream()
                        .map(DictionaryEntity::getCodeName)
                        .toArray(String[]::new);
                
                // 创建父级应用场景下拉列表
                DataValidationHelper validationHelper = mainSheet.getDataValidationHelper();
                CellRangeAddressList sceneRangeAddressList = new CellRangeAddressList(1, 1000, 9, 9);
                DataValidationConstraint sceneConstraint = validationHelper.createExplicitListConstraint(parentSceneNames);
                DataValidation sceneValidation = validationHelper.createValidation(sceneConstraint, sceneRangeAddressList);
                sceneValidation.setShowErrorBox(true);
                mainSheet.addValidationData(sceneValidation);
                
                // 为每个父级应用场景创建对应的子级列表
                int nameRowIndex = workbook.getSheet("级联关系").getLastRowNum() + 1;
                Map<String, String> sceneNameMap = new HashMap<>();
                
                for (DictionaryEntity parentScene : parentSceneList) {
                    List<DictionaryEntity> children = parentSceneMap.get(parentScene.getBdpDirId());
                    if (CollUtil.isEmpty(children)) {
                        continue;
                    }
                    
                    // 创建子应用场景列表
                    String[] childNames = children.stream()
                            .map(DictionaryEntity::getCodeName)
                            .toArray(String[]::new);
                    
                    // 在名称管理器sheet中创建数据
                    Row childSceneNameRow = nameManagerSheet.createRow(nameRowIndex++);
                    childSceneNameRow.createCell(0).setCellValue(parentScene.getCodeName());
                    
                    for (int i = 0; i < childNames.length; i++) {
                        childSceneNameRow.createCell(i + 1).setCellValue(childNames[i]);
                    }
                    
                    // 记录父级名称和对应的引用
                    String rangeName = parentScene.getCodeName();
                    int lastCol = childSceneNameRow.getLastCellNum() - 1;
                    if (lastCol > 0) {
                        String reference = "级联关系!$B$" + nameRowIndex + ":$" + 
                                         (char)('A' + lastCol) + "$" + nameRowIndex;
                        sceneNameMap.put(rangeName, reference);
                    }
                }
                
                // 创建名称管理器
                for (Map.Entry<String, String> entry : sceneNameMap.entrySet()) {
                    try {
                        Name name = workbook.createName();
                        // 使用父级应用场景名称作为名称管理器的名称，替换空格为下划线
                        name.setNameName(entry.getKey().replaceAll("\\s+", "_"));
                        name.setRefersToFormula(entry.getValue());
                    } catch (Exception e) {
                        log.warn("创建应用场景名称管理器失败: {}, 错误: {}", entry.getKey(), e.getMessage());
                    }
                }
                
                // 创建子应用场景下拉列表
                CellRangeAddressList subSceneRangeAddressList = new CellRangeAddressList(1, 1000, 10, 10);
                DataValidationConstraint subSceneConstraint = validationHelper.createFormulaListConstraint(
                        "INDIRECT(SUBSTITUTE(INDIRECT(\"J\"&ROW()),\" \",\"_\"))"); // 使用当前行的J列单元格值，替换空格为下划线
                DataValidation subSceneValidation = validationHelper.createValidation(subSceneConstraint, subSceneRangeAddressList);
                subSceneValidation.setShowErrorBox(true);
                mainSheet.addValidationData(subSceneValidation);
            }
            
            // 为"是否通信信息领域"添加下拉选择
            // 使用"是"和"否"作为选项，用户体验更好，导入时会自动转换为1和0
            DataValidationHelper validationHelper = mainSheet.getDataValidationHelper();
            CellRangeAddressList telecomRangeAddressList = new CellRangeAddressList(1, 1000, 11, 11);
            DataValidationConstraint telecomConstraint = validationHelper.createExplicitListConstraint(new String[]{"是", "否"});
            DataValidation telecomValidation = validationHelper.createValidation(telecomConstraint, telecomRangeAddressList);
            telecomValidation.setShowErrorBox(true);
            mainSheet.addValidationData(telecomValidation);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("专业词汇导入模板.xlsx", "UTF-8"));

            // 输出文件
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
            }
        } catch (Exception e) {
            log.error("下载专业词汇导入模板失败", e);
            throw new IOException("下载专业词汇导入模板失败：" + e.getMessage());
        }
    }

    @Override
    public PageInfo<ProfessionalTermEntity> fullTextSearch(ProfessionalTermQueryEvt evt) {
        if (StringUtils.isBlank(evt.getKeyword())) {
            return new PageInfo<>(Collections.emptyList());
        }

        // 构建ES查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 只查询专业词汇类型
        boolQuery.must(QueryBuilders.termQuery("basesType", "3"));

        // 只查询未删除的记录
        boolQuery.must(QueryBuilders.termQuery("isDeleted", CommonConstant.NO));

        // 全文检索
        boolQuery.should(QueryBuilders.matchPhraseQuery("term", evt.getKeyword()));
        boolQuery.should(QueryBuilders.matchPhraseQuery("chinese", evt.getKeyword()));
        boolQuery.should(QueryBuilders.matchPhraseQuery("english", evt.getKeyword()));
        boolQuery.should(QueryBuilders.matchPhraseQuery("chineseDefinition", evt.getKeyword()));
        boolQuery.should(QueryBuilders.matchPhraseQuery("englishDefinition", evt.getKeyword()));
        boolQuery.should(QueryBuilders.matchPhraseQuery("chineseSynonym", evt.getKeyword()));
        boolQuery.should(QueryBuilders.matchPhraseQuery("englishSynonym", evt.getKeyword()));
        boolQuery.minimumShouldMatch(1);

        try {
            // 创建NativeSearchQuery
            org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder searchQueryBuilder = new org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder();
            searchQueryBuilder.withQuery(boolQuery);

            // 设置分页
            searchQueryBuilder.withPageable(org.springframework.data.domain.PageRequest.of(evt.getPageNo() - 1, evt.getPageSize()));

            // 设置排序（按更新时间和创建时间降序）
            searchQueryBuilder.withSort(org.elasticsearch.search.sort.SortBuilders.fieldSort("updatedTime").order(org.elasticsearch.search.sort.SortOrder.DESC));
            searchQueryBuilder.withSort(org.elasticsearch.search.sort.SortBuilders.fieldSort("createdTime").order(org.elasticsearch.search.sort.SortOrder.DESC));

            // 执行查询
            org.springframework.data.elasticsearch.core.SearchHits<KnowledgeBasesIdx> searchHits =
                    elasticsearchRestTemplate.search(searchQueryBuilder.build(), KnowledgeBasesIdx.class);

            // 转换查询结果为ProfessionalTermEntity
            List<ProfessionalTermEntity> resultList = new ArrayList<>();
            for (org.springframework.data.elasticsearch.core.SearchHit<KnowledgeBasesIdx> hit : searchHits.getSearchHits()) {
                KnowledgeBasesIdx idx = hit.getContent();
                ProfessionalTermEntity entity = new ProfessionalTermEntity();
                BeanUtil.copyProperties(idx, entity);
                // 手动映射字段
                entity.setKgProfessionalTermsId(idx.getKgKnowledgeBasesId());
                entity.setTerm(idx.getTerm());
                entity.setChinese(idx.getChinese());
                entity.setEnglish(idx.getEnglish());
                entity.setChineseSynonym(idx.getChineseSynonym());
                entity.setChineseDefinition(idx.getChineseDefinition());
                entity.setEnglishDefinition(idx.getEnglishDefinition());
                entity.setEnglishSynonym(idx.getEnglishSynonym());
                entity.setMajor(idx.getMajor());
                entity.setApplicationScene(idx.getApplicationScene());
                entity.setIsTelecomInfoField(idx.getIsTelecomInfoField());
                entity.setCreatedUserName(idx.getCreatedUserName());
                entity.setCreatedTime(idx.getCreatedTime());
                entity.setUpdatedUserName(idx.getUpdatedUserName());
                entity.setUpdatedTime(idx.getUpdatedTime());
                entity.setIsDeleted(idx.getIsDeleted());
                entity.setState(idx.getState());
                entity.setAuditStatus(idx.getAuditStatus());
                entity.setSearchNumber(idx.getSearchNumber());
                entity.setRegion(idx.getRegion() == null ? null : String.valueOf(idx.getRegion()));
                entity.setReviewer(idx.getReviewer());
                entity.setAuditTime(idx.getAuditTime());
                entity.setReleaseTime(idx.getReleaseTime());
                entity.setChangeTime(idx.getChangeTime());
                entity.setSubmitTime(idx.getSubmitTime());
                entity.setWhetherFirstOffline(idx.getWhetherFirstOffline());
                entity.setClickCount(idx.getClickCount());

                resultList.add(entity);
            }

            // 创建PageInfo对象
            PageInfo<ProfessionalTermEntity> pageInfo = new PageInfo<>(resultList);
            pageInfo.setPageNum(evt.getPageNo());
            pageInfo.setPageSize(evt.getPageSize());
            pageInfo.setTotal(searchHits.getTotalHits());

            return pageInfo;
        } catch (Exception e) {
            log.error("专业词汇全文检索失败", e);
            return new PageInfo<>(Collections.emptyList());
        }
    }

    /**
     * 同步专业词汇数据到ES索引
     * 此方法用于在索引结构更新后，将数据库中的专业词汇数据同步到ES
     *
     * @return 同步结果
     */
    @Override
    public ServiceResp syncProfessionalTermToEs() {
        try {
            // 查询所有专业词汇数据
            LambdaQueryWrapper<ProfessionalTermEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO);

            List<ProfessionalTermEntity> terms = professionalTermMapper.selectList(queryWrapper);
            log.info("查询到{}条专业词汇数据需要同步到ES", terms.size());

            if (CollUtil.isEmpty(terms)) {
                return ServiceResp.success("没有专业词汇数据需要同步");
            }

            // 将专业词汇数据转换为ES索引对象并保存
            List<KnowledgeBasesIdx> idxList = terms.stream().map(term -> {
                KnowledgeBasesIdx idx = new KnowledgeBasesIdx();
                // 设置主键
                idx.setKgKnowledgeBasesId(term.getKgProfessionalTermsId());
                // 设置各字段
                idx.setTerm(term.getTerm());
                idx.setChinese(term.getChinese());
                idx.setEnglish(term.getEnglish());
                idx.setChineseSynonym(term.getChineseSynonym());
                idx.setChineseDefinition(term.getChineseDefinition());
                idx.setEnglishDefinition(term.getEnglishDefinition());
                idx.setEnglishSynonym(term.getEnglishSynonym());
                idx.setMajor(term.getMajor());
                idx.setApplicationScene(term.getApplicationScene());
                idx.setIsTelecomInfoField(term.getIsTelecomInfoField());
                idx.setBasesType(CommonConstant.BASES_TYPE_PROFESSIONAL_TERM);
                idx.setKnowledgeName(term.getTerm());
                idx.setCreatedUserName(term.getCreatedUserName());
                idx.setCreatedTime(term.getCreatedTime());
                idx.setUpdatedUserName(term.getUpdatedUserName());
                idx.setUpdatedTime(term.getUpdatedTime());
                idx.setIsDeleted(term.getIsDeleted());
                idx.setState(term.getState());
                idx.setAuditStatus(term.getAuditStatus());
                idx.setFullText(); // 更新全文检索字段
                idx.setReviewer(term.getReviewer());
                idx.setAuditTime(term.getAuditTime());
                idx.setReleaseTime(term.getReleaseTime());
                idx.setChangeTime(term.getChangeTime());
                idx.setSubmitTime(term.getSubmitTime());
                idx.setWhetherFirstOffline(term.getWhetherFirstOffline());
                idx.setClickCount(term.getClickCount());
                return idx;
            }).collect(Collectors.toList());

            // 批量保存到ES
            elasticsearchRestTemplate.save(idxList);

            log.info("成功同步{}条专业词汇数据到ES", idxList.size());
            return ServiceResp.success("成功同步" + idxList.size() + "条专业词汇数据到ES");
        } catch (Exception e) {
            log.error("同步专业词汇数据到ES失败", e);
            return ServiceResp.fail("同步专业词汇数据到ES失败: " + e.getMessage());
        }
    }

    /**
     * 校验专业词汇参数
     *
     * @param evt 专业词汇参数
     * @throws KnowledgeBasesException 校验失败抛出异常
     */
    private void checkParams(ProfessionalTermEvt evt) throws KnowledgeBasesException {
        if (StrUtil.isBlank(evt.getTerm())) {
            throw new KnowledgeBasesException("术语/缩略语/专业名词不能为空");
        }
        if (StrUtil.isBlank(evt.getEnglish())) {
            throw new KnowledgeBasesException("英文不能为空");
        }
        if (StrUtil.isBlank(evt.getMajor())) {
            throw new KnowledgeBasesException("专业领域不能为空");
        }
        if (StrUtil.isBlank(evt.getApplicationScene())) {
            throw new KnowledgeBasesException("应用场景不能为空");
        }
        if (StrUtil.isBlank(evt.getIsTelecomInfoField())) {
            throw new KnowledgeBasesException("是否通信信息领域不能为空");
        }
    }

    /**
     * 填写或修改专业词汇评审结论
     *
     * @param evt 评审结论事件
     * @return 服务响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp addOrUpdateTermReviewConclusion(ProfessionalTermReviewConclusionEvt evt) {
        // 1. 构建部分更新的实体对象（只设置需要更新的字段）
        KnowledgeBasesIdx updateEntity = new KnowledgeBasesIdx();
        updateEntity.setKgKnowledgeBasesId(evt.getProfessionalTermId()); // @Id字段必须设置

        if (CollectionUtil.isEmpty(evt.getAuditDimensionList())) {
            // 删除原有的评审结论
            professionalTermAuditDimensionMapper.delete(
                    new QueryWrapper<ProfessionalTermAuditDimensionD>()
                            .eq("professional_term_id", evt.getProfessionalTermId())
            );

            // 添加新的评审结论
            ProfessionalTermAuditDimensionD auditDimension = new ProfessionalTermAuditDimensionD();
            auditDimension.setCreatedTime(new Date());
            auditDimension.setCreatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setUpdatedTime(new Date());
            auditDimension.setUpdatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setOrderNum(1);
            auditDimension.setComment(evt.getComment());
            auditDimension.setWhetherPass(String.valueOf(evt.getWhetherPass()));
            auditDimension.setProfessionalTermId(evt.getProfessionalTermId());
            professionalTermAuditDimensionMapper.insert(auditDimension);

            Date updateTime = new Date();
            if (CommonConstant.YES.equals(evt.getWhetherPass())) {
                // 更新专业词汇状态为审核通过
                professionalTermMapper.update(null, new UpdateWrapper<ProfessionalTermEntity>()
                        .set("state", ProfessionalStateEnum.AUDIT_PASS.getStateCode())
                        .set("updated_time", updateTime)
                        .set("updated_user_name", PtSecurityUtils.getUsername())
                        .set("reviewer", PtSecurityUtils.getUsername())
                        .set("audit_time", updateTime)
                        .eq("kg_professional_terms_id", evt.getProfessionalTermId()));
                updateEntity.setState(ProfessionalStateEnum.AUDIT_PASS.getStateCode());
            } else if (CommonConstant.NO.equals(evt.getWhetherPass())) {
                // 更新专业词汇状态为审核不通过
                professionalTermMapper.update(null, new UpdateWrapper<ProfessionalTermEntity>()
                        .set("state", ProfessionalStateEnum.AUDIT_NOT_PASS.getStateCode())
                        .set("updated_time", updateTime)
                        .set("updated_user_name", PtSecurityUtils.getUsername())
                        .set("reviewer", PtSecurityUtils.getUsername())
                        .set("audit_time", updateTime)
                        .eq("kg_professional_terms_id", evt.getProfessionalTermId()));
                updateEntity.setState(ProfessionalStateEnum.AUDIT_NOT_PASS.getStateCode());
            }

            // 更新ES索引，添加前缀profess_
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
                // 使用ES前缀标识
                String documentId = "profess_" + evt.getProfessionalTermId();
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(Document.parse(objectMapper.writeValueAsString(updateEntity)))
                        .build();

                elasticsearchRestTemplate.update(
                        updateQuery,
                        IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName())
                );
            } catch (Exception e) {
                if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                    log.error("更新专业词汇ES索引失败，}，异常信息：{}", e.getMessage());
                    log.error("更新专业词汇ES索引失败，，异常信息：", e);
                    throw new KnowledgeBasesException("更新失败:{}", e.getMessage());
                }
            }

            return ServiceResp.success("审核操作成功");
        }

        // 删除原有的评审结论
        professionalTermAuditDimensionMapper.delete(
                new QueryWrapper<ProfessionalTermAuditDimensionD>()
                        .eq("professional_term_id", evt.getProfessionalTermId())
        );

        // 批量添加新的评审结论
        int orderNum = 0;
        for (ProfessionalTermAuditDimensionD auditDimension : evt.getAuditDimensionList()) {
            auditDimension.setCreatedTime(new Date());
            auditDimension.setCreatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setUpdatedTime(new Date());
            auditDimension.setUpdatedUserName(PtSecurityUtils.getUsername());
            auditDimension.setOrderNum(++orderNum);
            auditDimension.setWhetherPass(String.valueOf(evt.getWhetherPass()));
            auditDimension.setProfessionalTermId(evt.getProfessionalTermId());
            professionalTermAuditDimensionMapper.insert(auditDimension);
        }

        Date updateTime = new Date();
        if (CommonConstant.PASS.equals(evt.getWhetherPass())) {
            // 更新专业词汇状态为审核通过
            professionalTermMapper.update(null, new UpdateWrapper<ProfessionalTermEntity>()
                    .set("state", ProfessionalStateEnum.AUDIT_PASS.getStateCode())
                    .set("updated_time", updateTime)
                    .set("updated_user_name", PtSecurityUtils.getUsername())
                    .set("reviewer", PtSecurityUtils.getUsername())
                    .set("audit_time", updateTime)
                    .eq("kg_professional_terms_id", evt.getProfessionalTermId()));
            updateEntity.setState(ProfessionalStateEnum.AUDIT_PASS.getStateCode());
        } else if (CommonConstant.NO_PASS.equals(evt.getWhetherPass())) {
            // 更新专业词汇状态为审核不通过
            professionalTermMapper.update(null, new UpdateWrapper<ProfessionalTermEntity>()
                    .set("state", ProfessionalStateEnum.AUDIT_NOT_PASS.getStateCode())
                    .set("updated_time", updateTime)
                    .set("updated_user_name", PtSecurityUtils.getUsername())
                    .set("reviewer", PtSecurityUtils.getUsername())
                    .set("audit_time", updateTime)
                    .eq("kg_professional_terms_id", evt.getProfessionalTermId()));
            updateEntity.setState(ProfessionalStateEnum.AUDIT_NOT_PASS.getStateCode());
        }

        // 更新ES索引，添加前缀profess_
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
            // 使用ES前缀标识
            String documentId = "profess_" + evt.getProfessionalTermId();
            UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                    .withDocument(Document.parse(objectMapper.writeValueAsString(updateEntity)))
                    .build();

            elasticsearchRestTemplate.update(
                    updateQuery,
                    IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName())
            );
        } catch (Exception e) {
            if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                log.error("更新专业词汇ES索引失败，异常信息：{}", e.getMessage());
                log.error("更新专业词汇ES索引失败，，异常信息：", e);
                throw new KnowledgeBasesException("更新失败:{}", e.getMessage());
            }
        }

        return ServiceResp.success("审核操作成功");
    }

    /**
     * 获取专业词汇评审结论
     *
     * @param professionalTermId 专业词汇ID
     * @return 评审结论列表
     */
    @Override
    public ServiceResp getTermReviewConclusion(Long professionalTermId) {
        if (professionalTermId == null) {
            return ServiceResp.fail("专业词汇ID不能为空");
        }

        List<ProfessionalTermAuditDimensionD> auditDimensions = professionalTermAuditDimensionMapper.selectList(
                new QueryWrapper<ProfessionalTermAuditDimensionD>()
                        .eq("professional_term_id", professionalTermId)
                        .orderByAsc("order_num")
        );

        return ServiceResp.success("获取成功", auditDimensions);
    }

    /**
     * 批量提交专业词汇
     *
     * @param ids 专业词汇ID列表
     * @return 服务响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp batchSubmitTerms(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return ServiceResp.fail("请选择需要提交的专业词汇");
        }

        // 获取所有待提交的专业词汇
        List<ProfessionalTermEntity> termList = professionalTermMapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(termList)) {
            return ServiceResp.fail("未找到有效的专业词汇");
        }

        // 验证必填字段
        List<String> errorMsgs = new ArrayList<>();
        for (ProfessionalTermEntity term : termList) {
            StringBuilder errorMsg = new StringBuilder();

            // 检查唯一键约束和必填字段
            if (StrUtil.isBlank(term.getTerm())) {
                errorMsg.append("术语/缩略语/专业名词不能为空; ");
            }
            if (StrUtil.isBlank(term.getEnglish())) {
                errorMsg.append("英文不能为空; ");
            }
            if (StrUtil.isBlank(term.getMajor())) {
                errorMsg.append("专业领域不能为空; ");
            }
            if (StrUtil.isBlank(term.getApplicationScene())) {
                errorMsg.append("应用场景不能为空; ");
            }
            if (StrUtil.isBlank(term.getIsTelecomInfoField())) {
                errorMsg.append("是否通信信息领域不能为空; ");
            }

            if (errorMsg.length() > 0) {
                errorMsgs.add("专业词汇 [" + term.getTerm() + "] 存在问题: " + errorMsg);
            }
        }

        if (!errorMsgs.isEmpty()) {
            return ServiceResp.fail(String.join("<br/>", errorMsgs));
        }

        // 批量更新状态为"待审核"
        Date now = new Date();
        String currentUsername = PtSecurityUtils.getUsername();

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);

        for (ProfessionalTermEntity term : termList) {
            term.setState(ProfessionalStateEnum.PENDING_AUDIT.getStateCode());
            term.setAuditStatus(CommonConstant.NO);  // 设置为待审核
            term.setUpdatedTime(now);
            term.setUpdatedUserName(currentUsername);
            term.setSubmitTime(now);

            // 更新数据库
            professionalTermMapper.updateById(term);

            // 更新ES索引
            try {
                // 构建KnowledgeBasesIdx对象用于ES索引
                KnowledgeBasesIdx idx = new KnowledgeBasesIdx();
                BeanUtil.copyProperties(term, idx);
                // 设置主键
                idx.setKgKnowledgeBasesId(term.getKgProfessionalTermsId());
                // 设置状态字段
                idx.setState(term.getState());
                idx.setAuditStatus(term.getAuditStatus());
                idx.setUpdatedTime(term.getUpdatedTime());
                idx.setUpdatedUserName(term.getUpdatedUserName());
                idx.setSubmitTime(term.getSubmitTime());

                // 使用ES前缀标识
                String documentId = "profess_" + term.getKgProfessionalTermsId();
                UpdateQuery updateQuery = UpdateQuery.builder(documentId)
                        .withDocument(Document.parse(objectMapper.writeValueAsString(idx)))
                        .build();

                elasticsearchRestTemplate.update(
                        updateQuery,
                        IndexCoordinates.of(knowledgeBaseConfig.getBaseInfo().getIndexName())
                );
            } catch (Exception e) {
                if (!e.getMessage().contains("Created") && !e.getMessage().contains("200 OK") && !e.getMessage().contains("201 OK")) {
                    log.error("更新专业词汇ES索引失败，ID: {}，异常信息：{}", term.getKgProfessionalTermsId(), e.getMessage());
                    log.error("更新专业词汇ES索引失败，，异常信息：", e);
                    throw new KnowledgeBasesException("更新失败:{}", e.getMessage());
                }
            }
        }

        return ServiceResp.success("批量提交成功，共提交 " + termList.size() + " 条专业词汇");
    }

    @Override
    public void exportTermsToExcel(ExportProfessionalTermEvt evt, HttpServletResponse response) {
        log.info("开始导出专业词汇，参数：{}", evt);

        try {
            // 获取要导出的专业词汇数据
            List<ProfessionalTermEntity> termList = getTermsForExport(evt);

            if (CollectionUtil.isEmpty(termList)) {
                log.warn("没有找到要导出的专业词汇数据");
                throw new KnowledgeBasesException("没有找到要导出的专业词汇数据");
            }

            // 转换数据并生成Excel
            generateTermExcelFile(termList, evt.getFileName(), response);

            batchUpdateTermReportStatus(termList.stream().map(ProfessionalTermEntity::getKgProfessionalTermsId).collect(Collectors.toList())
                    , null, ExportStatusEnum.EXPORTED.getCode(), "");

            log.info("专业词汇导出完成，共导出{}条数据", termList.size());
        } catch (Exception e) {
            log.error("导出专业词汇失败", e);
            throw new KnowledgeBasesException("导出专业词汇失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp batchUpdateReportStatus(BatchUpdateTermReportStatusEvt evt) {
        log.info("批量修改专业词汇上报状态，参数：{}", evt);

        try {
            // 参数校验
            if (evt.getOperationType() == null) {
                return ServiceResp.fail("操作类型不能为空");
            }

            if (StringUtils.isBlank(evt.getReportStatus())) {
                return ServiceResp.fail("上报状态不能为空");
            }

            // 验证上报状态值是否有效
            if (!isValidReportStatus(evt.getReportStatus())) {
                return ServiceResp.fail("无效的上报状态值");
            }

            // 获取要修改的专业词汇ID列表
            List<Long> termIds = getTermIdsForUpdate(evt);

            if (CollectionUtil.isEmpty(termIds)) {
                return ServiceResp.fail("没有找到要修改的专业词汇数据");
            }

            // 批量更新上报状态
            int updateCount = batchUpdateTermReportStatus(termIds, evt.getReportStatus(), evt.getExportStatus(), evt.getReportDescription());

            log.info("批量修改专业词汇上报状态完成，共修改{}条数据", updateCount);
            return ServiceResp.success("批量修改成功，共修改" + updateCount + "条数据");

        } catch (Exception e) {
            log.error("批量修改专业词汇上报状态失败", e);
            return ServiceResp.fail("批量修改失败：" + e.getMessage());
        }
    }

        /**
     * 构建公共查询条件（包含权限处理逻辑）
     */
    private void buildCommonQueryConditions(ProfessionalTermQueryEvt evt) {
        if (StringUtil.isEmpty(evt.getBeforeOneUser())) {
            evt.setBeforeOneUser(PtSecurityUtils.getUsername());
        }
        if (CollUtil.isNotEmpty(evt.getCurrentUserList()) && CollUtil.isEmpty(evt.getRegion())) {
            evt.setRegion(evt.getCurrentUserList().stream().map(Integer::parseInt).collect(Collectors.toList()));
        }
        String username = PtSecurityUtils.getUsername();
        // todo 测试
        //  username = "xulinsen";

        if (CollUtil.isNotEmpty(evt.getState()) && (evt.getState().contains(CaseStateEnum.PENDING_AUDIT.getStateValue()))) {
            KgAuditorAllocationRuleConfigD kgAuditorAllocationRuleConfigD = auditorAllocationRuleConfigMapper.selectById(BigInteger.ONE);
            KgBasesAuditPersonDEvt kgAuditPersonDEvt = new KgBasesAuditPersonDEvt();
            kgAuditPersonDEvt.setRealName(username);
            if (evt.getType() == null || (evt.getType() != null && !CommonConstant.YES.equals(evt.getType()))) {
                if (StringUtils.isNotBlank(username)) {
                    List<KgBasesAuditPersonDVm> kgAuditPersonDVms = basesAuditPersonMapper.getBasesAuditPerson(kgAuditPersonDEvt);
                    if (CollUtil.isNotEmpty(kgAuditPersonDVms)) {
                        KgBasesAuditPersonDVm kgAuditPersonDVm = kgAuditPersonDVms.get(0);
                        //省级管理员不做限制
                        if (CommonConstant.NO_PASS.equals(kgAuditPersonDVm.getWhetherProvinceAdmin())) {
                            if (!CommonConstant.PASS.equals(kgAuditPersonDVm.getWhetherRegionAdmin())) {
                                //不是区域管理员，就需要通过分配规则过滤数据
                                switch (kgAuditorAllocationRuleConfigD.getValue()) {
                                    // todo 还是要处理
                                    case "1":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(kgAuditPersonDVm.getMajor().split(","))));
                                        break;
                                    case "2":
                                        evt.setStateRegion(kgAuditPersonDVm.getRegionList().stream().map(Integer::valueOf).collect(Collectors.toList()));
                                        break;
                                    case "3":
                                        evt.setStateMajor(new ArrayList<>(Arrays.asList(kgAuditPersonDVm.getMajor().split(","))));
                                        evt.setStateRegion(kgAuditPersonDVm.getRegionList().stream().map(Integer::valueOf).collect(Collectors.toList()));
                                        break;
                                }
                            } else {
                                //是区域管理员，只需要过滤区域，不受专业限制
                                evt.setStateRegion(kgAuditPersonDVm.getRegionList().stream().map(Integer::valueOf).collect(Collectors.toList()));
                            }
                        }

                    }
                }
            }
        }
    }

    /**
     * 使用与page方法相同的查询逻辑查询专业词汇数据
     * @param evt 查询条件
     * @param onlySelectId 是否只查询ID字段（用于提高性能）
     * @return 专业词汇实体列表
     */
    private List<ProfessionalTermEntity> queryTermsWithConditions(ProfessionalTermQueryEvt evt, boolean onlySelectId) {
        // 处理权限和公共查询条件
        evt.setCountTotal(true);
        buildCommonQueryConditions(evt);

        if (StringUtils.isNotBlank(evt.getFullText())) {
            // ES全文检索查询
            return queryTermsFromElasticsearch(evt, onlySelectId);
        } else {
            // 普通数据库查询
            return queryTermsFromDatabase(evt, onlySelectId);
        }
    }

    /**
     * 从Elasticsearch查询专业词汇数据
     */
    private List<ProfessionalTermEntity> queryTermsFromElasticsearch(ProfessionalTermQueryEvt evt, boolean onlySelectId) {
        // 构建ES查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 只查询专业词汇类型
        boolQuery.must(QueryBuilders.termQuery("basesType", "3"));

        // 只查询未删除的记录
        boolQuery.must(QueryBuilders.termQuery("isDeleted", CommonConstant.NO));

        // 全文检索
        if (StringUtils.isNotEmpty(evt.getFullText())) {
            boolQuery.must(QueryBuilders.matchPhraseQuery("fullText", evt.getFullText()));
        }

        // 权限专业过滤（优先）
        if (CollectionUtils.isNotEmpty(evt.getStateMajor())) {
            boolQuery.filter(QueryBuilders.termsQuery("major", evt.getStateMajor()));
        } else if (StringUtils.isNotBlank(evt.getMajor())) {
            evt.setMajorList(new ArrayList<>(Arrays.asList(evt.getMajor().split(","))));
            if (CollectionUtils.isNotEmpty(evt.getMajorList())) {
                boolQuery.filter(QueryBuilders.termsQuery("major", evt.getMajorList()));
            }
        }

        // 权限区域过滤（优先）
        if (CollectionUtils.isNotEmpty(evt.getStateRegion())) {
            boolQuery.filter(QueryBuilders.termsQuery("region", evt.getStateRegion()));
        }
        // 表单区域过滤
        if (CollectionUtils.isNotEmpty(evt.getRegion())) {
            boolQuery.filter(QueryBuilders.termsQuery("region", evt.getRegion()));
        }

        // 其他状态过滤
        if (CollectionUtils.isNotEmpty(evt.getState())) {
            boolQuery.filter(QueryBuilders.termsQuery("state", evt.getState()));
        }

        // 创建NativeSearchQuery
        org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder searchQueryBuilder = new org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder();
        searchQueryBuilder.withQuery(boolQuery);

        // 设置分页为查询所有（不分页）
        searchQueryBuilder.withPageable(org.springframework.data.domain.PageRequest.of(0, Integer.MAX_VALUE));

        // 设置排序（按更新时间和创建时间降序）
        searchQueryBuilder.withSort(org.elasticsearch.search.sort.SortBuilders.fieldSort("updatedTime").order(org.elasticsearch.search.sort.SortOrder.DESC));
        searchQueryBuilder.withSort(org.elasticsearch.search.sort.SortBuilders.fieldSort("createdTime").order(org.elasticsearch.search.sort.SortOrder.DESC));

        // 执行查询
        org.springframework.data.elasticsearch.core.SearchHits<KnowledgeBasesIdx> searchHits =
                elasticsearchRestTemplate.search(searchQueryBuilder.build(), KnowledgeBasesIdx.class);

        // 转换查询结果为ProfessionalTermEntity
        List<ProfessionalTermEntity> resultList = new ArrayList<>();
        for (org.springframework.data.elasticsearch.core.SearchHit<KnowledgeBasesIdx> hit : searchHits.getSearchHits()) {
            KnowledgeBasesIdx idx = hit.getContent();
            ProfessionalTermEntity entity = new ProfessionalTermEntity();
            
            if (onlySelectId) {
                // 只设置ID字段
                entity.setKgProfessionalTermsId(idx.getKgKnowledgeBasesId());
            } else {
                // 完整映射字段
                BeanUtil.copyProperties(idx, entity);
                entity.setKgProfessionalTermsId(idx.getKgKnowledgeBasesId());
                entity.setTerm(idx.getTerm());
                entity.setChinese(idx.getChinese());
                entity.setEnglish(idx.getEnglish());
                entity.setChineseSynonym(idx.getChineseSynonym());
                entity.setChineseDefinition(idx.getChineseDefinition());
                entity.setEnglishDefinition(idx.getEnglishDefinition());
                entity.setEnglishSynonym(idx.getEnglishSynonym());
                entity.setMajor(idx.getMajor());
                entity.setApplicationScene(idx.getApplicationScene());
                entity.setIsTelecomInfoField(idx.getIsTelecomInfoField());
                entity.setCreatedUserName(idx.getCreatedUserName());
                entity.setCreatedTime(idx.getCreatedTime());
                entity.setUpdatedUserName(idx.getUpdatedUserName());
                entity.setUpdatedTime(idx.getUpdatedTime());
                entity.setIsDeleted(idx.getIsDeleted());
                entity.setState(idx.getState());
                entity.setAuditStatus(idx.getAuditStatus());
                entity.setSearchNumber(idx.getSearchNumber());
                entity.setRegion(idx.getRegion() == null ? null : String.valueOf(idx.getRegion()));
                entity.setReviewer(idx.getReviewer());
                entity.setAuditTime(idx.getAuditTime());
                entity.setReleaseTime(idx.getReleaseTime());
                entity.setChangeTime(idx.getChangeTime());
                entity.setSubmitTime(idx.getSubmitTime());
                entity.setWhetherFirstOffline(idx.getWhetherFirstOffline());
                entity.setClickCount(idx.getClickCount());
            }

            resultList.add(entity);
        }

        return resultList;
    }

    /**
     * 从数据库查询专业词汇数据
     */
    private List<ProfessionalTermEntity> queryTermsFromDatabase(ProfessionalTermQueryEvt evt, boolean onlySelectId) {
        LambdaQueryWrapper<ProfessionalTermEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO);

        // 如果只需要查询ID，设置select字段
        if (onlySelectId) {
            queryWrapper.select(ProfessionalTermEntity::getKgProfessionalTermsId);
        }

        // 添加查询条件
        if (StringUtils.isNotBlank(evt.getTerm())) {
            queryWrapper.like(ProfessionalTermEntity::getTerm, evt.getTerm());
        }
        if (StringUtils.isNotBlank(evt.getChinese())) {
            queryWrapper.like(ProfessionalTermEntity::getChinese, evt.getChinese());
        }
        if (StringUtils.isNotBlank(evt.getEnglish())) {
            queryWrapper.like(ProfessionalTermEntity::getEnglish, evt.getEnglish());
        }

        // 支持审核状态多选查询
        if (StringUtils.isNotBlank(evt.getAuditStatus())) {
            String[] auditStatusArray = evt.getAuditStatus().split(",");
            if (auditStatusArray.length > 1) {
                queryWrapper.in(ProfessionalTermEntity::getAuditStatus, Arrays.asList(auditStatusArray));
            } else {
                queryWrapper.eq(ProfessionalTermEntity::getAuditStatus, evt.getAuditStatus());
            }
        }

        if (StringUtils.isNotBlank(evt.getCreatedUserName())) {
            queryWrapper.like(ProfessionalTermEntity::getCreatedUserName, evt.getCreatedUserName());
        }
        if (evt.getCreateTimeStart() != null) {
            queryWrapper.ge(ProfessionalTermEntity::getCreatedTime, evt.getCreateTimeStart());
        }
        if (evt.getCreateTimeEnd() != null) {
            queryWrapper.le(ProfessionalTermEntity::getCreatedTime, evt.getCreateTimeEnd());
        }

        // 添加state状态过滤条件
        if (CollectionUtil.isNotEmpty(evt.getState())) {
            queryWrapper.in(ProfessionalTermEntity::getState, evt.getState());
        }

        // 支持专业领域多选查询
        if (StringUtils.isNotBlank(evt.getMajor())) {
            String[] majorArray = evt.getMajor().split(",");
            if (majorArray.length > 1) {
                queryWrapper.and(wrapper -> {
                    for (String major : majorArray) {
                        wrapper.or().apply("major ~* ('(^|,)' || {0} || '(,|$)')", major);
                    }
                });
            } else {
                queryWrapper.eq(ProfessionalTermEntity::getMajor, evt.getMajor());
            }
        }

        // 支持应用场景多选查询
        if (StringUtils.isNotBlank(evt.getApplicationScene())) {
            String[] sceneArray = evt.getApplicationScene().split(",");
            if (sceneArray.length > 1) {
                queryWrapper.and(wrapper -> {
                    for (String scene : sceneArray) {
                        wrapper.or().apply("application_scene ~* ('(^|,)' || {0} || '(,|$)')", scene);
                    }
                });
            } else {
                queryWrapper.eq(ProfessionalTermEntity::getApplicationScene, evt.getApplicationScene());
            }
        }

        // 添加stateMajor过滤条件
        if (CollectionUtil.isNotEmpty(evt.getStateMajor())) {
            queryWrapper.and(wrapper -> {
                for (String major : evt.getStateMajor()) {
                    wrapper.or().apply("major ~* ('(^|,)' || {0} || '(,|$)')", major);
                }
            });
        }

        // 添加stateRegion过滤条件
        if (CollectionUtil.isNotEmpty(evt.getStateRegion())) {
            queryWrapper.in(ProfessionalTermEntity::getRegion, evt.getStateRegion().stream().map(String::valueOf).collect(Collectors.toList()));
        }

        // todo 查询条件的 区域
        if (CollectionUtil.isNotEmpty(evt.getRegion())) {
            queryWrapper.in(ProfessionalTermEntity::getRegion, evt.getRegion().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        
        // 支持上报状态查询
        if (CollectionUtil.isNotEmpty(evt.getReportStatusList())) {
            queryWrapper.in(ProfessionalTermEntity::getReportStatus, evt.getReportStatusList());
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(ProfessionalTermEntity::getUpdatedTime).orderByDesc(ProfessionalTermEntity::getCreatedTime);

        // 使用professionalTermMapper
        return professionalTermMapper.selectList(queryWrapper);
    }

    private List<ProfessionalTermEntity> getTermsForExport(ExportProfessionalTermEvt evt) {
        if (ExportProfessionalTermEvt.EXPORT_TYPE_SELECTED == evt.getExportType()) {
            // 选择导出：根据ID列表查询
            if (CollectionUtil.isEmpty(evt.getTermIds())) {
                throw new KnowledgeBasesException("选择导出时专业词汇ID列表不能为空");
            }

            LambdaQueryWrapper<ProfessionalTermEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(ProfessionalTermEntity::getKgProfessionalTermsId, evt.getTermIds())
                   .eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO)
                   .orderByDesc(ProfessionalTermEntity::getCreatedTime);

            return professionalTermMapper.selectList(wrapper);

        } else if (ExportProfessionalTermEvt.EXPORT_TYPE_ALL == evt.getExportType()) {
            // 全选导出：使用与page方法相同的查询逻辑
            if (evt.getQueryCondition() == null) {
                throw new KnowledgeBasesException("全选导出时查询条件不能为空");
            }

            ProfessionalTermQueryEvt queryEvt = evt.getQueryCondition();
            return queryTermsWithConditions(queryEvt, false);
        } else {
            throw new KnowledgeBasesException("不支持的导出类型：" + evt.getExportType());
        }
    }

    /**
     * 生成专业词汇Excel文件
     */
    private void generateTermExcelFile(List<ProfessionalTermEntity> termList, String fileName, HttpServletResponse response) throws IOException {
        Workbook workbook = null;
        try {
            // 读取模板文件
            ClassPathResource templateResource = new ClassPathResource("excel/template/专业词汇导出模板.xlsx");
            if (!templateResource.exists()) {
                throw new KnowledgeBasesException("专业词汇导出模板文件不存在");
            }

            // 打开模板文件
            workbook = new XSSFWorkbook(templateResource.getInputStream());

            // 获取第一个sheet页（索引为0）
            Sheet dataSheet = workbook.getSheetAt(0);

            // 转换数据
            List<String[]> dataList = convertTermsToExcelData(termList);

            // 从第二行开始写入数据（索引为1，因为第一行是表头）
            int rowIndex = 1;
            for (String[] rowData : dataList) {
                Row row = dataSheet.createRow(rowIndex++);
                for (int i = 0; i < rowData.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(rowData[i] != null ? rowData[i] : "");
                }
            }

            // 生成带时间戳的文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String finalFileName = (StringUtils.isNotBlank(fileName) ? fileName : "专业词汇导出数据") + "_" + timestamp + ".xlsx";

            // 设置响应头
            ExcelUtil.setBrowser(response, null, finalFileName);

            // 写入响应流
            workbook.write(response.getOutputStream());

        } catch (Exception e) {
            log.error("生成专业词汇Excel文件失败", e);
            throw new IOException("生成专业词汇Excel文件失败：" + e.getMessage());
        } finally {
            // 确保workbook资源被正确关闭
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook失败", e);
                }
            }
        }
    }

    /**
     * 将专业词汇数据转换为Excel数据格式
     */
    private List<String[]> convertTermsToExcelData(List<ProfessionalTermEntity> termList) {
        List<String[]> dataList = new ArrayList<>();
        
        if (CollectionUtil.isEmpty(termList)) {
            return dataList;
        }

        // 批量获取所有需要的字典数据，一次性查询提升性能
        Map<String, Map<String, String>> allDictionaryMaps = initAllDictionaryMaps();

        for (ProfessionalTermEntity term : termList) {
            String[] rowData = new String[14];

            // 术语/缩略语/专业名词
            rowData[0] = StringUtils.isNotBlank(term.getTerm()) ? term.getTerm() : "";

            // 英文 English
            rowData[1] = StringUtils.isNotBlank(term.getEnglish()) ? term.getEnglish() : "";

            // 中文 Chinese
            rowData[2] = StringUtils.isNotBlank(term.getChinese()) ? term.getChinese() : "";

            // 中文同义词 Chinese Synonym
            rowData[3] = StringUtils.isNotBlank(term.getChineseSynonym()) ? term.getChineseSynonym() : "";

            // 中文定义 Chinese Definition
            rowData[4] = StringUtils.isNotBlank(term.getChineseDefinition()) ? term.getChineseDefinition() : "";

            // 英文同义词 English Synonym
            rowData[5] = StringUtils.isNotBlank(term.getEnglishSynonym()) ? term.getEnglishSynonym() : "";

            // 英文定义 English Definition
            rowData[6] = StringUtils.isNotBlank(term.getEnglishDefinition()) ? term.getEnglishDefinition() : "";

            // 一级场景标签 - 查询父字典
            rowData[7] = getParentDictName(term.getApplicationScene(), allDictionaryMaps.get("C12_PARENT"));

            // 二级场景标签 - 当前字典值
            rowData[8] = getDictName(term.getApplicationScene(), allDictionaryMaps.get("C12"));

            // 一级专业标签 - 查询父字典
            rowData[9] = getParentDictName(term.getMajor(), allDictionaryMaps.get("C2_PARENT"));

            // 二级专业标签 - 当前字典值
            rowData[10] = getDictName(term.getMajor(), allDictionaryMaps.get("C2"));

            // 是否通信信息领域 - 0/1转换为否/是
            rowData[11] = convertTelecomInfoField(term.getIsTelecomInfoField());

            // 创建人
            rowData[12] = getTelPhone(term.getCreatedUserName());

            rowData[13] = StringUtils.isNotBlank(term.getReviewer()) ? term.getReviewer() : "";

            dataList.add(rowData);
        }

        return dataList;
    }

    /**
     * 默认用 13328223567 账号
     * @param userName
     * @return
     */
    public String getTelPhone(String userName) {
        String defaultTel = "13328223567";
        if (StringUtils.isBlank(userName)) {
            return defaultTel;
        }
        QryUserEvt user = new QryUserEvt();
        user.setLoginName(userName);
        try {
            SimpleUserVm simpleUserVm;
            simpleUserVm = smQryClient.qrySimpleUser(user).getBody();
            if (simpleUserVm != null) {
                return simpleUserVm.getTelephone();
            }
            return defaultTel;
        } catch (Exception e) {
            log.error("请求qry服务报错，暂时跳过", e);
            return defaultTel;
        }

    }

    /**
     * 初始化所有字典映射，一次性批量查询所有字典数据
     */
    private Map<String, Map<String, String>> initAllDictionaryMaps() {
        Map<String, Map<String, String>> allMaps = new HashMap<>();
        
        // 定义需要查询的字典类型
        List<String> dictTypes = Arrays.asList("C2", "C12");
        
        // 一次性查询所有字典数据
        List<DictionaryEntity> allDictList = dictionaryMapper.findByCodeTypeIn(dictTypes);
        
        if (CollectionUtil.isNotEmpty(allDictList)) {
            // 按字典类型分组
            Map<String, List<DictionaryEntity>> dictGroups = allDictList.stream()
                    .collect(Collectors.groupingBy(DictionaryEntity::getCodeType));
            
            // 处理每个字典类型
            for (Map.Entry<String, List<DictionaryEntity>> entry : dictGroups.entrySet()) {
                String dictType = entry.getKey();
                List<DictionaryEntity> dictList = entry.getValue();
                
                // 构建ID到名称的映射
                Map<String, String> idToNameMap = new HashMap<>();
                Map<String, String> idToParentNameMap = new HashMap<>();
                Map<Long, String> tempIdToNameMap = new HashMap<>();
                
                // 先构建临时的Long类型ID到名称映射，用于查找父字典
                for (DictionaryEntity dict : dictList) {
                    tempIdToNameMap.put(dict.getBdpDirId(), dict.getCodeName());
                }
                
                // 构建最终的字符串ID映射
                for (DictionaryEntity dict : dictList) {
                    String dictId = String.valueOf(dict.getBdpDirId());
                    String dictName = dict.getCodeName();
                    
                    idToNameMap.put(dictId, dictName);
                    
                    // 如果有父ID，查找父字典名称
                    if (dict.getParentId() != null) {
                        String parentName = tempIdToNameMap.get(dict.getParentId());
                        if (StringUtils.isNotBlank(parentName)) {
                            idToParentNameMap.put(dictId, parentName);
                        }
                    }
                }
                
                allMaps.put(dictType, idToNameMap);
                allMaps.put(dictType + "_PARENT", idToParentNameMap);
            }
        }
        
        return allMaps;
    }

    /**
     * 获取字典名称
     */
    private String getDictName(String dictId, Map<String, String> dictMap) {
        if (StringUtils.isBlank(dictId) || dictMap == null) {
            return "";
        }
        return dictMap.getOrDefault(dictId.trim(), "");
    }

    /**
     * 获取父字典名称
     */
    private String getParentDictName(String dictId, Map<String, String> parentDictMap) {
        if (StringUtils.isBlank(dictId) || parentDictMap == null) {
            return "";
        }
        return parentDictMap.getOrDefault(dictId.trim(), "");
    }

    /**
     * 转换通信信息领域字段，将0/1转换为否/是
     */
    private String convertTelecomInfoField(String isTelecomInfoField) {
        if (StringUtils.isBlank(isTelecomInfoField)) {
            return "";
        }
        
        switch (isTelecomInfoField.trim()) {
            case "0":
                return "否";
            case "1":
                return "是";
            default:
                return isTelecomInfoField;
        }
    }

    /**
     * 根据操作类型获取要修改的专业词汇ID列表
     */
    private List<Long> getTermIdsForUpdate(BatchUpdateTermReportStatusEvt evt) {
        if (BatchUpdateTermReportStatusEvt.OPERATION_TYPE_SELECTED == evt.getOperationType()) {
            // 选择修改：直接使用传入的ID列表
            if (CollectionUtil.isEmpty(evt.getTermIds())) {
                throw new KnowledgeBasesException("选择修改时专业词汇ID列表不能为空");
            }
            return evt.getTermIds();

        } else if (BatchUpdateTermReportStatusEvt.OPERATION_TYPE_ALL == evt.getOperationType()) {
            // 全选修改：使用与page方法相同的查询逻辑
            if (evt.getQueryCondition() == null) {
                throw new KnowledgeBasesException("全选修改时查询条件不能为空");
            }

            ProfessionalTermQueryEvt queryEvt = evt.getQueryCondition();
            List<ProfessionalTermEntity> termList = queryTermsWithConditions(queryEvt, true);
            return termList.stream()
                    .map(ProfessionalTermEntity::getKgProfessionalTermsId)
                    .collect(Collectors.toList());
        } else {
            throw new KnowledgeBasesException("不支持的操作类型：" + evt.getOperationType());
        }
    }

    /**
     * 验证上报状态值是否有效
     */
    private boolean isValidReportStatus(String reportStatus) {
        return ReportStatusEnum.NOT_REPORT.getCode().equals(reportStatus) ||
                ReportStatusEnum.REPORTED.getCode().equals(reportStatus) ||
                ReportStatusEnum.REPORT_FAILED.getCode().equals(reportStatus);
    }

    /**
     * 批量更新专业词汇上报状态
     */
    private int batchUpdateTermReportStatus(List<Long> termIds, String reportStatus, String exportStatus, String reportDescription) {
        if (CollectionUtil.isEmpty(termIds)) {
            return 0;
        }

        Date currentTime = new Date();
        String currentUser = PtSecurityUtils.getUsername();

        // 使用MyBatis-Plus的批量更新
        LambdaUpdateWrapper<ProfessionalTermEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProfessionalTermEntity::getKgProfessionalTermsId, termIds)
                        .eq(ProfessionalTermEntity::getIsDeleted, CommonConstant.NO)
                    .set(StringUtils.isNotBlank(reportStatus), ProfessionalTermEntity::getReportStatus, reportStatus)
                    .set(StringUtils.isNotBlank(exportStatus), ProfessionalTermEntity::getExportStatus, exportStatus)
                    .set(ProfessionalTermEntity::getReportDescription, reportDescription)
                    .set(ProfessionalTermEntity::getReportTime, currentTime)
                    .set(ProfessionalTermEntity::getUpdatedUserName, currentUser)
                    .set(ProfessionalTermEntity::getUpdatedTime, currentTime);

        return professionalTermMapper.update(null, updateWrapper);
    }
}
 