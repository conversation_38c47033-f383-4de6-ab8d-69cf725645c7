package com.ffcs.oss.kg.system.vm.cot;

import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.data.model.evt.knowledgeBases.cot.KgQaCotAuditDimensionD;
import com.ffcs.oss.kg.data.rd.entity.qa.KgQaPairAuditDimensionD;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 问答对详情视图模型
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "问答Cot详情视图模型")
public class QaCotInfoVm implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "问答Cot信息")
    private KgQaCotD qaCot;
    
    /**
     * 最新审核评论
     */
    private KgQaCotAuditDimensionD latestAuditComment;
} 