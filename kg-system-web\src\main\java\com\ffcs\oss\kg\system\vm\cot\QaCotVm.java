package com.ffcs.oss.kg.system.vm.cot;

import com.ffcs.oss.kg.data.model.entity.KgQaCotD;
import com.ffcs.oss.kg.data.model.entity.KgQaPairD;
import com.ffcs.oss.kg.data.model.vm.know.MajorStatisticsVm;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 问答对列表视图模型
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "问答Cot列表视图模型")
public class QaCotVm {

    @ApiModelProperty(value = "问答Cot列表分页信息")
    private PageInfo<KgQaCotD> qaCotPageInfo;

    @ApiModelProperty(value = "专业统计列表")
    private List<MajorStatisticsVm> allMajorStatisticsList;

    @ApiModelProperty(value = "专业总数")
    private Integer majorTotal;
} 