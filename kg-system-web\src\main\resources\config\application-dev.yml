#数据库以及ES 连接
spring:
  datasource:
    url: ******************************************************************************=${spring.application.name}
    username: ansagent
    password: s!v9C#Qnd8B2
    driver-class-name: org.postgresql.Driver
  elasticsearch:
    rest:
      #uris: *************:9201,*************:9201,*************:9201
      #username: elastic
      #password: E<PERSON>(nVmyxeMcK36G5CO9KCe8TDgsno15Vw5i)
      # es 8.x 版本斗门
      uris: 192.168.35.234:19201
      username: elastic
      password: <PERSON><PERSON>(AkFzcNIAbVYG4Wr/dA9dAM8FoMaTGGCb)

#ctgcache 配置
ctg-cache:
  maxTotal: 500
  maxIdle: 100
  minIdle: 5
  maxWaitMillis: 3000
  period: 3000
  monitorTimeout: 200
  enabled: false
  expireTime: 36000
  ins-config:
    - name: PORTAL-CENTER
      password: ENC(j+f/lPPIhjmx6xDKqDIjrJJtYAPa1dKq)
      cluster-nodes: *************:7001,*************:7002,*************:7001,*************:7002,*************:7001,*************:7002,*************:7001,*************:7002,*************:7001,*************:7002
  db-config:
    - name: PORTAL_CATCH_LOGIN
      ins-name: PORTAL-CENTER
      database: 1

# 图数据库配置
nebula:
  spaceEnable: false
  enable: false
  address[0]:
    host: *************
    port: 9769
  address[1]:
    host: *************
    port: 9769
  username: cndids
  password: ENC(hiHWKJrRGTzz3++OqDOcVYbiHJTdpN32)
  reconnect: true
  space: knowledge_graph
  max-conns-size: 200
  waitTime: 15000

# 日子级别
logging:
  level:
    root: info

# 旧功能配置
baidu:
  ak:
    - fhctvAA1uYcqDQsGPKhP1dCz5hir8Wsd
    - 10HcRn0weGVHAIMR7hH7YSRqoXY2j8c4
    - whfA7NPUGpfxZGDr6CjonUAf6ptbRO36
    - lFlcSQe1xGyQrppyjXLvmeHbdLPSeIfG
    - DGCcnWBlHlScgxeTjKP41STnCjOPIzrj
    - dWdmhK3qDYDZcStnUrxPoGZdFlpUKhTh
    - f8hl4ug6oBoSEtlqPumzHM0pBz7uBHi9
    - HreU0ElAthksP2RfYt80jZvmzWvMWZih
    - Fsy8OgBLASSY7h4XW8Wi67HZ4tYULZUT
    - Y1OI1iN0MuiisWNvTkKuz2m7Pdd9urGR
    - LUDzBIx2FZMADPXakA0U4PgULg5M6Xf3
    - PXAOXbRBkzVCPsmeT5C7XuXNW4ERuEv1
    - urUwh1xXWjT9bGCV33iG2dF9bIN0Gp0t

#测试环境小文件系统
ctdfs:
  host: **************
  port: 21
  userName: ffcs_zstp
  passWord: ENC(D6gH7fTM6A7OoZdTRYNkLlP1ZKtRxP0L)
  baseUrl: /ftp/知识图谱_ffcs_zstp/file
  # 下面的为小文件系统
  restPort: 8995
  restPassword: ENC(CYlXi9ZmmqY3cpWlIiMF5AtB+AlvDoWb)
  timeDiff: 2000
  scheme: aaa
  point: aaa
  # 下面是MinIO配置 - 使用负载均衡器地址连接MinIO集群
  # 推荐使用负载均衡器而不是直接连接多个节点，更可靠且易于维护
  endpoint: http://*************:9000
  # 如果没有负载均衡器，可以使用其中一个节点地址
  # endpoint: http://*************:9000
  accessKey: rag_flow
  secretKey: infini_rag_flow
  bucketName: kg-files
  type: minio
  #  type: ftp
  encryption: MD5

# 图谱同步配置 - 定时同步
scheduled:
  batchSize: 300
  cron:
    # 每天早上5点执行
    #fault: '0 */1 * * * ?'
    fault: '0 0 5 * * ?'

#第三方系统信息
yb:
  appid: 706e60176b326035c658dfb3b00627c6
  appkey: b67701ddcfbab49ba1df7f5244df07b8
  accessKey: qpsmbz7032n3m59swj
  secretKey: oqm292nr7czma01h43bskld86cnsm30d
  tokenUrl: http://**************:8766/eop/qry/api/intf/authorization
  alarmDetailUrl: http://**************:8766/eop/alarmInfoQry/api/intf/access
  alarmDetailOrder: FAULTID
  expireTime: 16


# 临时配置的地方 后续需要整理
kg:
  TgName: ACL
  FullCateName: 风险操作
  #  rsName: 'ip route-static,source ip->route-static,route-static->destination ip,route-static->next-hop,route-static->vpn-instance,route-static->interface'
  rsName: 'route-statics->route-static'
  ipIns: 'ip-address'
  ipEdgeOne: 'ip-address rank from'
  ipEdgeTwo: 'ip-address rank to'
  total: 200
rawCase:
  repeatRate: 0.7
es:
  total: 10

#  文件预览的相关路径
file:
  codeUrl: https://**************:30443/preview/getFileCode
  previewUrl: https://**************:30443
  contiUri: https://**************:32703/portal-gateway/kg-system-web/sys/api/contingencyPlan/filePreview

#知识图谱配置
know:
  graph:
    #页面展示的 数据大小
    showNum: 200

# 数字员工连接代码
sz:
  # 基础地址
  base-url: http://**************:30179
  # 员工资源管理-资源绑定
  bind-api: /api/staff/resource/bind
  # 员工资源管理-资源List获取
  resource-list-api: /api/staff/resource/getResourceList
  # 员工资源管理-资源解绑
  unbind-api: /api/staff/resource/removeBind

# 区域修改
region:
  default-region: 1000000
  region-schema: diswb

# 知识库数据统计配置（前端服务配置）

# rag上传文件通用配置 案例库以及知识库
rag:
  uploadFileUrl: http://**************:17001/batchUploadFile
  enabled: false


# 集团的ftp 配置
jtctdfs:
  host: **************
  port: 21
  userName: ffcs_zstp
  passWord: ENC(D6gH7fTM6A7OoZdTRYNkLlP1ZKtRxP0L)
  baseUrl: /ftp/知识图谱_ffcs_zstp/file
  type: sftp
  restPort: 8995
  restPassword: ENC(CYlXi9ZmmqY3cpWlIiMF5AtB+AlvDoWb)
  timeDiff: 2000

# 知识库模块统一配置
kbs:
  # 知识图谱索引配置
  base-info:
    # 索引信息
    index-name: knowledge_bases_index
  # 集团相关配置
  jt:
    # 文件上传
    batchSaveFilesUrl: http://************:8000/serviceAgent/rest/wsc/batchSaveFiles
    # 上下线及删除
    updDocUrl: http://************:8000/serviceAgent/rest/wsc/updDoc
    # 3c信息查询
    get3CTreeUrl: http://************:8000/serviceAgent/rest/wsc/get3CTree
    enabled: true
    regionCode: 351000000000000000000001
    baseFilePath: /knowledge/sftp/fujian/
    eopFilePath: /yyzz/fujian/fj/zstp/
    jtAuthor: 71081051@FJ
  # RAG Web相关配置  ragflow 和 ragflow 最好只开启一个
  ragweb:
    url: http://**************:17001
    enabled: false
  # RAG Flow相关配置
  ragflow:
    enabled: false
    api:
      url: http://192.168.35.84:30880/api/v1/datasets
      key: ragflow-M4OWQ1MmQwMTUyNDExZjA4NGRhZmE2ZG
  # 问答对文档相关配置
  qwd:
    # 默认的分组
    categoryId: 53
  # 相似度检查配置
  similarity:
    # 是否启用查重
    check: false

#根因自证用的是这个es
elasticsearch:
  server:
    clustername: oss-graph
    httpaddr: ${spring.elasticsearch.rest.uris}
    password: ${spring.elasticsearch.rest.password}
    protocol: http
    tcpaddr:
    url: ${spring.elasticsearch.rest.uris}
    username: ${spring.elasticsearch.rest.username}
    size: 10000
    maxConnTotal: 100
    maxConnPerRoute: 100
    scrollTime: 3m
    scrollSize: 1000
    version: 8.x

fault:
  graph:
    full-category-name: 故障图谱分类

#知识空间ragflow配置
ragflow:
  base-url: "http://192.168.35.84:30882"
  rerank-url: "http://**************:8000"
  api-key: "ragflow-QwMjNiYTQyNGQ4ODExZjBhMjM3ZWU4OW"
  connect-timeout: 10000
  read-timeout: 30000
  write-timeout: 30000
  polling-interval: 2000
  max-polling-times: 60