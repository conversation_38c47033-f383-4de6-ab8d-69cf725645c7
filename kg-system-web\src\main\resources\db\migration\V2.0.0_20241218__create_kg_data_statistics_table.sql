-- 创建知识库数据统计表的序列
CREATE SEQUENCE IF NOT EXISTS "kg"."kg_data_statistics_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- 创建知识库数据统计表
CREATE TABLE IF NOT EXISTS "kg"."kg_data_statistics_d" (
  "kg_data_statistics_id" numeric(18,0) NOT NULL DEFAULT nextval('"kg".kg_data_statistics_id_seq'::regclass),
  "statistics_date" date NOT NULL,
  "region_id" numeric(18,0) NOT NULL,
  "region_name" varchar(255) COLLATE "pg_catalog"."default",
  "full_region_path" varchar(500) COLLATE "pg_catalog"."default",
  "region_level" int4,
  "document_total_size" numeric(20,0) DEFAULT 0,
  "document_count" numeric(18,0) DEFAULT 0,
  "qa_pair_count" numeric(18,0) DEFAULT 0,
  "professional_terms_count" numeric(18,0) DEFAULT 0,
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "is_deleted" varchar(2) COLLATE "pg_catalog"."default" DEFAULT '0',
  "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "create_user" varchar(100) COLLATE "pg_catalog"."default" DEFAULT 'SYSTEM',
  "update_user" varchar(100) COLLATE "pg_catalog"."default" DEFAULT 'SYSTEM',
  CONSTRAINT "kg_data_statistics_d_pkey" PRIMARY KEY ("kg_data_statistics_id")
);

-- 添加表注释
COMMENT ON TABLE "kg"."kg_data_statistics_d" IS '知识库数据统计表，用于记录每日各区县的文档大小、问答对数量、专业词汇数量等统计数据';

-- 添加字段注释
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."kg_data_statistics_id" IS '主键ID';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."statistics_date" IS '统计日期';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."region_id" IS '区域ID，关联cs_sm_region_d表';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."region_name" IS '区域名称（冗余字段，便于查询）';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."full_region_path" IS '区域全路径（福建省/福州市/鼓楼区）';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."region_level" IS '区域级别（1:省 2:市 3:区县）';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."document_total_size" IS '文档总大小（字节）';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."document_count" IS '文档数量';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."qa_pair_count" IS '问答对数量';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."professional_terms_count" IS '专业词汇数量';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."remark" IS '备注';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."is_deleted" IS '是否删除 0:未删除 1:已删除';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."create_time" IS '创建时间';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."update_time" IS '更新时间';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."create_user" IS '创建用户';
COMMENT ON COLUMN "kg"."kg_data_statistics_d"."update_user" IS '更新用户';

-- 创建索引
-- 日期范围查询索引
CREATE INDEX IF NOT EXISTS "idx_kg_data_statistics_date" ON "kg"."kg_data_statistics_d" ("statistics_date");

-- 区域查询索引
CREATE INDEX IF NOT EXISTS "idx_kg_data_statistics_region" ON "kg"."kg_data_statistics_d" ("region_id");

-- 日期和区域联合查询索引
CREATE INDEX IF NOT EXISTS "idx_kg_data_statistics_date_region" ON "kg"."kg_data_statistics_d" ("statistics_date", "region_id");

-- 删除标记索引
CREATE INDEX IF NOT EXISTS "idx_kg_data_statistics_deleted" ON "kg"."kg_data_statistics_d" ("is_deleted");

-- 区域级别索引
CREATE INDEX IF NOT EXISTS "idx_kg_data_statistics_region_level" ON "kg"."kg_data_statistics_d" ("region_level");

-- 复合索引：用于按日期范围和区域查询未删除的记录
CREATE INDEX IF NOT EXISTS "idx_kg_data_statistics_query" ON "kg"."kg_data_statistics_d" ("statistics_date", "region_id", "is_deleted");

-- 在数据库序列和表成功创建后进行权限设置（如果需要）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "kg"."kg_data_statistics_d" TO [your_app_user];
-- GRANT USAGE, SELECT ON SEQUENCE "kg"."kg_data_statistics_id_seq" TO [your_app_user]; 