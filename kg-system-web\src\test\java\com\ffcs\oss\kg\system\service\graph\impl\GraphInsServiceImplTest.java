package com.ffcs.oss.kg.system.service.graph.impl;

import com.ffcs.oss.kg.data.model.evt.graph.CommonInsEvt;
import com.ffcs.oss.kg.data.model.vo.graph.TempEntityInstanceVO;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * GraphInsServiceImpl测试类
 * 用于验证pageTempEntity方法修复后的效果
 */
@SpringBootTest
@ActiveProfiles("test")
public class GraphInsServiceImplTest {

    @Resource
    private GraphInsServiceImpl graphInsService;

    /**
     * 测试pageTempEntity方法的total数量和实际条数一致性
     * 使用与问题描述相同的入参进行测试
     */
    @Test
    public void testPageTempEntityConsistency() {
        // 构建测试参数，与问题描述中的入参一致
        CommonInsEvt evt = new CommonInsEvt();
        evt.setPageNo(1);
        evt.setPageSize(20);
        evt.setTgId(11640673L);
        evt.setEntityName(Arrays.asList("局站"));
        evt.setKeyword(null);

        // 执行查询
        PageInfo<TempEntityInstanceVO> result = graphInsService.pageTempEntity(evt);

        // 验证结果
        System.out.println("=== 测试结果 ===");
        System.out.println("Total: " + result.getTotal());
        System.out.println("实际返回条数: " + result.getList().size());
        System.out.println("PageNum: " + result.getPageNum());
        System.out.println("PageSize: " + result.getPageSize());
        System.out.println("Pages: " + result.getPages());

        // 打印返回的数据
        if (!result.getList().isEmpty()) {
            System.out.println("=== 返回数据示例 ===");
            TempEntityInstanceVO firstItem = result.getList().get(0);
            System.out.println("TempId: " + firstItem.getTempId());
            System.out.println("Name: " + firstItem.getName());
            System.out.println("DisplayName: " + firstItem.getDisplayName());
            System.out.println("EntityName: " + firstItem.getEntityName());
            System.out.println("ModelCode: " + firstItem.getModelCode());
        }

        // 断言：total数量应该与实际返回的条数逻辑一致
        // 如果是第一页且返回条数小于pageSize，则total应该等于返回条数
        if (result.getPageNum() == 1 && result.getList().size() < evt.getPageSize()) {
            assert result.getTotal() == result.getList().size() : 
                "Total数量(" + result.getTotal() + ")与实际条数(" + result.getList().size() + ")不一致";
        }

        System.out.println("=== 测试通过：total数量与实际条数一致 ===");
    }

    /**
     * 测试不限定实体类型的情况
     */
    @Test
    public void testPageTempEntityWithUnlimitedType() {
        CommonInsEvt evt = new CommonInsEvt();
        evt.setPageNo(1);
        evt.setPageSize(20);
        evt.setTgId(11640673L);
        evt.setEntityName(Arrays.asList("不限定实体类型"));
        evt.setKeyword(null);

        PageInfo<TempEntityInstanceVO> result = graphInsService.pageTempEntity(evt);

        System.out.println("=== 不限定实体类型测试结果 ===");
        System.out.println("Total: " + result.getTotal());
        System.out.println("实际返回条数: " + result.getList().size());

        // 不限定实体类型时，应该返回所有符合其他条件的记录
        assert result.getTotal() >= 0 : "Total数量应该大于等于0";
    }

    /**
     * 测试关键字搜索功能
     */
    @Test
    public void testPageTempEntityWithKeyword() {
        CommonInsEvt evt = new CommonInsEvt();
        evt.setPageNo(1);
        evt.setPageSize(20);
        evt.setTgId(11640673L);
        evt.setEntityName(Arrays.asList("局站"));
        evt.setKeyword("马尾");

        PageInfo<TempEntityInstanceVO> result = graphInsService.pageTempEntity(evt);

        System.out.println("=== 关键字搜索测试结果 ===");
        System.out.println("Total: " + result.getTotal());
        System.out.println("实际返回条数: " + result.getList().size());

        // 验证返回的记录是否包含关键字
        for (TempEntityInstanceVO item : result.getList()) {
            boolean containsKeyword = (item.getName() != null && item.getName().contains("马尾")) ||
                                    (item.getDisplayName() != null && item.getDisplayName().contains("马尾"));
            assert containsKeyword : "返回的记录应该包含关键字'马尾'";
        }
    }
}
