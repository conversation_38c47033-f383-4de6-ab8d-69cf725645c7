# AI赋能开发培训指南

## 🎯 培训目标

欢迎来到AI赋能开发的世界！本指南将帮助你从零开始，学会使用AI工具来**大幅提升开发效率**。

**你将学到：**
- ✅ 如何配置和使用Cursor IDE
- ✅ 编写高效的提示词技巧
- ✅ 使用@符号快速调用功能
- ✅ 从需求到代码的完整开发流程
- ✅ AI辅助测试和调试方法

**效果预览：**
- 📈 开发效率提升 **80%以上**
- ⏰ 学习新项目时间从 **几天缩短到几小时**
- 🎯 代码质量显著提升，规范统一

---

## 第一部分：环境准备（15分钟）

### 1.1 下载和安装Cursor

#### 步骤1：下载Cursor
1. 访问官网：https://cursor.sh/
2. 选择对应系统版本下载
3. 按照提示完成安装

#### 步骤2：基本设置
1. 打开Cursor
2. 选择主题和字体（推荐Dark主题）
3. 设置中文界面（可选）

### 1.2 配置AI功能

#### 步骤1：设置API密钥
```
1. 点击右上角设置按钮 ⚙️
2. 选择 "Models" 
3. 输入你的API密钥
4. 选择Claude-3.7-Sonnet模型（推荐）
```

#### 步骤2：开启长期对话
```
1. 在设置中找到 "Chat"
2. 开启 "Long context chat"
3. 设置对话记忆长度为最大值
```

### 1.3 安装必要插件

推荐插件列表：
- **Chinese Language Pack**：中文支持
- **GitLens**：Git增强
- **Auto Rename Tag**：标签重命名
- **Bracket Pair Colorizer**：括号着色

---

## 第二部分：基础使用技巧（20分钟）

### 2.1 认识Cursor界面

```
┌─────────────────────────────────────────────────────────┐
│  📁 文件浏览器  │        📝 代码编辑器        │  🤖 AI聊天  │
│                │                            │            │
│  project/      │  // 你的代码在这里           │  问题和    │
│  ├── src/      │  public class Demo {       │  回答显示  │
│  ├── config/   │    // AI帮你写代码           │  在这里    │
│  └── docs/     │  }                         │            │
└─────────────────────────────────────────────────────────┘
```

### 2.2 快捷键大全

| 功能 | 快捷键 | 说明 |
|-----|--------|------|
| 打开AI聊天 | `Ctrl+L` | 最常用！ |
| 智能补全 | `Tab` | 接受AI建议 |
| 拒绝建议 | `Esc` | 不要AI的建议 |
| 选择代码+AI | `Ctrl+K` | 对选中代码提问 |
| 文件搜索 | `Ctrl+P` | 快速找文件 |

### 2.3 神奇的@符号使用

#### @符号可以调用特定功能：

**📁 调用文件：**
```
@文件名.java  # 让AI看到特定文件内容
@/src/main/  # 让AI看到整个目录
```

**🔍 搜索代码：**
```
@codebase 搜索相关代码
@web 搜索网上资料
@docs 查看文档
```

**实际例子：**
```
用户输入：@UserService.java 这个文件的用户注册方法有什么问题？
AI回答：我来分析UserService.java中的用户注册方法...
```

---

## 第三部分：项目规则配置（15分钟）

### 3.1 为什么需要规则？

**没有规则的后果：**
- AI生成的代码风格不统一 😵
- 不符合团队开发规范 😵
- 需要反复修改 😵

**有了规则的好处：**
- 代码风格完全统一 ✅
- 符合项目规范 ✅
- 一次生成就能用 ✅

### 3.2 创建项目规则文件

#### 步骤1：创建.cursorrules文件
在项目根目录创建文件：`.cursorrules`

#### 步骤2：编写项目规则
```bash
# 知识图谱项目开发规则

## 项目基本信息
- 项目名称：知识图谱管理系统
- 技术栈：Java 8 + Spring Boot 2.4.2 + PostgreSQL + MyBatis-Plus
- 包名前缀：com.ffcs.oss.kg

## 代码规范

### Java类命名规范
- Entity类：以Entity结尾，如 UserEntity
- Service接口：以Service结尾，如 UserService  
- Service实现：以ServiceImpl结尾，如 UserServiceImpl
- Controller类：以Controller结尾，如 UserController
- Mapper接口：以Mapper结尾，如 UserMapper

### 方法命名规范
- 查询方法：select、find、get开头
- 新增方法：save、add、insert开头
- 修改方法：update、modify开头
- 删除方法：delete、remove开头

### 注解使用规范
- 类必须添加：@Service、@RestController等
- 方法必须添加：@Override、@Transactional等
- 字段必须添加：@ApiModelProperty、@TableField等

### 返回值规范
- Controller统一返回：ServiceResp<T>
- Service层返回：具体业务对象或List
- 分页查询返回：IPage<T>

### 异常处理规范
- 业务异常：抛出BusinessException
- 参数校验：使用@Valid注解
- 统一异常处理：GlobalExceptionHandler

## 数据库规范

### 表命名规范
- 表名格式：模块_功能_类型，如 kg_user_d
- 类型后缀：_d(数据表)、_c(配置表)、_r(关系表)

### 字段命名规范
- 主键：表名去后缀_id，如 kg_user_id
- 创建时间：created_time
- 更新时间：updated_time
- 创建人：created_user_name
- 逻辑删除：is_deleted

## 代码生成要求

### 生成Java代码时：
1. 必须包含完整的包声明和导入
2. 必须添加类注释和方法注释
3. 必须使用项目统一的注解规范
4. 必须处理异常情况
5. 必须包含日志记录

### 生成SQL时：
1. 使用PostgreSQL语法
2. 字段名使用下划线命名
3. 必须包含注释说明
4. 考虑索引优化

请严格按照以上规则生成代码，确保代码质量和一致性。
```

### 3.3 规则文件的威力

**有了规则文件后，你只需要说：**
```
"帮我生成一个用户管理的完整代码"
```

**AI就会自动：**
- ✅ 按照你的包名结构
- ✅ 使用你定义的命名规范
- ✅ 添加你要求的注解
- ✅ 符合你的返回值格式
- ✅ 包含你需要的异常处理

---

## 第四部分：实战演练 - 从需求到代码（30分钟）

### 4.1 需求场景

**业务需求：**
> 我们需要一个"文章分类管理"功能，包括：
> 1. 分类的增删改查
> 2. 支持分页查询
> 3. 支持按分类名称搜索
> 4. 分类可以有层级关系（父子分类）

### 4.2 第一步：需求分析和表设计

#### 与AI对话：
```
我需要设计一个文章分类管理功能，请帮我：
1. 分析需求并设计数据库表结构
2. 考虑层级关系的实现方案
3. 提供建表SQL

需求：分类增删改查、分页查询、名称搜索、支持父子分类
```

#### AI会帮你：
1. 📋 梳理需求要点
2. 🗄️ 设计表结构
3. 📝 生成建表SQL
4. 💡 给出技术建议

**AI回复示例：**
```sql
-- 文章分类表
CREATE TABLE kg_article_category_d (
    kg_article_category_id BIGSERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) UNIQUE COMMENT '分类编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父分类ID，0表示顶级分类',
    category_level INTEGER DEFAULT 1 COMMENT '分类层级',
    sort_order INTEGER DEFAULT 0 COMMENT '排序号',
    category_desc TEXT COMMENT '分类描述',
    is_enabled CHAR(1) DEFAULT '1' COMMENT '是否启用',
    created_user_name VARCHAR(50) COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted CHAR(1) DEFAULT '0' COMMENT '逻辑删除'
);

-- 创建索引
CREATE INDEX idx_category_parent ON kg_article_category_d(parent_id);
CREATE INDEX idx_category_name ON kg_article_category_d(category_name);
```

### 4.3 第二步：生成实体类

#### 与AI对话：
```
@.cursorrules 根据上面的表结构，生成对应的Java实体类
```

#### AI自动生成：
```java
package com.ffcs.oss.kg.data.rd.entity.article;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文章分类实体类
 * 
 * <AUTHOR> Generated
 * @since 2024-01-20
 */
@Data
@TableName("kg_article_category_d")
@ApiModel("文章分类")
public class ArticleCategoryEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分类ID")
    @TableId(value = "kg_article_category_id", type = IdType.AUTO)
    private Long kgArticleCategoryId;

    @ApiModelProperty("分类名称")
    @TableField("category_name")
    private String categoryName;

    @ApiModelProperty("分类编码")
    @TableField("category_code")
    private String categoryCode;

    @ApiModelProperty("父分类ID")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty("分类层级")
    @TableField("category_level")
    private Integer categoryLevel;

    @ApiModelProperty("排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty("分类描述")
    @TableField("category_desc")
    private String categoryDesc;

    @ApiModelProperty("是否启用")
    @TableField("is_enabled")
    private String isEnabled;

    @ApiModelProperty("创建人")
    @TableField("created_user_name")
    private String createdUserName;

    @ApiModelProperty("创建时间")
    @TableField("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    @ApiModelProperty("更新时间")
    @TableField("updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    @ApiModelProperty("是否删除")
    @TableField("is_deleted")
    @TableLogic
    private String isDeleted;
}
```

### 4.4 第三步：生成Mapper接口

#### 与AI对话：
```
生成对应的Mapper接口，包含：
1. 基础CRUD操作
2. 根据父ID查询子分类
3. 按名称模糊搜索
4. 树形结构查询
```

#### AI自动生成：
```java
package com.ffcs.oss.kg.data.rd.mapper.article;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffcs.oss.kg.data.rd.entity.article.ArticleCategoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文章分类 Mapper 接口
 *
 * <AUTHOR> Generated
 * @since 2024-01-20
 */
@Mapper
public interface ArticleCategoryMapper extends BaseMapper<ArticleCategoryEntity> {

    /**
     * 分页查询分类列表
     */
    IPage<ArticleCategoryEntity> selectCategoryPage(
            Page<ArticleCategoryEntity> page,
            @Param("categoryName") String categoryName,
            @Param("parentId") Long parentId,
            @Param("isEnabled") String isEnabled
    );

    /**
     * 根据父ID查询子分类
     */
    List<ArticleCategoryEntity> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询分类树形结构
     */
    List<ArticleCategoryEntity> selectCategoryTree();

    /**
     * 根据名称模糊搜索
     */
    List<ArticleCategoryEntity> selectByNameLike(@Param("categoryName") String categoryName);

    /**
     * 检查分类编码是否存在
     */
    int countByCategoryCode(@Param("categoryCode") String categoryCode, @Param("excludeId") Long excludeId);
}
```

### 4.5 第四步：生成Service层

#### 与AI对话：
```
生成Service接口和实现类，包含：
1. 完整的CRUD操作
2. 树形结构处理
3. 分页查询
4. 业务校验（编码重复检查等）
5. 事务处理
```

#### AI会生成两个文件：

**ArticleCategoryService.java (接口)**
```java
package com.ffcs.oss.kg.biz.service.article;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ffcs.oss.kg.data.rd.entity.article.ArticleCategoryEntity;

import java.util.List;

/**
 * 文章分类服务接口
 */
public interface ArticleCategoryService {
    
    /**
     * 新增分类
     */
    Boolean saveCategory(ArticleCategoryEntity category);
    
    /**
     * 更新分类
     */
    Boolean updateCategory(ArticleCategoryEntity category);
    
    /**
     * 删除分类
     */
    Boolean deleteCategory(Long categoryId);
    
    /**
     * 根据ID查询
     */
    ArticleCategoryEntity getCategoryById(Long categoryId);
    
    /**
     * 分页查询
     */
    IPage<ArticleCategoryEntity> getCategoryPage(int current, int size, String categoryName, Long parentId);
    
    /**
     * 查询分类树
     */
    List<ArticleCategoryEntity> getCategoryTree();
}
```

**ArticleCategoryServiceImpl.java (实现类)**
```java
package com.ffcs.oss.kg.biz.service.article.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.kg.biz.service.article.ArticleCategoryService;
import com.ffcs.oss.kg.common.core.exception.BusinessException;
import com.ffcs.oss.kg.data.rd.entity.article.ArticleCategoryEntity;
import com.ffcs.oss.kg.data.rd.mapper.article.ArticleCategoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文章分类服务实现类
 */
@Slf4j
@Service
public class ArticleCategoryServiceImpl extends ServiceImpl<ArticleCategoryMapper, ArticleCategoryEntity> 
        implements ArticleCategoryService {

    @Autowired
    private ArticleCategoryMapper categoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCategory(ArticleCategoryEntity category) {
        log.info("开始新增文章分类，分类名称：{}", category.getCategoryName());
        
        try {
            // 校验分类编码是否重复
            if (StringUtils.isNotBlank(category.getCategoryCode())) {
                int count = categoryMapper.countByCategoryCode(category.getCategoryCode(), null);
                if (count > 0) {
                    throw new BusinessException("分类编码已存在");
                }
            }
            
            // 设置创建时间
            category.setCreatedTime(LocalDateTime.now());
            category.setUpdatedTime(LocalDateTime.now());
            
            // 如果有父分类，设置层级
            if (category.getParentId() != null && category.getParentId() > 0) {
                ArticleCategoryEntity parent = this.getById(category.getParentId());
                if (parent != null) {
                    category.setCategoryLevel(parent.getCategoryLevel() + 1);
                }
            } else {
                category.setParentId(0L);
                category.setCategoryLevel(1);
            }
            
            boolean result = this.save(category);
            log.info("新增文章分类完成，结果：{}", result);
            return result;
            
        } catch (Exception e) {
            log.error("新增文章分类异常", e);
            throw new BusinessException("新增分类失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCategory(ArticleCategoryEntity category) {
        log.info("开始更新文章分类，分类ID：{}", category.getKgArticleCategoryId());
        
        try {
            // 校验分类是否存在
            ArticleCategoryEntity existing = this.getById(category.getKgArticleCategoryId());
            if (existing == null) {
                throw new BusinessException("分类不存在");
            }
            
            // 校验分类编码是否重复
            if (StringUtils.isNotBlank(category.getCategoryCode())) {
                int count = categoryMapper.countByCategoryCode(category.getCategoryCode(), category.getKgArticleCategoryId());
                if (count > 0) {
                    throw new BusinessException("分类编码已存在");
                }
            }
            
            // 设置更新时间
            category.setUpdatedTime(LocalDateTime.now());
            
            boolean result = this.updateById(category);
            log.info("更新文章分类完成，结果：{}", result);
            return result;
            
        } catch (Exception e) {
            log.error("更新文章分类异常", e);
            throw new BusinessException("更新分类失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCategory(Long categoryId) {
        log.info("开始删除文章分类，分类ID：{}", categoryId);
        
        try {
            // 检查是否有子分类
            List<ArticleCategoryEntity> children = categoryMapper.selectByParentId(categoryId);
            if (!children.isEmpty()) {
                throw new BusinessException("该分类下存在子分类，无法删除");
            }
            
            // 逻辑删除
            boolean result = this.removeById(categoryId);
            log.info("删除文章分类完成，结果：{}", result);
            return result;
            
        } catch (Exception e) {
            log.error("删除文章分类异常", e);
            throw new BusinessException("删除分类失败：" + e.getMessage());
        }
    }

    @Override
    public ArticleCategoryEntity getCategoryById(Long categoryId) {
        log.info("查询文章分类，分类ID：{}", categoryId);
        return this.getById(categoryId);
    }

    @Override
    public IPage<ArticleCategoryEntity> getCategoryPage(int current, int size, String categoryName, Long parentId) {
        log.info("分页查询文章分类，页码：{}，大小：{}，名称：{}，父ID：{}", current, size, categoryName, parentId);
        
        Page<ArticleCategoryEntity> page = new Page<>(current, size);
        return categoryMapper.selectCategoryPage(page, categoryName, parentId, "1");
    }

    @Override
    public List<ArticleCategoryEntity> getCategoryTree() {
        log.info("查询文章分类树形结构");
        return categoryMapper.selectCategoryTree();
    }
}
```

### 4.6 第五步：生成Controller层

#### 与AI对话：
```
生成Controller类，包含：
1. RESTful风格的API接口
2. 参数校验
3. 统一返回格式
4. 完整的接口文档注解
5. 异常处理
```

#### AI自动生成：
```java
package com.ffcs.oss.kg.system.controller.article;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ffcs.oss.kg.biz.service.article.ArticleCategoryService;
import com.ffcs.oss.kg.common.core.mvc.ServiceResp;
import com.ffcs.oss.kg.data.rd.entity.article.ArticleCategoryEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文章分类管理控制器
 */
@Slf4j
@Api(tags = "文章分类管理")
@RestController
@RequestMapping("/api/article/category")
@Validated
public class ArticleCategoryController {

    @Autowired
    private ArticleCategoryService categoryService;

    @ApiOperation("新增文章分类")
    @PostMapping("/save")
    public ServiceResp<Boolean> saveCategory(@Valid @RequestBody ArticleCategoryEntity category) {
        log.info("接收新增文章分类请求：{}", category.getCategoryName());
        
        try {
            Boolean result = categoryService.saveCategory(category);
            return ServiceResp.success("新增成功", result);
        } catch (Exception e) {
            log.error("新增文章分类失败", e);
            return ServiceResp.fail("新增失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新文章分类")
    @PostMapping("/update")
    public ServiceResp<Boolean> updateCategory(@Valid @RequestBody ArticleCategoryEntity category) {
        log.info("接收更新文章分类请求，ID：{}", category.getKgArticleCategoryId());
        
        try {
            Boolean result = categoryService.updateCategory(category);
            return ServiceResp.success("更新成功", result);
        } catch (Exception e) {
            log.error("更新文章分类失败", e);
            return ServiceResp.fail("更新失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除文章分类")
    @PostMapping("/delete")
    public ServiceResp<Boolean> deleteCategory(
            @ApiParam("分类ID") @RequestParam @NotNull Long categoryId) {
        log.info("接收删除文章分类请求，ID：{}", categoryId);
        
        try {
            Boolean result = categoryService.deleteCategory(categoryId);
            return ServiceResp.success("删除成功", result);
        } catch (Exception e) {
            log.error("删除文章分类失败", e);
            return ServiceResp.fail("删除失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据ID查询分类")
    @GetMapping("/detail")
    public ServiceResp<ArticleCategoryEntity> getCategoryById(
            @ApiParam("分类ID") @RequestParam @NotNull Long categoryId) {
        log.info("接收查询文章分类请求，ID：{}", categoryId);
        
        try {
            ArticleCategoryEntity result = categoryService.getCategoryById(categoryId);
            return ServiceResp.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询文章分类失败", e);
            return ServiceResp.fail("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation("分页查询分类列表")
    @GetMapping("/page")
    public ServiceResp<IPage<ArticleCategoryEntity>> getCategoryPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") int current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") int size,
            @ApiParam("分类名称") @RequestParam(required = false) String categoryName,
            @ApiParam("父分类ID") @RequestParam(required = false) Long parentId) {
        log.info("接收分页查询文章分类请求，页码：{}，大小：{}，名称：{}", current, size, categoryName);
        
        try {
            IPage<ArticleCategoryEntity> result = categoryService.getCategoryPage(current, size, categoryName, parentId);
            return ServiceResp.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询文章分类失败", e);
            return ServiceResp.fail("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation("查询分类树形结构")
    @GetMapping("/tree")
    public ServiceResp<List<ArticleCategoryEntity>> getCategoryTree() {
        log.info("接收查询分类树形结构请求");
        
        try {
            List<ArticleCategoryEntity> result = categoryService.getCategoryTree();
            return ServiceResp.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询分类树形结构失败", e);
            return ServiceResp.fail("查询失败：" + e.getMessage());
        }
    }
}
```

### 4.7 第六步：生成Mapper.xml文件

#### 与AI对话：
```
生成对应的MyBatis XML映射文件，包含复杂查询的SQL实现
```

#### AI自动生成：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ffcs.oss.kg.data.rd.mapper.article.ArticleCategoryMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.ffcs.oss.kg.data.rd.entity.article.ArticleCategoryEntity">
        <id column="kg_article_category_id" property="kgArticleCategoryId" />
        <result column="category_name" property="categoryName" />
        <result column="category_code" property="categoryCode" />
        <result column="parent_id" property="parentId" />
        <result column="category_level" property="categoryLevel" />
        <result column="sort_order" property="sortOrder" />
        <result column="category_desc" property="categoryDesc" />
        <result column="is_enabled" property="isEnabled" />
        <result column="created_user_name" property="createdUserName" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 分页查询分类列表 -->
    <select id="selectCategoryPage" resultMap="BaseResultMap">
        SELECT 
            kg_article_category_id,
            category_name,
            category_code,
            parent_id,
            category_level,
            sort_order,
            category_desc,
            is_enabled,
            created_user_name,
            created_time,
            updated_time
        FROM kg_article_category_d 
        WHERE is_deleted = '0'
        <if test="isEnabled != null and isEnabled != ''">
            AND is_enabled = #{isEnabled}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND category_name LIKE CONCAT('%', #{categoryName}, '%')
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        ORDER BY category_level ASC, sort_order ASC, created_time DESC
    </select>

    <!-- 根据父ID查询子分类 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT 
            kg_article_category_id,
            category_name,
            category_code,
            parent_id,
            category_level,
            sort_order,
            category_desc,
            is_enabled,
            created_user_name,
            created_time,
            updated_time
        FROM kg_article_category_d 
        WHERE is_deleted = '0' 
          AND parent_id = #{parentId}
          AND is_enabled = '1'
        ORDER BY sort_order ASC, created_time ASC
    </select>

    <!-- 查询分类树形结构 -->
    <select id="selectCategoryTree" resultMap="BaseResultMap">
        SELECT 
            kg_article_category_id,
            category_name,
            category_code,
            parent_id,
            category_level,
            sort_order,
            category_desc,
            is_enabled,
            created_user_name,
            created_time,
            updated_time
        FROM kg_article_category_d 
        WHERE is_deleted = '0' 
          AND is_enabled = '1'
        ORDER BY category_level ASC, sort_order ASC, created_time ASC
    </select>

    <!-- 根据名称模糊搜索 -->
    <select id="selectByNameLike" resultMap="BaseResultMap">
        SELECT 
            kg_article_category_id,
            category_name,
            category_code,
            parent_id,
            category_level,
            sort_order,
            category_desc,
            is_enabled,
            created_user_name,
            created_time,
            updated_time
        FROM kg_article_category_d 
        WHERE is_deleted = '0' 
          AND is_enabled = '1'
          AND category_name LIKE CONCAT('%', #{categoryName}, '%')
        ORDER BY category_level ASC, sort_order ASC
    </select>

    <!-- 检查分类编码是否存在 -->
    <select id="countByCategoryCode" resultType="int">
        SELECT COUNT(1) 
        FROM kg_article_category_d 
        WHERE is_deleted = '0' 
          AND category_code = #{categoryCode}
        <if test="excludeId != null">
            AND kg_article_category_id != #{excludeId}
        </if>
    </select>

</mapper>
```

---

## 第五部分：AI辅助测试和调试（20分钟）

### 5.1 自动生成测试用例

#### 与AI对话：
```
为ArticleCategoryService生成完整的单元测试代码，包括：
1. 正常情况测试
2. 异常情况测试  
3. 边界条件测试
4. Mock对象设置
```

#### AI自动生成测试类：
```java
package com.ffcs.oss.kg.biz.service.article.impl;

import com.ffcs.oss.kg.biz.service.article.ArticleCategoryService;
import com.ffcs.oss.kg.common.core.exception.BusinessException;
import com.ffcs.oss.kg.data.rd.entity.article.ArticleCategoryEntity;
import com.ffcs.oss.kg.data.rd.mapper.article.ArticleCategoryMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 文章分类服务测试类
 */
@ExtendWith(MockitoExtension.class)
class ArticleCategoryServiceImplTest {

    @Mock
    private ArticleCategoryMapper categoryMapper;

    @InjectMocks
    private ArticleCategoryServiceImpl categoryService;

    private ArticleCategoryEntity testCategory;

    @BeforeEach
    void setUp() {
        testCategory = new ArticleCategoryEntity();
        testCategory.setKgArticleCategoryId(1L);
        testCategory.setCategoryName("测试分类");
        testCategory.setCategoryCode("TEST001");
        testCategory.setParentId(0L);
        testCategory.setCategoryLevel(1);
        testCategory.setSortOrder(1);
        testCategory.setIsEnabled("1");
        testCategory.setCreatedTime(LocalDateTime.now());
    }

    @Test
    void testSaveCategory_Success() {
        // 准备测试数据
        when(categoryMapper.countByCategoryCode(any(), any())).thenReturn(0);
        when(categoryMapper.insert(any())).thenReturn(1);

        // 执行测试
        Boolean result = categoryService.saveCategory(testCategory);

        // 验证结果
        assertTrue(result);
        verify(categoryMapper).countByCategoryCode(testCategory.getCategoryCode(), null);
        verify(categoryMapper).insert(testCategory);
    }

    @Test
    void testSaveCategory_DuplicateCode() {
        // 准备测试数据
        when(categoryMapper.countByCategoryCode(any(), any())).thenReturn(1);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            categoryService.saveCategory(testCategory);
        });

        assertEquals("分类编码已存在", exception.getMessage());
    }

    @Test
    void testDeleteCategory_HasChildren() {
        // 准备测试数据
        List<ArticleCategoryEntity> children = new ArrayList<>();
        children.add(new ArticleCategoryEntity());
        when(categoryMapper.selectByParentId(1L)).thenReturn(children);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            categoryService.deleteCategory(1L);
        });

        assertEquals("该分类下存在子分类，无法删除", exception.getMessage());
    }

    @Test
    void testGetCategoryTree_Success() {
        // 准备测试数据
        List<ArticleCategoryEntity> categories = new ArrayList<>();
        categories.add(testCategory);
        when(categoryMapper.selectCategoryTree()).thenReturn(categories);

        // 执行测试
        List<ArticleCategoryEntity> result = categoryService.getCategoryTree();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试分类", result.get(0).getCategoryName());
    }
}
```

### 5.2 生成API测试脚本

#### 与AI对话：
```
生成Postman测试集合或者curl命令，用于测试所有的API接口
```

#### AI自动生成：
```bash
#!/bin/bash

# 文章分类管理API测试脚本
BASE_URL="http://localhost:8080/api/article/category"

echo "=== 文章分类管理API测试 ==="

# 1. 新增分类
echo "1. 测试新增分类..."
curl -X POST "$BASE_URL/save" \
  -H "Content-Type: application/json" \
  -d '{
    "categoryName": "技术文章",
    "categoryCode": "TECH",
    "parentId": 0,
    "sortOrder": 1,
    "categoryDesc": "技术相关的文章分类",
    "isEnabled": "1",
    "createdUserName": "admin"
  }'

echo -e "\n"

# 2. 查询分类列表（分页）
echo "2. 测试分页查询..."
curl -X GET "$BASE_URL/page?current=1&size=10&categoryName=技术" \
  -H "Content-Type: application/json"

echo -e "\n"

# 3. 查询分类详情
echo "3. 测试查询详情..."
curl -X GET "$BASE_URL/detail?categoryId=1" \
  -H "Content-Type: application/json"

echo -e "\n"

# 4. 查询分类树
echo "4. 测试查询分类树..."
curl -X GET "$BASE_URL/tree" \
  -H "Content-Type: application/json"

echo -e "\n"

# 5. 更新分类
echo "5. 测试更新分类..."
curl -X POST "$BASE_URL/update" \
  -H "Content-Type: application/json" \
  -d '{
    "kgArticleCategoryId": 1,
    "categoryName": "技术文章（更新）",
    "categoryCode": "TECH_UPDATE",
    "categoryDesc": "更新后的技术文章分类"
  }'

echo -e "\n"

# 6. 删除分类
echo "6. 测试删除分类..."
curl -X POST "$BASE_URL/delete?categoryId=1" \
  -H "Content-Type: application/json"

echo -e "\n"
echo "=== 测试完成 ==="
```

### 5.3 调试技巧

#### 使用AI分析错误：
**当出现错误时，复制错误信息给AI：**
```
这个错误什么意思，怎么解决？

java.lang.ClassNotFoundException: com.ffcs.oss.kg.data.rd.mapper.article.ArticleCategoryMapper
    at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
    at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
```

**AI会告诉你：**
1. 🔍 错误原因分析
2. 🛠️ 解决方案步骤
3. 🎯 预防类似问题的建议

---

## 第六部分：高级技巧（15分钟）

### 6.1 引入外部网站内容

#### 使用@web搜索最新技术：
```
@web Spring Boot 3.0 新特性有哪些？
```

#### 使用@docs查看官方文档：
```
@docs MyBatis-Plus 分页查询的最新用法
```

### 6.2 批量代码生成

#### 一次性生成多个相似模块：
```
请根据现有的文章分类模块，生成以下3个类似模块的完整代码：
1. 用户标签管理 (user_tag_d表)
2. 文档分类管理 (doc_category_d表)  
3. 知识点分类管理 (knowledge_category_d表)

要求：保持相同的代码结构和规范，只需要修改相关的字段名称。
```

### 6.3 代码重构和优化

#### 代码优化建议：
```
@现有的UserService.java 请分析这个类的代码质量，并提供优化建议：
1. 性能优化
2. 代码规范
3. 安全问题
4. 可维护性改进
```

### 6.4 项目文档生成

#### 自动生成项目文档：
```
基于当前项目的代码结构，生成以下文档：
1. API接口文档（Markdown格式）
2. 数据库设计文档
3. 部署指南
4. 开发规范文档
```

---

## 第七部分：团队协作与最佳实践（10分钟）

### 7.1 团队规范建立

#### 统一团队的.cursorrules文件：
```bash
# 团队共享规则文件
git add .cursorrules
git commit -m "添加团队AI开发规范"
git push origin main
```

#### 建立代码评审标准：
1. ✅ AI生成的代码必须经过人工审查
2. ✅ 关键业务逻辑必须添加注释说明
3. ✅ 必须包含异常处理和日志记录
4. ✅ 必须遵循项目命名规范

### 7.2 效率提升技巧

#### 建立个人提示词库：
```
# 常用提示词模板

## 代码生成模板
"根据以下需求生成完整的Java代码，包括Entity、Mapper、Service、Controller四层架构..."

## 代码审查模板  
"请审查以下代码的质量，从性能、安全、可维护性三个方面给出改进建议..."

## 文档生成模板
"根据以下代码生成详细的API文档，包括请求参数、响应格式、错误码说明..."
```

### 7.3 质量保障措施

#### 代码生成后的检查清单：
- [ ] 包名和导入是否正确
- [ ] 注解使用是否符合规范
- [ ] 异常处理是否完整
- [ ] 日志记录是否恰当
- [ ] 业务逻辑是否合理
- [ ] 单元测试是否可运行

---

## 第八部分：常见问题与解决方案（10分钟）

### 8.1 常见问题FAQ

#### Q1: AI生成的代码编译不通过怎么办？
**A1:** 
```
把编译错误信息复制给AI：
"这段代码编译报错，请帮我修复：[错误信息]"

AI会自动分析并提供修复方案。
```

#### Q2: 如何让AI理解我们项目的特定规范？
**A2:**
```
1. 完善.cursorrules文件
2. 提供现有代码示例
3. 明确指出特殊要求
4. 使用@filename让AI参考现有代码
```

#### Q3: AI生成的代码质量如何保证？
**A3:**
```
1. 制定代码审查标准
2. 建立单元测试流程
3. 使用静态代码分析工具
4. 定期团队代码review
```

### 8.2 性能优化建议

#### 提示词优化：
```
❌ 不好的提示词：
"帮我写个用户管理"

✅ 好的提示词：
"根据我们项目的开发规范(.cursorrules)，为用户管理模块生成完整的四层架构代码，包括：
1. UserEntity实体类（对应user_info_d表）
2. UserMapper接口和XML
3. UserService接口和实现类
4. UserController控制器
要求包含CRUD操作、分页查询、参数校验、异常处理、完整注释。"
```

---

## 第九部分：实际演示环节（20分钟）

### 9.1 现场演示准备

#### 演示环境检查：
1. ✅ Cursor IDE已安装并配置
2. ✅ AI功能正常可用
3. ✅ 项目环境已准备
4. ✅ 数据库连接正常

#### 演示脚本：
```
演示1：项目快速理解（5分钟）
- 展示如何用AI分析陌生项目
- 快速提取项目规范和架构

演示2：从需求到代码（10分钟）
- 现场接收一个新需求
- 完整走一遍开发流程
- 展示生成的代码质量

演示3：调试和优化（5分钟）
- 模拟一个错误场景
- 展示AI辅助调试过程
- 代码优化建议
```

### 9.2 互动环节

#### 鼓励现场提问：
```
"大家可以提出具体的开发场景，我现场用AI来解决！"

常见问题：
- 如何处理复杂的业务逻辑？
- 如何生成前端页面？
- 如何优化数据库查询？
- 如何编写测试用例？
```

---

## 总结：AI赋能开发的价值体现

### 🚀 效率提升数据
- **代码生成速度**：提升 85%
- **学习新项目时间**：从3天缩短到2小时
- **文档编写效率**：提升 90%
- **调试问题解决**：提升 70%

### 💡 核心价值
1. **解放重复劳动**：告别CRUD代码手写
2. **快速学习能力**：新技术新项目快速上手
3. **质量一致性**：统一的代码规范和结构
4. **创新时间释放**：更多时间专注业务创新

### 🎯 成功关键
1. **合理期望**：AI是助手，不是替代
2. **规范建设**：完善的项目规则和团队标准
3. **持续学习**：跟进AI工具的发展和最佳实践
4. **质量把控**：建立完善的审查和测试流程

---

## 课后练习作业

### 基础练习（必做）
1. 配置自己的Cursor开发环境
2. 为一个简单功能编写完整的.cursorrules文件
3. 使用AI生成一个完整的CRUD模块

### 进阶练习（选做）
1. 尝试使用AI优化现有代码
2. 生成单元测试和API文档
3. 探索AI在前端开发中的应用

### 团队练习（推荐）
1. 制定团队AI使用规范
2. 建立代码质量检查标准
3. 分享使用心得和技巧

---

**记住：AI是你的编程伙伴，不是魔法棒。合理使用，持续学习，让AI真正成为提升工作效率的得力助手！** 🤝✨ 

